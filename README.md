## Business Overview

The Rule Admin module enables dynamic rule creation based on various criteria and actions. This is especially useful for scenarios such as personalized promotions, conditional product discounts, or producing some actions like sending mail, creating alerts, etc. The module allows users to define criteria groups, create conditions based on those criteria, and form complex condition sets that can be applied to products or bundles.

The business flow and corresponding entity classes are:

1. **Define Criteria Groups & Criteria**
    - **Entities**:
        - `CriteriaGroup`: Logical grouping (e.g., "Customer")
        - `Criteria`: Actual evaluation points (e.g., "Age", "Market")
        - `CriteriaConfig`: Stores config for dynamic evaluation (e.g., allowed rules, mandatory config, display order)

2. **Create Conditions**
    - Conditions apply comparison operations on criteria and evaluate values.
    - A condition is composed of:
        - One or more **Criteria**
        - A **Comparison Operator** (e.g., equals, contains any, between)
        - One or more **Expected Values**
    - **Entities**:
        - `Condition`: Contains criteria, comparison operator and values
        - `ConditionNode`: Store condition and its previous and next relation

3. **Form Condition Sets**
    - A `ConditionSet` is a series of connected `ConditionNode`s that lead to an `Action`.
    - **Entities**:
        - `ConditionSet`: Defines logical sequenceof conditions with AND relation.
        - `Action`: Describes what happens when conditions are met (e.g., apply 10% discount)

4. **Group Condition Sets**
    - Multiple sets grouped into a reusable, composite rule.
    - **Entities**:
        - `ConditionSetGroup`: Groups condition sets together
        - `ConditionSetGroupTemplate`: Templates for reusable groups

5. **Define Rule**
   The `Rule` entity is the core domain object representing a complete business rule, including its logic, type, priority, effective date range, and associations with products or bundles.

    - Final business rule linking condition logic with product or bundle.
    - **Entities**:
        - `Rule`: Encapsulates full business rule logic
        - `Product`, `Bundle`: Targets of the rule

---

## Entity Relation

- `Rule` aggregates `ConditionSetGroup`
- `ConditionSetGroup` contains multiple `ConditionSet`
- `ConditionSet` has an ordered list of `Condition`
- `Condition` refers to one or more `Criteria`
- `Criteria` belongs to a `CriteriaGroup`
- `Action` is tied to each `ConditionSet`

---

## Entity-Class Mapping Summary

| Business Concept           | Domain Entity Class           |
|----------------------------|-------------------------------|
| Criteria Group             | `CriteriaGroup`               |
| Criteria                   | `Criteria`                    |
| Criteria Configuration     | `CriteriaConfig`              |
| Single Condition           | `Condition`                   |
| Ordered Condition Link     | `ConditionNode`               |
| Logical Condition Flow     | `ConditionSet`                |
| Group of Condition Sets    | `ConditionSetGroup`           |
| Condition Group Template   | `ConditionSetGroupTemplate`   |
| Action Definition          | `Action`                      |
| Product Target             | `Product`                     |
| Bundle Target              | `Bundle`                      |
| Entire Rule Definition     | `Rule`                        |

---

## Example Workflow

1. Define a `CriteriaGroup` named `Customer Attributes`
2. Add `Criteria` such as `Age`, `Region`, `MembershipStatus`
3. Start creation of a Rule with giving name, effective date etc.
4. Link with a product or bundle to a `Rule`
5. Create `Condition`: "Market" Contains All 'Europe, Asia'"
6. Create `Condition`: "Customer Age > 25"
7. Link created `Conditions` with linear line(AND).
8. Add an `Action`: “Give 10% discount”
9. Bundle these into a `ConditionSet`
10. Combine multiple `ConditionSets` into a `ConditionSetGroup`

---


# Folder Structure Details

This project is a Spring Boot application developed using Domain-Driven Design (DDD) principles. The project structure is organized according to clean architecture and DDD principles.

## Project Structure

```
├── adapter/
│   └── api.client/
├── persistence/
│   ├── mapper/
│   ├── model/
│   └── repository/
├── application.service/
├── config/
├── controller/
├── domain/
│   ├── entity/
│   ├── repository/
│   ├── service/
│   └── valueobject/
...
```

## Layers and Responsibilities

### Adapter Layer
**`adapter/api.client/`**
- Contains API clients used for connections to external systems
- Client classes required for communicating with other microservices or third-party APIs

### Persistence Layer
**`persistence/`**
- Contains components related to database operations

**`persistence/mapper/`**
- Mapper classes that transform domain objects into database models and vice versa

**`persistence/model/`**
- JPA entity classes representing database tables
- These classes may be annotated with @Entity

**`persistence/repository/`**
- Concrete implementations of repository interfaces for database operations
- Concrete implementations of Spring Data JPA repositories

### Application Layer
**`application.service/`**
- Contains application service classes
- Services that manage business workflows and implement use cases
- Orchestrates domain services and acts as an intermediary between the domain and the outside world

### Configuration
**`config/`**
- Classes containing application configurations
- Spring Bean definitions, security settings, database configurations, etc.
- Classes annotated with Spring's @Configuration annotation

### Controller Layer
**`controller/`**
- REST controller classes that handle HTTP requests
- Classes annotated with Spring's @RestController annotation
- Defines API endpoints and manages request/response processes

### Domain Layer
**`domain/`**
- Contains the domain model with the core business logic of the application
- The most important layer in DDD, where business rules and domain knowledge are defined

**`domain/entity/`**
- Domain objects (entities) with identifiers(such as id) and lifecycles
- Contains business logic and behaviors
- Different from entities in the persistence layer, independent of any framework

**`domain/repository/`**
- Contains repository interfaces
- Abstract interfaces used by the domain layer for database operations
- Concrete implementations are in the persistence layer

**`domain/service/`**
- Services for domain operations that don't belong to a single entity
- Business logic that manages interactions between multiple entities

**`domain/valueobject/`**
- Immutable value objects without identifiers (such as id)
- Equality determined by their values, not by identity
