name: Build, Push and Deploy Rule Admin Service

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag for the service and liquibase images'
        required: false
        default: 'latest'
      deployment-environment:
        type: choice
        description: 'Environment for Kubernetes deployment (None for no deployment)'
        options: [ 'None', 'dev', 'uat' ]
        default: 'None'
        required: true

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Verify Code Quality
        uses: sarp-dev-team/sarp-actions/.github/actions/verify@main
        with:
          maven-username: ${{ secrets.MAVEN_USERNAME }}
          maven-token: ${{ secrets.MAVEN_PASSWORD }}
          sonar-host-url: ${{ vars.SONAR_HOST_URL }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          sonar-project-key: ${{ secrets.SONAR_PROJECT_KEY }}
          sonar-project-name: ${{ github.event.repository.name }}
          branch-name: ${{ github.ref_name }}

      - name: Publish Liquibase Image to ACR
        uses: sarp-dev-team/sarp-actions/.github/actions/publish-liquibase@main
        with:
          repository: ofms/liquibase
          tag: ${{ inputs.tag || 'latest' }}
          branch: ${{ github.ref_name }}
          force-publish: ${{ github.event_name == 'workflow_dispatch' }}
          mvn-username: ${{ secrets.MAVEN_USERNAME }}
          mvn-token: ${{ secrets.MAVEN_PASSWORD }}
        env:
          ACR_USERNAME: ${{ secrets.ACR_USERNAME }}
          ACR_PASSWORD: ${{ secrets.ACR_PASSWORD }}
          AZURE_REGISTRY: ${{ vars.AZURE_REGISTRY }}

      - name: Publish Service Image to ACR
        uses: sarp-dev-team/sarp-actions/.github/actions/publish-branch-image-acr@main
        with:
          service: rule-admin-service
          tag: ${{ inputs.tag || 'latest' }}
          branch: ${{ github.ref_name }}
          mvn-username: ${{ secrets.MAVEN_USERNAME }}
          mvn-token: ${{ secrets.MAVEN_PASSWORD }}
        env:
          ACR_USERNAME: ${{ secrets.ACR_USERNAME }}
          ACR_PASSWORD: ${{ secrets.ACR_PASSWORD }}
          AZURE_REGISTRY: ${{ vars.AZURE_REGISTRY }}

      - name: Deploy Service
        uses: sarp-dev-team/sarp-actions/.github/actions/restart-deployment@main
        if: ${{ github.event_name == 'push' || inputs.deployment-environment != 'None' }}
        with:
          kubeconfig-content: ${{ secrets.KUBECONFIG_CONTENT }}
          context: ofms
          namespace: rule-admin-${{ github.event_name == 'push' && 'dev' || inputs.deployment-environment }}
          deployment: rule-admin
