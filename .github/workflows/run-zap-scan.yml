name: Run ZAP Scan

on:
  schedule:
    - cron: '0 20 * * 5' 
  workflow_dispatch:

jobs:
  zap_scan:
    runs-on: ubuntu-latest
    name: Run ZAP Scan

    permissions:
      contents: read
      issues: write

    steps:
      - name: Run ZAP Scan
        uses: sarp-dev-team/sarp-actions/.github/actions/run-zap-scan@main
        with:
          target-url: ${{ vars.ZAP_TARGET_URL }}
          is-api: true
          token: ${{ secrets.GITHUB_TOKEN}}
