{"uuid": "1b43f6b2-0757-4840-b10e-45392c2fc7cc", "lastMigration": 33, "name": "Demo API", "endpointPrefix": "", "latency": 0, "port": 3000, "hostname": "", "folders": [], "routes": [{"uuid": "2ecbae5c-078f-4e22-9783-66dbc9febb51", "type": "crud", "documentation": "", "method": "", "endpoint": "products", "responses": [{"uuid": "aba5e820-9c89-4f1a-a943-a8213ccc4a68", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "ec32", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "2ecbae5c-078f-4e22-9783-66dbc9febb51"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "c2f56f05-dfd0-407c-b8f8-25acea1985ab", "id": "ec32", "name": "products", "documentation": "", "value": "[\n  {\n    \"id\": \"c5f60f7c-0cbf-4f89-a720-741535eb4d48\",\n    \"name\": \"In-Flight WiFi\",\n    \"varianterAttributeId\": \"ab839bd3-302e-4acb-a1a3-5daeee3689bd\",\n    \"briefingNeeded\": true,\n    \"standalone\": true,\n    \"hasVariants\": true,\n    \"contentDescription\": \"High-speed internet access during flight\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"wifiImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 60,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"DEPARTURE\",\n    \"deliveryLocationType\": \"ONBOARD\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": true,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"Speed\",\n    \"category\": {\n      \"id\": \"d100d747-cd23-4275-8e23-9199b326ce24\",\n      \"name\": \"Connectivity\"\n    },\n    \"variants\": [\n      {\n        \"id\": \"96a72bdf-2320-43bf-97de-d38f7492ba78\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"Basic\", \"unit\": null }\n        ],\n        \"contentDescription\": \"Basic WiFi\",\n        \"contentType\": \"IMAGE\",\n        \"contentData\": \"basicWifiImage\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 30,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"DEPARTURE\",\n        \"deliveryLocationType\": \"ONBOARD\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": false,\n        \"changeAvailable\": false\n      },\n      {\n        \"id\": \"347cdd70-7d39-46d3-8c4d-84626a829e92\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"Premium\", \"unit\": null }\n        ],\n        \"contentDescription\": \"Premium WiFi\",\n        \"contentType\": \"VIDEO\",\n        \"contentData\": \"premiumWifiVideo\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 60,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"DEPARTURE\",\n        \"deliveryLocationType\": \"ONBOARD\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": true,\n        \"changeAvailable\": false\n      }\n    ],\n    \"attributes\": [\n      {\n        \"attributeId\": \"dcfcde60-1e16-413c-a1f2-9ab9fe373513\",\n        \"attributeValues\": [\n          { \"value\": 100, \"unit\": \"MB\" }\n        ],\n        \"attributeName\": \"Data Limit\",\n        \"id\": \"3eb748ff-5650-4133-8321-36e432c9cf1a\"\n      }\n    ]\n  },\n  {\n    \"id\": \"a1b2c3d4-5678-4e9f-8a1b-2c3d4e5f6789\",\n    \"name\": \"Extra Baggage\",\n    \"varianterAttributeId\": \"b2c3d4e5-6789-4f8a-1b2c-3d4e5f6789a1\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": true,\n    \"contentDescription\": \"Purchase additional baggage allowance\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"baggageImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"CHECKIN\",\n    \"deliveryLocationType\": \"AIRPORT\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": true,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"Weight\",\n    \"category\": {\n      \"id\": \"e2f3a4b5-c6d7-4e8f-9a1b-2c3d4e5f6789\",\n      \"name\": \"Luggage\"\n    },\n    \"variants\": [\n      {\n        \"id\": \"b3c4d5e6-789a-4f8b-1c2d-3e4f5a6b7c8d\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"10\", \"unit\": \"KG\" }\n        ],\n        \"contentDescription\": \"10kg Extra\",\n        \"contentType\": \"IMAGE\",\n        \"contentData\": \"10kgImage\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 0,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"CHECKIN\",\n        \"deliveryLocationType\": \"AIRPORT\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": false,\n        \"changeAvailable\": false\n      }\n    ],\n    \"attributes\": [\n      {\n        \"attributeId\": \"f4e5d6c7-b8a9-4f8b-1c2d-3e4f5a6b7c8d\",\n        \"attributeValues\": [\n          { \"value\": 10, \"unit\": \"KG\" }\n        ],\n        \"attributeName\": \"Weight\",\n        \"id\": \"c5d6e7f8-9a1b-4c2d-3e4f-5a6b7c8d9e0f\"\n      }\n    ]\n  },\n  {\n    \"id\": \"b2c3d4e5-6789-4f8a-1b2c-3d4e5f6789a2\",\n    \"name\": \"Lounge Access\",\n    \"varianterAttributeId\": \"c3d4e5f6-789a-4f8b-1c2d-3e4f5a6b7c8e\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": false,\n    \"contentDescription\": \"Access to premium airport lounge\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"loungeImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 120,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"CHECKIN\",\n    \"deliveryLocationType\": \"AIRPORT\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"LoungeType\",\n    \"category\": {\n      \"id\": \"f3a4b5c6-d7e8-4f9a-1b2c-3d4e5f6789b1\",\n      \"name\": \"Services\"\n    },\n    \"variants\": [],\n    \"attributes\": [\n      {\n        \"attributeId\": \"e5d6c7b8-a9f0-4b1c-2d3e-4f5a6b7c8d9e\",\n        \"attributeValues\": [\n          { \"value\": \"Business\", \"unit\": null }\n        ],\n        \"attributeName\": \"LoungeType\",\n        \"id\": \"d6e7f8a9-1b2c-3d4e-5f6a-7b8c9d0e1f2a\"\n      }\n    ]\n  },\n  {\n    \"id\": \"c3d4e5f6-789a-4f8b-1c2d-3e4f5a6b7c8f\",\n    \"name\": \"Seat Selection\",\n    \"varianterAttributeId\": \"d4e5f6a7-89a1-4b8c-2d3e-4f5a6b7c8d9f\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": true,\n    \"contentDescription\": \"Choose your preferred seat\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"seatImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"BOOKING\",\n    \"deliveryLocationType\": \"ONLINE\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": false,\n    \"changeAvailable\": true,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"SeatType\",\n    \"category\": {\n      \"id\": \"a4b5c6d7-e8f9-4a1b-2c3d-4e5f6789b2c3\",\n      \"name\": \"Seating\"\n    },\n    \"variants\": [\n      {\n        \"id\": \"e5f6a7b8-9a1b-4c2d-3e4f-5a6b7c8d9e1f\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"Window\", \"unit\": null }\n        ],\n        \"contentDescription\": \"Window seat\",\n        \"contentType\": \"IMAGE\",\n        \"contentData\": \"windowSeatImage\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 0,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"BOOKING\",\n        \"deliveryLocationType\": \"ONLINE\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": false,\n        \"changeAvailable\": true\n      }\n    ],\n    \"attributes\": [\n      {\n        \"attributeId\": \"f6a7b8c9-a1b2-4c3d-5e6f-7a8b9c0d1e2f\",\n        \"attributeValues\": [\n          { \"value\": \"Aisle\", \"unit\": null }\n        ],\n        \"attributeName\": \"SeatType\",\n        \"id\": \"e7f8a9b0-1a2b-3c4d-5e6f-7a8b9c0d1e2f\"\n      }\n    ]\n  },\n  {\n    \"id\": \"d4e5f6a7-89a1-4b8c-2d3e-4f5a6b7c8d9a\",\n    \"name\": \"In-Flight Meal\",\n    \"varianterAttributeId\": \"e5f6a7b8-9a1b-4c2d-3e4f-5a6b7c8d9e2a\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": true,\n    \"contentDescription\": \"Choose your meal for the flight\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"mealImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"DEPARTURE\",\n    \"deliveryLocationType\": \"ONBOARD\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"MealType\",\n    \"category\": {\n      \"id\": \"b5c6d7e8-f9a1-4b2c-3d4e-5f6789b3c4d5\",\n      \"name\": \"Catering\"\n    },\n    \"variants\": [\n      {\n        \"id\": \"f6a7b8c9-a1b2-4c3d-5e6f-7a8b9c0d1e3f\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"Vegetarian\", \"unit\": null }\n        ],\n        \"contentDescription\": \"Vegetarian meal\",\n        \"contentType\": \"IMAGE\",\n        \"contentData\": \"vegMealImage\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 0,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"DEPARTURE\",\n        \"deliveryLocationType\": \"ONBOARD\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": true,\n        \"changeAvailable\": false\n      }\n    ],\n    \"attributes\": [\n      {\n        \"attributeId\": \"a7b8c9d0-a1b2-4c3d-5e6f-7a8b9c0d1e3f\",\n        \"attributeValues\": [\n          { \"value\": \"Non-Vegetarian\", \"unit\": null }\n        ],\n        \"attributeName\": \"MealType\",\n        \"id\": \"f8a9b0c1-2a3b-4c5d-6e7f-8a9b0c1d2e3f\"\n      }\n    ]\n  },\n  {\n    \"id\": \"e1a2b3c4-d5f6-4a7b-8c9d-0e1f2a3b4c5d\",\n    \"name\": \"Priority Boarding\",\n    \"varianterAttributeId\": \"f2a3b4c5-d6e7-4f8a-9b0c-1d2e3f4a5b6c\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": false,\n    \"contentDescription\": \"Board the plane before general boarding\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"priorityBoardingImage\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"CHECKIN\",\n    \"deliveryLocationType\": \"AIRPORT\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"BoardingGroup\",\n    \"category\": {\n      \"id\": \"c5d6e7f8-9a1b-4c2d-3e4f-5a6b7c8d9e1a\",\n      \"name\": \"Services\"\n    },\n    \"variants\": [],\n    \"attributes\": [\n      {\n        \"attributeId\": \"a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5e\",\n        \"attributeValues\": [\n          { \"value\": \"Group 1\", \"unit\": null }\n        ],\n        \"attributeName\": \"BoardingGroup\",\n        \"id\": \"b2c3d4e5-f6a7-4b8c-9d0e-1f2a3b4c5d6e\"\n      }\n    ]\n  },\n  {\n    \"id\": \"f3b4c5d6-e7f8-4a9b-0c1d-2e3f4a5b6c7d\",\n    \"name\": \"Travel Insurance\",\n    \"varianterAttributeId\": \"a4a5b6c7-d8e9-4f0a-1b2c-3d4e5f6a7b8c\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": true,\n    \"contentDescription\": \"Comprehensive travel insurance for your trip\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"insuranceImageData\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"BOOKING\",\n    \"deliveryLocationType\": \"ONLINE\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": false,\n    \"changeAvailable\": true,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"CoverageType\",\n    \"category\": {\n      \"id\": \"d6e7f8a9-1b2c-3d4e-5f6a-7b8c9d0e1f3a\",\n      \"name\": \"Insurance\"\n    },\n    \"variants\": [\n      {\n        \"id\": \"b5c6d7e8-f9a0-4b1c-2d3e-4f5a6b7c8d9e\",\n        \"varianterAttributeValues\": [\n          { \"value\": \"Basic\", \"unit\": null }\n        ],\n        \"contentDescription\": \"Basic coverage\",\n        \"contentType\": \"IMAGE\",\n        \"contentData\": \"basicCoverageImage\",\n        \"deliveryUsageType\": \"ONE_TIME\",\n        \"deliveryUsageCount\": 1,\n        \"deliveryUsageDuration\": 0,\n        \"deliveryUsageDurationUnit\": \"MINUTE\",\n        \"deliveryActivationTrigger\": \"BOOKING\",\n        \"deliveryLocationType\": \"ONLINE\",\n        \"deliveryChannel\": \"SMS\",\n        \"refundAvailable\": false,\n        \"changeAvailable\": true\n      }\n    ],\n    \"attributes\": [\n      {\n        \"attributeId\": \"c7d8e9f0-a1b2-4c3d-5e6f-7a8b9c0d1e4f\",\n        \"attributeValues\": [\n          { \"value\": \"Worldwide\", \"unit\": null }\n        ],\n        \"attributeName\": \"CoverageArea\",\n        \"id\": \"d8e9f0a1-b2c3-4d5e-6f7a-8b9c0d1e2f3a\"\n      }\n    ]\n  },\n  {\n    \"id\": \"a5b6c7d8-e9f0-4a1b-2c3d-4e5f6a7b8c9d\",\n    \"name\": \"Pet in Cabin\",\n    \"varianterAttributeId\": \"c6d7e8f9-a0b1-4c2d-3e4f-5a6b7c8d9e0f\",\n    \"briefingNeeded\": true,\n    \"standalone\": true,\n    \"hasVariants\": false,\n    \"contentDescription\": \"Bring your pet in the cabin\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"petCabinImage\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"CHECKIN\",\n    \"deliveryLocationType\": \"AIRPORT\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": false,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"PetType\",\n    \"category\": {\n      \"id\": \"e7f8a9b0-1a2b-3c4d-5e6f-7a8b9c0d1e5f\",\n      \"name\": \"Special Services\"\n    },\n    \"variants\": [],\n    \"attributes\": [\n      {\n        \"attributeId\": \"e9f0a1b2-c3d4-5e6f-7a8b-9c0d1e2f3a4b\",\n        \"attributeValues\": [\n          { \"value\": \"Dog\", \"unit\": null }\n        ],\n        \"attributeName\": \"PetType\",\n        \"id\": \"f0a1b2c3-d4e5-6f7a-8b9c-0d1e2f3a4b5c\"\n      }\n    ]\n  },\n  {\n    \"id\": \"b6c7d8e9-f0a1-4b2c-3d4e-5f6a7b8c9d0e\",\n    \"name\": \"Fast Track Security\",\n    \"varianterAttributeId\": \"d7e8f9a0-b1c2-4d3e-5f6a-7b8c9d0e1f2a\",\n    \"briefingNeeded\": false,\n    \"standalone\": true,\n    \"hasVariants\": false,\n    \"contentDescription\": \"Skip the regular security line\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"fastTrackImage\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"CHECKIN\",\n    \"deliveryLocationType\": \"AIRPORT\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": true,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"ServiceLevel\",\n    \"category\": {\n      \"id\": \"f8a9b0c1-2a3b-4c5d-6e7f-8a9b0c1d2e5f\",\n      \"name\": \"Services\"\n    },\n    \"variants\": [],\n    \"attributes\": [\n      {\n        \"attributeId\": \"a0b1c2d3-e4f5-6a7b-8c9d-0e1f2a3b4c5d\",\n        \"attributeValues\": [\n          { \"value\": \"Express\", \"unit\": null }\n        ],\n        \"attributeName\": \"ServiceLevel\",\n        \"id\": \"b1c2d3e4-f5a6-7b8c-9d0e-1f2a3b4c5d6e\"\n      }\n    ]\n  },\n  {\n    \"id\": \"c7d8e9f0-a1b2-4c3d-5e6f-7a8b9c0d1e2f\",\n    \"name\": \"Infant Bassinet\",\n    \"varianterAttributeId\": \"e8f9a0b1-c2d3-4e5f-6a7b-8c9d0e1f2a3b\",\n    \"briefingNeeded\": true,\n    \"standalone\": true,\n    \"hasVariants\": false,\n    \"contentDescription\": \"Request a bassinet for your infant\",\n    \"contentType\": \"IMAGE\",\n    \"contentData\": \"bassinetImage\",\n    \"deliveryUsageType\": \"ONE_TIME\",\n    \"deliveryUsageCount\": 1,\n    \"deliveryUsageDuration\": 0,\n    \"deliveryUsageDurationUnit\": \"MINUTE\",\n    \"deliveryActivationTrigger\": \"BOOKING\",\n    \"deliveryLocationType\": \"ONBOARD\",\n    \"deliveryChannel\": \"EMAIL\",\n    \"refundAvailable\": false,\n    \"changeAvailable\": false,\n    \"status\": \"ACTIVE\",\n    \"type\": \"PRODUCT\",\n    \"stocked\": true,\n    \"varianterAttributeName\": \"InfantAge\",\n    \"category\": {\n      \"id\": \"a9b0c1d2-3e4f-5a6b-7c8d-9e0f1a2b3c4d\",\n      \"name\": \"Special Services\"\n    },\n    \"variants\": [],\n    \"attributes\": [\n      {\n        \"attributeId\": \"c2d3e4f5-a6b7-8c9d-0e1f-2a3b4c5d6e7f\",\n        \"attributeValues\": [\n          { \"value\": \"0-6 months\", \"unit\": null }\n        ],\n        \"attributeName\": \"InfantAge\",\n        \"id\": \"d3e4f5a6-b7c8-9d0e-1f2a-3b4c5d6e7f8a\"\n      }\n    ]\n  }\n]"}], "callbacks": []}