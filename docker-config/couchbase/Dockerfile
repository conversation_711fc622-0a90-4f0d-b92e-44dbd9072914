FROM couchbase:latest

# Environment variables for Couchbase setup
ENV CB_REST_USERNAME=admin
ENV CB_REST_PASSWORD=sarp123
ENV BUCKET_NAME=SARP
ENV SCOPE_NAME=OfMS
ENV RAM_SIZE=512
ENV BUCKET_TYPE=couchbase
ENV REPLICA_COUNT=1

# Copy the setup script
COPY setup.sh /opt/couchbase/setup.sh

# Change owner of setup script
RUN chown couchbase:couchbase /opt/couchbase/setup.sh

# Make the script executable
RUN chmod +x /opt/couchbase/setup.sh

# Set the entrypoint
CMD ["/bin/sh", "/opt/couchbase/setup.sh"]
