#!/bin/bash
set -e

# Print environment variables being used
echo "Setting up Couchbase with:"
echo "Username: $CB_REST_USERNAME"
echo "Bucket: $BUCKET_NAME"
echo "Scope: $SCOPE_NAME"

# Start Couchbase server in the background
/entrypoint.sh couchbase-server &

# Generic function for retries
retry_until() {
  local name=$1
  local max_attempts=$2
  local interval=$3
  local cmd=$4
  
  echo "Waiting for $name..."
  local attempt=0
  
  while [ $attempt -lt $max_attempts ]; do
    if eval "$cmd"; then
      echo "$name is ready"
      return 0
    fi
    
    attempt=$((attempt+1))
    echo "Waiting for $name... (attempt $attempt/$max_attempts)"
    sleep $interval
  done
  
  echo "$name not ready within allowed time"
  return 1
}

# Wait for Couchbase to start up
retry_until "Couchbase server" 60 2 "curl -s http://localhost:8091/pools > /dev/null" || exit 1

# Check if cluster is already initialized
if couchbase-cli server-list --cluster localhost --username $CB_REST_USERNAME --password $CB_REST_PASSWORD > /dev/null 2>&1; then
  echo "Cluster is already initialized"
else
  echo "Initializing Couchbase cluster..."
  couchbase-cli cluster-init \
      --cluster localhost \
      --cluster-username $CB_REST_USERNAME \
      --cluster-password $CB_REST_PASSWORD \
      --services data,index,query \
      --cluster-ramsize 1024 \
      --cluster-index-ramsize 512 \
      --index-storage-setting default
fi

# Wait for services to be fully initialized
retry_until "Cluster health" 30 2 "curl -s -u $CB_REST_USERNAME:$CB_REST_PASSWORD http://localhost:8091/pools/default | grep -q '\"status\":\"healthy\"'" || echo "Proceeding without waiting for cluster health"

# Set appropriate replica count
NODE_COUNT=$(couchbase-cli server-list --cluster localhost --username $CB_REST_USERNAME --password $CB_REST_PASSWORD | grep -c "^" || echo "1")
if [ "$NODE_COUNT" -le 1 ]; then
  ADJUSTED_REPLICA_COUNT=0
  echo "Single node detected, setting replica count to 0"
else
  ADJUSTED_REPLICA_COUNT=$(( NODE_COUNT > REPLICA_COUNT ? REPLICA_COUNT : NODE_COUNT-1 ))
  echo "Setting replica count to $ADJUSTED_REPLICA_COUNT based on $NODE_COUNT available nodes"
fi

# Check for existing bucket or create new one
if couchbase-cli bucket-list --cluster localhost --username $CB_REST_USERNAME --password $CB_REST_PASSWORD | grep -q "$BUCKET_NAME"; then
  echo "Bucket '$BUCKET_NAME' already exists, skipping creation"
else
  echo "Creating bucket '$BUCKET_NAME'..."
  couchbase-cli bucket-create \
      --cluster localhost \
      --username $CB_REST_USERNAME \
      --password $CB_REST_PASSWORD \
      --bucket $BUCKET_NAME \
      --bucket-type $BUCKET_TYPE \
      --bucket-ramsize $RAM_SIZE \
      --bucket-replica $ADJUSTED_REPLICA_COUNT \
      --wait
fi

# Check for existing scope or create new one
check_scope() {
  couchbase-cli collection-manage \
      --cluster localhost \
      --username $CB_REST_USERNAME \
      --password $CB_REST_PASSWORD \
      --bucket $BUCKET_NAME \
      --list-scopes | grep -q "$SCOPE_NAME"
}

if check_scope; then
  echo "Scope '$SCOPE_NAME' already exists in bucket '$BUCKET_NAME', skipping creation"
else
  echo "Creating scope '$SCOPE_NAME' in bucket '$BUCKET_NAME'..."
  couchbase-cli collection-manage \
      --cluster localhost \
      --username $CB_REST_USERNAME \
      --password $CB_REST_PASSWORD \
      --bucket $BUCKET_NAME \
      --create-scope $SCOPE_NAME
  
  # Verify scope creation
  retry_until "Scope $SCOPE_NAME" 15 2 "check_scope"
fi

echo "Couchbase setup completed with bucket '$BUCKET_NAME' and scope '$SCOPE_NAME'"

# Keep container running
tail -f /dev/null