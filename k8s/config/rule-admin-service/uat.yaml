ruleAdminService:
  image: 
    tag: latest
  port: 8080
  imagePullSecrets: acr-creds
  configMapName: rule-admin-config
  replicas: 1
  env:
    spring:
      datasource:
        host: postgresql.ofms-infra-uat
    sarp:
      kafka:
        producer:
          bootstrap-servers: kafka.ofms-infra-uat:9092
          client-id: rule-admin-producer
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer
          properties:
            enable.idempotence: true
            max.in.flight.requests.per.connection: 5
            batch.size: 16384
            compression.type: lz4
            buffer.memory: 33554432
        producers:
          rule-producer:
            bootstrap-servers: kafka.ofms-infra-uat:9092
            client-id: rule-admin-rule-producer
      rule:
        event:
          producer: rule-producer
          topic: sarp.offer.rule.0
        market:
          producer: rule-producer
          topic: sarp.offer.market.0
    service:
      discovery:
        product:
          url: http://mockoon:3000/

liquibase:
  image: 
    tag: latest
