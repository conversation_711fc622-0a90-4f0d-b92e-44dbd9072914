environments:
  dev:
  uat:

---
templates:
  default:
    missingFileHandler: Warn
    values:
      - config/{{ .Release.Name }}/{{ .Environment.Name }}.yaml
    secrets:
      - secrets/{{ .Release.Name }}/{{ .Environment.Name }}-secrets.yaml

releases:
  - name: mockoon
    namespace: rule-admin-{{ .Environment.Name | default "dev" }}
    chart: ./charts/mockoon
    inherit:
      - template: default

  - name: rule-admin-service
    namespace: rule-admin-{{ .Environment.Name | default "dev" }}
    chart: ./charts/rule-admin-service
    inherit:
      - template: default
