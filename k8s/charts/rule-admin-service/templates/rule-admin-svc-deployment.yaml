apiVersion: apps/v1
kind: Deployment
metadata:
  name: rule-admin
spec:
  replicas:  {{ .Values.ruleAdminService.replicas }}
  selector:
    matchLabels:
      app: rule-admin
  template:
    metadata:
      labels:
        app: rule-admin
      annotations:
        checksum/config: {{ dict "env" .Values.ruleAdminService.env | toYaml | sha256sum }}
    spec:
      initContainers:
        - name: liquibase-commons
          image: {{ .Values.liquibase.image.repository }}
          command:
            - "liquibase"
            - "update"
            - "--changelog-file=db/commons/changelog/db.changelog-master.yml"
            - "--url=jdbc:postgresql://{{ .Values.ruleAdminService.env.spring.datasource.host | default "postgres" }}:{{ .Values.ruleAdminService.env.spring.datasource.port }}/{{ .Values.ruleAdminService.env.spring.datasource.db }}"
            - "--username={{ .Values.ruleAdminService.env.spring.datasource.username }}"
            - "--password={{ .Values.ruleAdminService.env.spring.datasource.password }}"
        - name: liquibase
          image: {{ .Values.liquibase.image.repository }}:{{ .Values.liquibase.image.tag }}
          command:
            - "liquibase"
            - "update"
            - "--changelog-file=db/changelog/db.changelog-master.yml"
            - "--url=jdbc:postgresql://{{ .Values.ruleAdminService.env.spring.datasource.host | default "postgres" }}:{{ .Values.ruleAdminService.env.spring.datasource.port }}/{{ .Values.ruleAdminService.env.spring.datasource.db }}"
            - "--username={{ .Values.ruleAdminService.env.spring.datasource.username }}"
            - "--password={{ .Values.ruleAdminService.env.spring.datasource.password }}"
      containers:
        - name: rule-admin
          image: {{ .Values.ruleAdminService.image.repository }}:{{ .Values.ruleAdminService.image.tag }}
          imagePullPolicy: Always
          ports:
            - containerPort: {{ .Values.ruleAdminService.port }}
          envFrom:
            - configMapRef:
                name: {{ .Values.ruleAdminService.configMapName }}
          # readinessProbe:
          #   httpGet:
          #     path: /
          #     port: {{ .Values.ruleAdminService.port }}
          #   initialDelaySeconds: 10
          #   periodSeconds: 10
      imagePullSecrets:
        - name: {{ .Values.ruleAdminService.imagePullSecrets }}
