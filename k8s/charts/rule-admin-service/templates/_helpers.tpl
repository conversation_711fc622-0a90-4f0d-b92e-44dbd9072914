{{/*
Generates env-style key-value pairs from a map
Usage:
  include "renderEnvPrefix" (list "PREFIX_" .Values.foo)
*/}}
{{- define "flattenEnvMap" -}}
{{- $root := index . 0 -}}
{{- $prefix := index . 1 -}}
{{- range $k, $v := $root }}
  {{- $key := printf "%s%s" $prefix (upper $k) }}
  {{- if kindIs "map" $v }}
    {{- include "flattenEnvMap" (list $v (print $key "_")) }}
  {{- else }}
{{ $key }}: "{{ $v }}"
  {{- end }}
{{- end }}
{{- end }}