apiVersion: apps/v1
kind: Deployment
metadata:
  name: mockoon
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mockoon
  template:
    metadata:
      labels:
        app: mockoon
      annotations:
        configmap-hash: {{ .Files.Get "files/environment.json" | sha256sum }}
    spec:
      containers:
        - name: mockoon
          image: {{ .Values.mockoon.image }}
          ports:
            - containerPort: {{ .Values.mockoon.port }}
          volumeMounts:
            - name: mockoon-config
              mountPath: /config/{{ .Values.mockoon.configFileName }}
              subPath: {{ .Values.mockoon.configFileName }}
          command:
            - "mockoon-cli"
            - "start"
            - "-d"
            - "/config/{{ .Values.mockoon.configFileName }}"
            - "-p"
            - "{{ .Values.mockoon.port }}"
      volumes:
        - name: mockoon-config
          configMap:
            name: {{ .Values.mockoon.configMapName }}
---
apiVersion: v1
kind: Service
metadata:
  name: mockoon
spec:
  selector:
    app: mockoon
  ports:
    - protocol: TCP
      port: {{ .Values.mockoon.port }}
      targetPort: {{ .Values.mockoon.port }}
