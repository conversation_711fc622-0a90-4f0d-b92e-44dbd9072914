{"uuid": "0fb1ea50-7a24-49d9-932c-bf04fe321068", "lastMigration": 33, "name": "Demo API", "endpointPrefix": "", "latency": 0, "port": 3000, "hostname": "", "folders": [], "routes": [{"uuid": "12d2b134-0943-4bd4-9561-d4abb45bffe3", "type": "crud", "documentation": "", "method": "", "endpoint": "products", "responses": [{"uuid": "3eef4122-db80-4bc4-9d7c-84393795dfa7", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "leut", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "12d2b134-0943-4bd4-9561-d4abb45bffe3"}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "9665e3a0-9264-421d-820c-fc74495c1911", "id": "leut", "name": "Products", "documentation": "mock datas produced according to ProductEntity in sarp product-service", "value": "[\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-************\",\n    \"name\": \"iPhone 14 Pro\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-************\",\n      \"name\": \"Smartphones\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-************\",\n        \"name\": \"Electronics\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-************\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-************\",\n            \"name\": \"Storage\",\n            \"description\": \"Internal storage capacity\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"128GB\"},\n              {\"value\": \"256GB\"},\n              {\"value\": \"512GB\"},\n              {\"value\": \"1TB\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        },\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440005\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440006\",\n            \"name\": \"Color\",\n            \"description\": \"Device color\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Space Black\"},\n              {\"value\": \"Silver\"},\n              {\"value\": \"Gold\"},\n              {\"value\": \"Deep Purple\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440007\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440008\",\n          \"name\": \"Screen Size\",\n          \"description\": \"Display size in inches\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"NUMBER\",\n          \"valueDataType\": \"DECIMAL\",\n          \"boundaryValues\": {\n            \"min\": 0,\n            \"max\": 100\n          },\n          \"unitOptions\": [\"inches\"]\n        },\n        \"attributeValues\": [\n          {\"value\": \"6.1\", \"unit\": \"inches\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440009\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440010\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-************\",\n            \"name\": \"Storage\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"128GB\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440011\",\n    \"name\": \"Nike Air Max 270\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440012\",\n      \"name\": \"Running Shoes\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440013\",\n        \"name\": \"Footwear\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440014\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440015\",\n            \"name\": \"Size\",\n            \"description\": \"Shoe size\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"US 7\"},\n              {\"value\": \"US 8\"},\n              {\"value\": \"US 9\"},\n              {\"value\": \"US 10\"},\n              {\"value\": \"US 11\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440016\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440017\",\n          \"name\": \"Weight\",\n          \"description\": \"Shoe weight\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"NUMBER\",\n          \"valueDataType\": \"DECIMAL\",\n          \"boundaryValues\": {\n            \"min\": 0,\n            \"max\": 1000\n          },\n          \"unitOptions\": [\"grams\"]\n        },\n        \"attributeValues\": [\n          {\"value\": \"320\", \"unit\": \"grams\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440018\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440019\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440015\",\n            \"name\": \"Size\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"US 9\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440020\",\n    \"name\": \"Premium Economy Seat Upgrade\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440021\",\n      \"name\": \"Seat Upgrades\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440023\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440024\",\n            \"name\": \"Seat Location\",\n            \"description\": \"Preferred seat location\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Front\"},\n              {\"value\": \"Middle\"},\n              {\"value\": \"Back\"},\n              {\"value\": \"Window\"},\n              {\"value\": \"Aisle\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440025\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440026\",\n          \"name\": \"Legroom\",\n          \"description\": \"Additional legroom in inches\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"NUMBER\",\n          \"valueDataType\": \"DECIMAL\",\n          \"boundaryValues\": {\n            \"min\": 0,\n            \"max\": 100\n          },\n          \"unitOptions\": [\"inches\"]\n        },\n        \"attributeValues\": [\n          {\"value\": \"34\", \"unit\": \"inches\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440027\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440028\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440024\",\n            \"name\": \"Seat Location\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Window\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440029\",\n    \"name\": \"Extra Baggage Allowance\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440030\",\n      \"name\": \"Baggage Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440031\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440032\",\n            \"name\": \"Baggage Weight\",\n            \"description\": \"Additional baggage weight allowance\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"5kg\"},\n              {\"value\": \"10kg\"},\n              {\"value\": \"15kg\"},\n              {\"value\": \"20kg\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440033\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440034\",\n          \"name\": \"Max Dimensions\",\n          \"description\": \"Maximum baggage dimensions\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"TEXT\",\n          \"valueDataType\": \"STRING\"\n        },\n        \"attributeValues\": [\n          {\"value\": \"90x75x43 cm\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440035\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440036\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440032\",\n            \"name\": \"Baggage Weight\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"10kg\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440037\",\n    \"name\": \"Premium Meal Service\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440038\",\n      \"name\": \"In-Flight Dining\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440039\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440040\",\n            \"name\": \"Meal Type\",\n            \"description\": \"Type of meal service\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Vegetarian\"},\n              {\"value\": \"Vegan\"},\n              {\"value\": \"Halal\"},\n              {\"value\": \"Kosher\"},\n              {\"value\": \"Gluten-Free\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440041\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440042\",\n          \"name\": \"Course Count\",\n          \"description\": \"Number of courses in meal\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"NUMBER\",\n          \"valueDataType\": \"INTEGER\",\n          \"boundaryValues\": {\n            \"min\": 1,\n            \"max\": 5\n          }\n        },\n        \"attributeValues\": [\n          {\"value\": \"3\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440043\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440044\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440040\",\n            \"name\": \"Meal Type\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Vegetarian\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440045\",\n    \"name\": \"Airport Lounge Access\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440046\",\n      \"name\": \"Airport Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440047\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440048\",\n            \"name\": \"Lounge Type\",\n            \"description\": \"Type of lounge access\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Standard\"},\n              {\"value\": \"Premium\"},\n              {\"value\": \"First Class\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440049\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440050\",\n          \"name\": \"Access Duration\",\n          \"description\": \"Duration of lounge access\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"SELECT\",\n          \"valueDataType\": \"STRING\",\n          \"valueOptions\": [\n            {\"value\": \"3 hours\"},\n            {\"value\": \"6 hours\"},\n            {\"value\": \"12 hours\"}\n          ]\n        },\n        \"attributeValues\": [\n          {\"value\": \"3 hours\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440051\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440052\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440048\",\n            \"name\": \"Lounge Type\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Premium\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440053\",\n    \"name\": \"Priority Boarding\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440054\",\n      \"name\": \"Boarding Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440055\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440056\",\n            \"name\": \"Boarding Group\",\n            \"description\": \"Priority boarding group\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Group 1\"},\n              {\"value\": \"Group 2\"},\n              {\"value\": \"Group 3\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440057\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440058\",\n          \"name\": \"Early Access\",\n          \"description\": \"Minutes before regular boarding\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"NUMBER\",\n          \"valueDataType\": \"INTEGER\",\n          \"boundaryValues\": {\n            \"min\": 0,\n            \"max\": 60\n          },\n          \"unitOptions\": [\"minutes\"]\n        },\n        \"attributeValues\": [\n          {\"value\": \"30\", \"unit\": \"minutes\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440059\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440060\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440056\",\n            \"name\": \"Boarding Group\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Group 1\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440061\",\n    \"name\": \"In-Flight WiFi Access\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440062\",\n      \"name\": \"Connectivity Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440063\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440064\",\n            \"name\": \"Data Plan\",\n            \"description\": \"WiFi data plan\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Basic (100MB)\"},\n              {\"value\": \"Standard (500MB)\"},\n              {\"value\": \"Premium (1GB)\"},\n              {\"value\": \"Unlimited\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440065\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440066\",\n          \"name\": \"Speed\",\n          \"description\": \"Connection speed\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"SELECT\",\n          \"valueDataType\": \"STRING\",\n          \"valueOptions\": [\n            {\"value\": \"Standard\"},\n            {\"value\": \"High-Speed\"}\n          ]\n        },\n        \"attributeValues\": [\n          {\"value\": \"High-Speed\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440067\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440068\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440064\",\n            \"name\": \"Data Plan\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Premium (1GB)\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440069\",\n    \"name\": \"Travel Insurance\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440070\",\n      \"name\": \"Insurance Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440071\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440072\",\n            \"name\": \"Coverage Type\",\n            \"description\": \"Type of insurance coverage\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Basic\"},\n              {\"value\": \"Standard\"},\n              {\"value\": \"Comprehensive\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440073\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440074\",\n          \"name\": \"Coverage Amount\",\n          \"description\": \"Maximum coverage amount\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"SELECT\",\n          \"valueDataType\": \"STRING\",\n          \"valueOptions\": [\n            {\"value\": \"$10,000\"},\n            {\"value\": \"$25,000\"},\n            {\"value\": \"$50,000\"}\n          ]\n        },\n        \"attributeValues\": [\n          {\"value\": \"$25,000\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440075\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440076\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-446655440072\",\n            \"name\": \"Coverage Type\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Standard\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  },\n  {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440077\",\n    \"name\": \"Fast Track Security\",\n    \"category\": {\n      \"id\": \"550e8400-e29b-41d4-a716-446655440078\",\n      \"name\": \"Airport Security Services\",\n      \"parentCategory\": {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440022\",\n        \"name\": \"Flight Services\",\n        \"leaf\": false\n      },\n      \"leaf\": true,\n      \"categoryAttributes\": [\n        {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440079\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-************\",\n            \"name\": \"Service Level\",\n            \"description\": \"Level of fast track service\",\n            \"valueCardinality\": \"SINGLE\",\n            \"inputType\": \"SELECT\",\n            \"valueDataType\": \"STRING\",\n            \"valueOptions\": [\n              {\"value\": \"Standard\"},\n              {\"value\": \"Premium\"},\n              {\"value\": \"VIP\"}\n            ]\n          },\n          \"varianter\": true,\n          \"required\": true\n        }\n      ]\n    },\n    \"attributes\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-446655440081\",\n        \"attribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-446655440082\",\n          \"name\": \"Estimated Time\",\n          \"description\": \"Estimated time savings\",\n          \"valueCardinality\": \"SINGLE\",\n          \"inputType\": \"SELECT\",\n          \"valueDataType\": \"STRING\",\n          \"valueOptions\": [\n            {\"value\": \"15-30 minutes\"},\n            {\"value\": \"30-45 minutes\"},\n            {\"value\": \"45-60 minutes\"}\n          ]\n        },\n        \"attributeValues\": [\n          {\"value\": \"30-45 minutes\"}\n        ]\n      }\n    ],\n    \"variants\": [\n      {\n        \"id\": \"550e8400-e29b-41d4-a716-************\",\n        \"varianterAttribute\": {\n          \"id\": \"550e8400-e29b-41d4-a716-************\",\n          \"attribute\": {\n            \"id\": \"550e8400-e29b-41d4-a716-************\",\n            \"name\": \"Service Level\"\n          },\n          \"attributeValues\": [\n            {\"value\": \"Premium\"}\n          ]\n        },\n        \"refundAvailable\": true\n      }\n    ],\n    \"refundAvailable\": true\n  }\n]"}], "callbacks": [{"uuid": "c3ab206b-b1c6-4d66-9e00-dadff938a5c7", "id": "lgs5", "uri": "", "name": "Callback", "documentation": "", "method": "post", "headers": [], "bodyType": "FILE", "body": "", "databucketID": "", "filePath": "", "sendFileAsBody": true}]}