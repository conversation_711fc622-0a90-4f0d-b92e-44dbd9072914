apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: rule-admin-applicationset
  namespace: argocd
spec:
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
    - list:
        elements:
          - env: dev
            namespace: rule-admin-dev
            cluster: https://ofms.************.sslip.io
            sync: true
          - env: uat
            namespace: rule-admin-uat
            cluster: https://ofms.************.sslip.io
            sync: true
  template:
    metadata:
      name: rule-admin-{{.env}}
    spec:
      project: sarp
      source:
        repoURL: https://github.com/sarp-dev-team/rule-admin-service.git
        targetRevision: HEAD
        path: k8s
        plugin:
          name: helmfile
          env:
            - name: ENV_NAME
              value: "{{.env}}"
      destination:
        server: "{{.cluster}}"
        namespace: "{{.namespace}}"
  templatePatch: |
    {{- if .sync }}
    spec:
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
    {{- end }}
