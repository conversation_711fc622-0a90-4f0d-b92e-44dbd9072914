package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "condition_node")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConditionNodeEntity {
    @Id
    private UUID id;

    @ManyToOne(cascade = {CascadeType.ALL})
    @JoinColumn(name = "condition_id", nullable = false)
    private ConditionEntity condition;

    @Column(name = "next_node_id")
    private UUID nextNodeId;

    @Column(name = "previous_node_id")
    private UUID previousNodeId;
}
