package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.ProductEntity;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

public interface ProductJpaRepository
        extends JpaRepository<ProductEntity, UUID>, QuerydslPredicateExecutor<ProductEntity> {
    @Query("SELECT p.variantId FROM ProductEntity p WHERE p.id IN :productEntityIds")
    List<UUID> findVariantIdsByProductEntityIds(@Param("productEntityIds") Collection<UUID> productEntityIds);

    List<ProductEntity> findAllByVariantIdIn(List<UUID> ids);

    List<ProductEntity> findAllByIdIn(Collection<UUID> ids);
}
