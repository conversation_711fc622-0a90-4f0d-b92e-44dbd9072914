package com.sarp.rule.adapter.persistence.model.entity;

import com.sarp.rule.adapter.persistence.model.enums.BundleStatus;
import com.sarp.rule.adapter.persistence.model.enums.BundleType;
import jakarta.persistence.*;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "bundle")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BundleEntity extends AuditEntity {

    @Id
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String description;

    @Lob
    private byte[] image;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private BundleType type;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private BundleStatus status;

    @OneToMany(mappedBy = "bundle", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<BundleProductEntity> bundleProducts;
}
