package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.adapter.persistence.model.entity.ProductEntity;
import com.sarp.rule.domain.entity.Product;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", builder = @Builder)
public interface ProductMapper {

    ProductEntity toEntity(Product product);

    Product toDomain(ProductEntity productEntity);
}
