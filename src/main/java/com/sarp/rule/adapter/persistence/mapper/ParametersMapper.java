package com.sarp.rule.adapter.persistence.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.adapter.persistence.exception.ParameterMappingException;
import com.sarp.rule.domain.valueobject.action.Parameters;
import java.util.HashMap;
import java.util.Map;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Common mapper for Parameters value object conversion. This can be used by other mappers to handle
 * Parameters conversions.
 */
@Mapper(componentModel = "spring")
public abstract class ParametersMapper {

    @Autowired
    protected ObjectMapper objectMapper;

    public JsonNode map(Parameters parameters) {
        if (parameters == null || parameters.parameters() == null) {
            return objectMapper.createArrayNode();
        }
        try {
            return objectMapper.valueToTree(parameters.parameters());
        } catch (Exception e) {
            throw new ParameterMappingException("Error serializing Parameters", e);
        }
    }

    public Parameters map(JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        try {
            Map<String, Object> paramList = objectMapper.convertValue(
                    jsonNode, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class));
            return new Parameters(paramList);
        } catch (Exception e) {
            throw new ParameterMappingException("Error deserializing Parameters", e);
        }
    }

    /**
     * Maps a JSON String to Parameters value object. This method can be used for reverse mapping
     * when needed.
     *
     * @param jsonString The JSON string to convert
     * @return Parameters value object
     */
    public Parameters map(String jsonString) {
        if (jsonString == null || jsonString.isEmpty() || "[]".equals(jsonString)) {
            return null;
        }
        try {
            Map<String, Object> paramList = objectMapper.readValue(
                    jsonString, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class));
            return new Parameters(paramList);
        } catch (JsonProcessingException e) {
            throw new ParameterMappingException("Error deserializing Parameters", e);
        }
    }

    /**
     * Maps a list of strings to Parameters value object. This method can be used for reverse
     * mapping when needed.
     *
     * @param parameters The list of strings to convert
     * @return Parameters value object
     */
    public Parameters map(Map<String, Object> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return null;
        }
        return new Parameters(parameters);
    }

    public Parameters map(Object value) {

        if (!(value instanceof Map<?, ?> rawMap)) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        rawMap.forEach((k, v) -> result.put(String.valueOf(k), v));

        return new Parameters(result);
    }
}
