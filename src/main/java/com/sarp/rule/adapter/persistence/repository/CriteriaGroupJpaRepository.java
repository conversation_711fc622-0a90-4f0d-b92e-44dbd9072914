package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CriteriaGroupJpaRepository extends JpaRepository<CriteriaGroupEntity, UUID> {
    boolean existsByName(String name);

    @Query("Select MAX(c.displayOrder) from CriteriaGroupEntity c")
    Optional<Integer> findHighestDisplayOrder();

    @Query("SELECT cg FROM CriteriaGroupEntity cg LEFT JOIN FETCH cg.criteriaList WHERE cg.id = :id")
    Optional<CriteriaGroupEntity> findByIdWithCriteria(@Param("id") UUID id);

    @Query(
            value = "WITH RankedResults AS ("
                    + "    SELECT cg.id, cg.name, cg.display_order AS cg_display_order, "
                    + "    c.id as c_id, c.criteria_group_id, c.name as c_name, "
                    + "    cc.id as cc_id, cc.criteria_id, cc.display_order, cc.allowed_rules, "
                    + "    ROW_NUMBER() OVER(PARTITION BY cg.id ORDER BY cg.display_order ASC, cg.id) AS row_num "
                    + "    FROM criteria_group cg "
                    + "    JOIN criteria c ON cg.id = c.criteria_group_id "
                    + "    JOIN criteria_config cc ON c.id = cc.criteria_id "
                    + "    WHERE :rule is null OR cc.allowed_rules @> cast(:rule as jsonb) "
                    + ") "
                    + "SELECT id, name, cg_display_order, c_id, criteria_group_id, c_name, cc_id, criteria_id, display_order, allowed_rules "
                    + "FROM RankedResults "
                    + "WHERE row_num = 1 ",
            countQuery = "SELECT count(DISTINCT cg.id) FROM criteria_group cg "
                    + "JOIN criteria c ON cg.id = c.criteria_group_id "
                    + "JOIN criteria_config cc ON c.id = cc.criteria_id "
                    + "WHERE :rule is null OR cc.allowed_rules @> cast(:rule as jsonb)",
            nativeQuery = true)
    Page<CriteriaGroupEntity> findAllWithDetailsByRuleType(Pageable pageable, @Param("rule") String ruleJson);
}
