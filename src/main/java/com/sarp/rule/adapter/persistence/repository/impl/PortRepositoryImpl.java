package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.PortMapper;
import com.sarp.rule.adapter.persistence.repository.PortJpaRepository;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.repository.PortRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class PortRepositoryImpl implements PortRepository {
    private final PortJpaRepository portJpaRepository;
    private final PortMapper portMapper;

    @Override
    public Port findByPortCode(String portCode) {
        return portMapper.toDomain(portJpaRepository.findByPortCode(portCode));
    }

    @Override
    public Port findByPortNamer(String portName) {
        return portMapper.toDomain(portJpaRepository.findByPortName(portName));
    }

    @Override
    public List<Port> findAllByPortCodeIn(List<String> portCodes) {
        return portMapper.toDomain(portJpaRepository.findAllByPortCodeIn(portCodes));
    }
}
