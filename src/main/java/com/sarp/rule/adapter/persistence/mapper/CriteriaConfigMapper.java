package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import com.sarp.rule.domain.entity.CriteriaConfig;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", builder = @Builder)
public interface CriteriaConfigMapper {

    @Mapping(source = "criteriaId", target = "criteria.id")
    CriteriaConfigEntity toCriteriaConfigEntity(CriteriaConfig criteriaConfig);

    @Mapping(source = "criteria.id", target = "criteriaId")
    CriteriaConfig toCriteriaConfigDomain(CriteriaConfigEntity criteriaConfigEntity);
}
