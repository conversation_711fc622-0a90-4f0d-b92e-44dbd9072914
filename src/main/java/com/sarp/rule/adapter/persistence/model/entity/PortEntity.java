package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "port", schema = "commons")
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortEntity {
    @Id
    @Column(name = "port_code", length = 4, nullable = false)
    private String portCode;

    @Column(name = "port_name", length = 100, nullable = false)
    private String portName;

    @Column(name = "city_code", length = 3)
    private String cityCode;

    @Column(name = "longitude", precision = 15, scale = 8)
    private BigDecimal longitude;

    @Column(name = "latitude", precision = 15, scale = 8)
    private BigDecimal latitude;

    @Column(name = "port_type", length = 20, nullable = false)
    private String portType = "AIRPORT";

    @Column(name = "has_comfort")
    private boolean hasComfort;

    @Column(name = "has_convertible_currency")
    private boolean hasConvertibleCurrency;

    @Column(name = "is_round_trip_mandatory")
    private boolean roundTripMandatory;

    @Column(name = "is_domestic")
    private boolean domestic;

    @Column(name = "is_active")
    private boolean active;

    @Column(name = "is_refundable")
    private boolean refundable;

    @Column(name = "is_award")
    private boolean award;

    @Column(name = "is_star_award")
    private boolean starAward;

    @Column(name = "ajet_active")
    private boolean ajetActive;

    @Column(name = "is_spa")
    private boolean spa;

    @Column(name = "is_spa_arrival")
    private boolean spaArrival;

    @Column(name = "is_active_for_web_agent")
    private boolean activeForWebAgent;

    @Column(name = "is_deleted")
    private boolean deleted;
}
