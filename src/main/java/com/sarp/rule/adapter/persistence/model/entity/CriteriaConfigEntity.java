package com.sarp.rule.adapter.persistence.model.entity;

import com.sarp.rule.adapter.persistence.model.converter.RuleTypeListConverter;
import com.sarp.rule.adapter.persistence.model.enums.RuleType;
import jakarta.persistence.*;
import java.util.List;
import java.util.UUID;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "criteria_config")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CriteriaConfigEntity {

    @Id
    private UUID id;

    @Column(name = "display_order", nullable = false, columnDefinition = "int2")
    private Integer displayOrder;

    @JdbcTypeCode(SqlTypes.JSON)
    @Convert(converter = RuleTypeListConverter.class)
    @Column(name = "allowed_rules", columnDefinition = "jsonb")
    private List<RuleType> allowedRules;

    @JdbcTypeCode(SqlTypes.JSON)
    @Convert(converter = RuleTypeListConverter.class)
    @Column(name = "mandatory_rules", columnDefinition = "jsonb")
    private List<RuleType> mandatoryRules;

    @OneToOne()
    @JoinColumn(name = "criteria_id", referencedColumnName = "id")
    private CriteriaEntity criteria;
}
