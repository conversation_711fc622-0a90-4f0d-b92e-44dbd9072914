package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.*;

@Entity
@Table(name = "criteria_group")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CriteriaGroupEntity {
    @Id
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(name = "display_order", nullable = false, unique = true)
    private Integer displayOrder;

    @OneToMany(mappedBy = "criteriaGroup", fetch = FetchType.LAZY)
    private List<CriteriaEntity> criteriaList = new ArrayList<>();
}
