package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Table;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "condition_set_group_template")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConditionSetGroupTemplateEntity extends AuditEntity {

    @Id
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String author;

    @ElementCollection
    @CollectionTable(
            name = "condition_set_group_template_condition_set_group",
            joinColumns = @JoinColumn(name = "condition_set_group_template_id"))
    @Column(name = "condition_set_group_id")
    private Set<UUID> conditionSetGroupIds = new HashSet<>();
}
