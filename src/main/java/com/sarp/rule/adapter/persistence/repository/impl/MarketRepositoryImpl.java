package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.adapter.persistence.model.entity.MarketEntity;
import com.sarp.rule.adapter.persistence.repository.MarketJpaRepository;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class MarketRepositoryImpl implements MarketRepository {
    private final MarketMapper marketMapper;
    private final MarketJpaRepository marketJpaRepository;

    @Override
    public PaginatedResult<Market> findAllMarkets(PagingParams pagingParams) {
        Pageable pageable = PageRequest.of(pagingParams.getPageNumber(), pagingParams.getPageSize());

        Page<MarketEntity> marketPage = marketJpaRepository.findAll(pageable);

        return new PaginatedResult<>(
                marketPage.getContent().stream().map(marketMapper::toDomain).toList(),
                pagingParams.getPageNumber(),
                pagingParams.getPageSize(),
                marketPage.getTotalElements(),
                marketPage.getTotalPages());
    }

    @Override
    public Optional<Market> findById(UUID marketId) {
        return marketJpaRepository.findById(marketId).map(marketMapper::toDomain);
    }

    @Override
    public Market save(Market market) {
        MarketEntity marketEntity = marketMapper.toEntity(market);
        MarketEntity savedMarket = marketJpaRepository.save(marketEntity);
        return marketMapper.toDomain(savedMarket);
    }

    @Override
    public void deleteById(UUID marketId) {
        marketJpaRepository.deleteById(marketId);
    }

    @Override
    public List<Market> findAllMarketsByMarketIdsIn(List<UUID> marketIds) {
        List<MarketEntity> marketEntities = marketJpaRepository.findAllByIdIn(marketIds);
        return marketEntities.stream().map(marketMapper::toDomain).toList();
    }
}
