package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.adapter.persistence.model.entity.ConditionSetGroupTemplateEntity;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ConditionSetGroupTemplateMapper {

    ConditionSetGroupTemplateEntity toEntity(ConditionSetGroupTemplate conditionSetGroupTemplate);

    ConditionSetGroupTemplate toDomain(ConditionSetGroupTemplateEntity conditionSetGroupTemplateEntity);
}
