package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.ConditionNodeDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionNodeDTO;
import com.sarp.rule.adapter.persistence.model.entity.ConditionNodeEntity;
import com.sarp.rule.domain.entity.ConditionNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
        componentModel = "spring",
        uses = {ConditionMapper.class})
public interface ConditionNodeMapper {

    @Mapping(target = "id", ignore = true)
    ConditionNode conditionNodeDtoToConditionNode(ConditionNodeDTO conditionNodeDto);

    ConditionNode updateConditionNodeDtoToConditionNode(UpdateConditionNodeDTO updateConditionNodeDto);

    ConditionNodeEntity conditionNodeToConditionNodeEntity(ConditionNode conditionNode);
}
