package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.*;

@Entity
@Table(name = "condition_set")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConditionSetEntity {
    @Id
    private UUID id;

    @Column(nullable = false)
    private String displayOrder;

    @OneToMany(
            cascade = {CascadeType.PERSIST, CascadeType.MERGE},
            orphanRemoval = true)
    @JoinColumn(name = "condition_set_id", nullable = false)
    @Builder.Default
    private Set<ConditionNodeEntity> conditionNodes = new HashSet<>();

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "condition_set_action",
            joinColumns = @JoinColumn(name = "condition_set_id"),
            inverseJoinColumns = @JoinColumn(name = "action_id"))
    @Builder.Default
    private Set<ActionEntity> actions = new HashSet<>();
}
