package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CriteriaConfigJpaRepository extends JpaRepository<CriteriaConfigEntity, UUID> {
    List<CriteriaConfigEntity> findByCriteriaIdIn(Collection<UUID> criteriaIds);
}
