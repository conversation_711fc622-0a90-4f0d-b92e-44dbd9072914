package com.sarp.rule.adapter.persistence.repository.impl;

import com.querydsl.core.types.Predicate;
import com.sarp.rule.adapter.persistence.mapper.ProductMapper;
import com.sarp.rule.adapter.persistence.model.entity.ProductEntity;
import com.sarp.rule.adapter.persistence.model.entity.QProductEntity;
import com.sarp.rule.adapter.persistence.repository.ProductJpaRepository;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.repository.ProductRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortDirection;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import io.github.perplexhub.rsql.RSQLQueryDslSupport;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProductRepositoryImpl implements ProductRepository {

    private static final String DEFAULT_SORT_FIELD = "id";
    private static final Sort.Direction DEFAULT_SORT_DIRECTION = Sort.Direction.DESC;

    private final ProductJpaRepository productJpaRepository;
    private final ProductMapper productMapper;

    @Override
    public PaginatedResult<Product> findProductsMatchingCriteria(
            String rsqlQuery, PagingParams pagingParams, SortingParams sortingParams) {

        Predicate predicate = buildPredicate(rsqlQuery);
        Pageable pageable = buildPageable(pagingParams, sortingParams);

        Page<ProductEntity> productPage = executeQuery(predicate, pageable);

        return mapToPaginatedResult(productPage, pagingParams);
    }

    @Override
    public List<UUID> findAllVariantIdsByProductEntityIds(List<UUID> productEntityIds) {
        return productJpaRepository.findVariantIdsByProductEntityIds(productEntityIds);
    }

    @Override
    public List<Product> findAllByIdIn(List<UUID> ids) {
        return productJpaRepository.findAllByIdIn(ids).stream()
                .map(productMapper::toDomain)
                .toList();
    }

    @Override
    public List<Product> findAllByVariantIdIn(List<UUID> variantIds) {
        return productJpaRepository.findAllByVariantIdIn(variantIds).stream()
                .map(productMapper::toDomain)
                .toList();
    }

    @Override
    public List<Product> saveAll(List<Product> products) {
        List<ProductEntity> productEntities =
                products.stream().map(productMapper::toEntity).toList();

        List<ProductEntity> savedProductEntities = productJpaRepository.saveAll(productEntities);

        return savedProductEntities.stream().map(productMapper::toDomain).toList();
    }

    private Predicate buildPredicate(String rsqlQuery) {
        if (rsqlQuery == null || rsqlQuery.trim().isEmpty()) {
            return null;
        }
        return RSQLQueryDslSupport.toPredicate(rsqlQuery, QProductEntity.productEntity);
    }

    private Pageable buildPageable(PagingParams pagingParams, SortingParams sortingParams) {
        Sort sort = buildSort(sortingParams);
        return PageRequest.of(pagingParams.getPageNumber(), pagingParams.getPageSize(), sort);
    }

    private Sort buildSort(SortingParams sortingParams) {
        if (sortingParams == null || sortingParams.isEmpty()) {
            return getDefaultSort();
        }

        List<Sort.Order> orders = sortingParams.getSortFields().stream()
                .map(sortField -> sortField.getDirection() == SortDirection.ASC
                        ? Sort.Order.asc(sortField.getFieldName())
                        : Sort.Order.desc(sortField.getFieldName()))
                .toList();

        return Sort.by(orders);
    }

    private Sort getDefaultSort() {
        return Sort.by(DEFAULT_SORT_DIRECTION, DEFAULT_SORT_FIELD);
    }

    private Page<ProductEntity> executeQuery(Predicate predicate, Pageable pageable) {
        return predicate != null
                ? productJpaRepository.findAll(predicate, pageable)
                : productJpaRepository.findAll(pageable);
    }

    private PaginatedResult<Product> mapToPaginatedResult(Page<ProductEntity> page, PagingParams pagingParams) {
        List<Product> products =
                page.getContent().stream().map(productMapper::toDomain).toList();

        return new PaginatedResult<>(
                products,
                pagingParams.getPageNumber(),
                pagingParams.getPageSize(),
                page.getTotalElements(),
                page.getTotalPages());
    }
}
