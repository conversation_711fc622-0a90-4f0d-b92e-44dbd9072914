package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionSetGroupDTO;
import com.sarp.rule.adapter.persistence.model.entity.ConditionSetGroupEntity;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper interface for converting between RuleSetDTO and RuleSet domain entity.
 *
 * <p>This mapper delegates the mapping of nested objects (rules, conditionSet) to other specialized
 * mappers defined in the 'uses' clause.
 */
@Mapper(
        componentModel = "spring",
        uses = {
            ConditionSetMapper.class,
        })
public interface ConditionSetGroupMapper {

    @Mapping(target = "id", ignore = true)
    ConditionSetGroup conditionSetGroupDtoToConditionSetGroup(ConditionSetGroupDTO conditionSetGroupDto);

    ConditionSetGroup updateConditionSetGroupDtoToConditionSetGroup(
            UpdateConditionSetGroupDTO updateConditionSetGroupDTO);

    ConditionSetGroupEntity toEntity(ConditionSetGroup conditionSetGroup);

    ConditionSetGroup toDomain(ConditionSetGroupEntity conditionSetGroupEntity);
}
