package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "market")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketEntity extends AuditEntity {
    @Id
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @ManyToMany(
            cascade = {CascadeType.PERSIST, CascadeType.MERGE},
            targetEntity = PortEntity.class)
    @JoinTable(
            name = "market_ports",
            joinColumns = @JoinColumn(name = "market_id"),
            inverseJoinColumns = @JoinColumn(name = "port_code", referencedColumnName = "port_code"))
    @Builder.Default
    private Set<PortEntity> ports = new HashSet<>();
}
