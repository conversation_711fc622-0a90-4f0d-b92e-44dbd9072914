package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.model.entity.MarketEntity;
import com.sarp.rule.domain.entity.Market;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {PortMapper.class})
public interface MarketMapper {
    Market toDomain(MarketEntity market);

    MarketEntity toEntity(Market market);

    MarketResponseDTO toMarketResponseDTO(Market market);
}
