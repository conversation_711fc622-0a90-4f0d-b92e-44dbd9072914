package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.MarketEntity;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MarketJpaRepository extends JpaRepository<MarketEntity, UUID> {
    MarketEntity findByName(String name);

    List<MarketEntity> findAllByIdIn(Collection<UUID> ids);
}
