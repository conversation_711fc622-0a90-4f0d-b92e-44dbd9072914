package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.CreateCriteriaRequestDTO;
import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.domain.entity.Criteria;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", builder = @Builder)
public interface CriteriaMapper {

    @Named("toCriteriaEntity")
    @Mapping(source = "criteriaGroupId", target = "criteriaGroup.id")
    CriteriaEntity toCriteriaEntity(Criteria criteria);

    @Named("toCriteriaDomain")
    @Mapping(source = "criteriaGroup.id", target = "criteriaGroupId")
    Criteria toCriteriaDomain(CriteriaEntity criteriaEntity);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "startDateTime", target = "startDateTime", qualifiedByName = "instantToLocalDateTime")
    @Mapping(source = "endDateTime", target = "endDateTime", qualifiedByName = "instantToLocalDateTime")
    Criteria toCriteriaDomain(CreateCriteriaRequestDTO criteriaRequestDTO);

    @Mapping(source = "startDateTime", target = "startDateTime", qualifiedByName = "localDateTimeToInstant")
    @Mapping(source = "endDateTime", target = "endDateTime", qualifiedByName = "localDateTimeToInstant")
    CriteriaResponseDTO toCriteriaResponseDTO(Criteria criteria);

    @Named("instantToLocalDateTime")
    static LocalDateTime instantToLocalDateTime(Instant instant) {
        return instant == null ? null : LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    @Named("localDateTimeToInstant")
    static Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime == null
                ? null
                : localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }
}
