package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.adapter.persistence.model.entity.BundleEntity;
import com.sarp.rule.adapter.persistence.repository.BundleJpaRepository;
import com.sarp.rule.adapter.persistence.util.PaginatedResultMapper;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.repository.BundleRepository;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class BundleRepositoryImpl implements BundleRepository {

    private final BundleJpaRepository bundleJpaRepository;
    private final BundleMapper bundleMapper;

    @Override
    public Bundle save(Bundle bundle) {
        BundleEntity bundleEntity = bundleMapper.toEntity(bundle);
        BundleEntity savedEntity = bundleJpaRepository.save(bundleEntity);
        return bundleMapper.toDomain(savedEntity);
    }

    @Override
    public void delete(Bundle bundle) {
        bundleJpaRepository.delete(bundleMapper.toEntity(bundle));
    }

    @Override
    public List<Bundle> saveAll(List<Bundle> bundles) {
        List<BundleEntity> bundleEntities =
                bundles.stream().map(bundleMapper::toEntity).toList();
        List<BundleEntity> savedEntities = bundleJpaRepository.saveAll(bundleEntities);
        return savedEntities.stream().map(bundleMapper::toDomain).toList();
    }

    @Override
    public Optional<Bundle> findById(UUID id) {
        return bundleJpaRepository.findById(id).map(bundleMapper::toDomain);
    }

    @Override
    public PaginatedResult<Bundle> findAllByStatus(BundleStatus status, PagingParams params) {
        Pageable pageable = toPageable(params);
        Page<BundleEntity> entityPage =
                bundleJpaRepository.findAllByStatus(bundleMapper.toBundleStatus(status), pageable);
        return PaginatedResultMapper.map(entityPage, bundleMapper::toDomain);
    }

    @Override
    public PaginatedResult<Bundle> findAllByStatusNot(BundleStatus bundleStatus, PagingParams params) {
        Pageable pageable = toPageable(params);
        Page<BundleEntity> entityPage =
                bundleJpaRepository.findAllByStatusNot(bundleMapper.toBundleStatus(bundleStatus), pageable);
        return PaginatedResultMapper.map(entityPage, bundleMapper::toDomain);
    }

    private Pageable toPageable(PagingParams params) {
        return PageRequest.of(params.getPageNumber(), params.getPageSize());
    }

    @Override
    public List<Bundle> findAllById(List<UUID> ids) {
        return bundleJpaRepository.findAllById(ids).stream()
                .map(bundleMapper::toDomain)
                .toList();
    }

    @Override
    public List<Bundle> findAllActiveByIdIn(List<UUID> ids) {
        return bundleJpaRepository
                .findAllByIdInAndStatus(ids, bundleMapper.toBundleStatus(BundleStatus.ACTIVE))
                .stream()
                .map(bundleMapper::toDomain)
                .toList();
    }
}
