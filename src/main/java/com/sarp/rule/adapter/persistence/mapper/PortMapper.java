package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.adapter.persistence.model.entity.PortEntity;
import com.sarp.rule.domain.entity.Port;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface PortMapper {
    Port toDomain(PortEntity port);

    List<Port> toDomain(List<PortEntity> portEntities);

    PortEntity toEntity(Port port);
}
