package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.RuleEntity;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

public interface RuleJpaRepository
        extends JpaRepository<RuleEntity, UUID>,
                JpaSpecificationExecutor<RuleEntity>,
                QuerydslPredicateExecutor<RuleEntity> {
    List<RuleEntity> findAllByConditionSetGroupsId(UUID conditionSetGroupId);
}
