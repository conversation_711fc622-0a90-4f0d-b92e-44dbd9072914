package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.PortEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface PortJpaRepository extends JpaRepository<PortEntity, String> {
    PortEntity findByPortCode(String portCode);

    PortEntity findByPortName(String portName);

    List<PortEntity> findAllByPortCodeIn(List<String> portName);
}
