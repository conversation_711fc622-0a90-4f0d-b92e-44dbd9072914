package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.*;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.adapter.persistence.model.enums.RuleType;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.valueobject.criteria.CriteriaWithConfigDetails;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(
        componentModel = "spring",
        builder = @Builder(),
        uses = {CriteriaMapper.class})
public interface CriteriaGroupMapper {

    @Mapping(source = "criteriaList", target = "criteriaList", qualifiedByName = "toCriteriaEntity")
    CriteriaGroupEntity toEntity(CriteriaGroup criteriaGroup);

    @Mapping(source = "criteriaList", target = "criteriaList", qualifiedByName = "toCriteriaDomain")
    CriteriaGroup toDomain(CriteriaGroupEntity criteriaGroupEntity);

    @Named("withoutCriteriaList")
    @Mapping(target = "criteriaList", ignore = true)
    CriteriaGroup toDomainWithoutCriteriaList(CriteriaGroupEntity criteriaGroupEntity);

    @Mapping(target = "criteriaList", ignore = true)
    CriteriaGroupEntity toEntityWithoutCriteriaList(CriteriaGroup criteriaGroup);

    @Mapping(target = "id")
    CriteriaGroupResponseDTO toCriteriaGroupResponseDTO(CriteriaGroup criteriaGroup);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "displayOrder", ignore = true)
    @Mapping(target = "criteriaList", ignore = true)
    CriteriaGroup toDomain(CreateCriteriaGroupRequestDTO createCriteriaGroupRequestDTO);

    @Mapping(target = "id")
    CriteriaDTO toCriteriaDTO(Criteria criteria);

    @Mapping(target = "id")
    @Mapping(target = "criteriaList", expression = "java(toCriteriaDTOList(criteriaGroup.getCriteriaList()))")
    CriteriaGroupWithCriteriaResponseDTO toCriteriaGroupWithCriteriaResponseDTO(CriteriaGroup criteriaGroup);

    @Mapping(target = "id")
    CriteriaGroup toDomain(UUID id, UpdateCriteriaGroupRequestDTO updateCriteriaGroupRequestDTO);

    CriteriaGroup toDomain(Integer displayOrder, CreateCriteriaGroupRequestDTO createCriteriaGroupRequestDTO);

    @Mapping(source = "criteriaList", target = "criteriaDetails")
    CriteriaGroupWithDetails entityToDetails(CriteriaGroupEntity entity);

    @Mapping(source = "id", target = "criteriaId")
    @Mapping(source = "name", target = "criteriaName")
    @Mapping(target = "displayOrder", source = "criteriaConfig.displayOrder")
    @Mapping(target = "allowedRules", source = "criteriaConfig.allowedRules")
    @Mapping(target = "mandatoryRules", source = "criteriaConfig.mandatoryRules")
    CriteriaWithConfigDetails entityToDetails(CriteriaEntity entity);

    default List<CriteriaDTO> toCriteriaDTOList(List<Criteria> criteriaList) {
        if (criteriaList == null) return Collections.emptyList();

        return criteriaList.stream().map(this::toCriteriaDTO).toList();
    }

    default List<CriteriaGroupWithCriteriaResponseDTO> toCriteriaGroupWithCriteriaResponseDTOList(
            List<CriteriaGroup> criteriaGroups) {
        if (criteriaGroups == null) return Collections.emptyList();

        return criteriaGroups.stream()
                .map(this::toCriteriaGroupWithCriteriaResponseDTO)
                .toList();
    }

    default RuleType toRuleType(com.sarp.rule.domain.valueobject.rule.RuleType domainRuleType) {
        return RuleType.valueOf(domainRuleType.name());
    }

    default Instant map(LocalDateTime value) {
        return value != null ? value.atZone(ZoneId.systemDefault()).toInstant() : null;
    }

    default LocalDateTime map(Instant value) {
        return value != null ? LocalDateTime.ofInstant(value, ZoneId.systemDefault()) : null;
    }
}
