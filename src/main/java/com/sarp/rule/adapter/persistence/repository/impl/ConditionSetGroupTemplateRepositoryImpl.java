package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupTemplateMapper;
import com.sarp.rule.adapter.persistence.model.entity.ConditionSetGroupTemplateEntity;
import com.sarp.rule.adapter.persistence.repository.ConditionSetGroupTemplateJpaRepository;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.repository.ConditionSetGroupTemplateRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConditionSetGroupTemplateRepositoryImpl implements ConditionSetGroupTemplateRepository {

    private final ConditionSetGroupTemplateJpaRepository conditionSetGroupTemplateJpaRepository;
    private final ConditionSetGroupTemplateMapper conditionSetGroupTemplateMapper;

    @Override
    public ConditionSetGroupTemplate save(ConditionSetGroupTemplate template) {
        ConditionSetGroupTemplateEntity entity = conditionSetGroupTemplateMapper.toEntity(template);
        ConditionSetGroupTemplateEntity savedEntity = conditionSetGroupTemplateJpaRepository.save(entity);
        return conditionSetGroupTemplateMapper.toDomain(savedEntity);
    }

    @Override
    public Optional<ConditionSetGroupTemplate> findById(UUID id) {
        return conditionSetGroupTemplateJpaRepository.findById(id).map(conditionSetGroupTemplateMapper::toDomain);
    }

    @Override
    public void deleteById(UUID id) {
        conditionSetGroupTemplateJpaRepository.deleteById(id);
    }

    @Override
    public PaginatedResult<ConditionSetGroupTemplate> findAll(PagingParams pagingParams) {
        Pageable pageable = PageRequest.of(pagingParams.getPageNumber(), pagingParams.getPageSize());
        Page<ConditionSetGroupTemplateEntity> entiPage = conditionSetGroupTemplateJpaRepository.findAll(pageable);
        return mapToPaginatedResult(entiPage);
    }

    private PaginatedResult<ConditionSetGroupTemplate> mapToPaginatedResult(
            Page<ConditionSetGroupTemplateEntity> entityPage) {
        List<ConditionSetGroupTemplate> conditionSetGroupTemplates = entityPage.getContent().stream()
                .map(conditionSetGroupTemplateMapper::toDomain)
                .toList();
        return new PaginatedResult<>(
                conditionSetGroupTemplates,
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.getTotalElements(),
                entityPage.getTotalPages());
    }
}
