package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.*;
import java.util.UUID;
import lombok.*;

@Entity
@Table(name = "bundle_product")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BundleProductEntity {
    @Id
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "bundle_id")
    private BundleEntity bundle;

    @ManyToOne
    @JoinColumn(name = "product_id")
    private ProductEntity product;

    private Integer quantity;
}
