package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.ConditionDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionDTO;
import com.sarp.rule.adapter.persistence.model.entity.ConditionEntity;
import com.sarp.rule.domain.entity.Condition;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = CriteriaMapper.class)
public interface ConditionMapper {

    @Mapping(target = "id", ignore = true)
    Condition conditionDtoToCondition(ConditionDTO conditionDto);

    Condition updateConditionDtoToCondition(UpdateConditionDTO updateConditionDTO);

    ConditionEntity toConditionEntity(Condition condition);

    Condition toConditionDomain(ConditionEntity conditionEntity);

    default List<String> map(List<Object> value) {
        if (value == null) {
            return Collections.emptyList();
        }
        return value.stream().map(Object::toString).toList();
    }

    default List<ConditionEntity> toEntityList(List<Condition> conditions) {
        if (conditions == null) {
            return Collections.emptyList();
        }
        return conditions.stream().map(this::toConditionEntity).toList();
    }

    default List<Condition> toDomainList(List<ConditionEntity> entities) {
        if (entities == null) {
            return Collections.emptyList();
        }
        return entities.stream().map(this::toConditionDomain).toList();
    }
}
