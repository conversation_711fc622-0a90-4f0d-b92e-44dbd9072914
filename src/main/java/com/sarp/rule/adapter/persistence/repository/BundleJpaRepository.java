package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.BundleEntity;
import com.sarp.rule.adapter.persistence.model.enums.BundleStatus;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BundleJpaRepository extends JpaRepository<BundleEntity, UUID> {
    Page<BundleEntity> findAllByStatus(BundleStatus status, Pageable pageable);

    Page<BundleEntity> findAllByStatusNot(BundleStatus bundleStatus, Pageable pageable);

    List<BundleEntity> findAllByIdInAndStatus(List<UUID> ids, BundleStatus status);
}
