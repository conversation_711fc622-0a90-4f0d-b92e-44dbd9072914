package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.ActionDTO;
import com.sarp.generated.openapi.api.dto.UpdateActionDTO;
import com.sarp.rule.adapter.persistence.model.entity.ActionEntity;
import com.sarp.rule.domain.entity.Action;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
        componentModel = "spring",
        uses = {ParametersMapper.class})
public interface ActionMapper {

    @Mapping(target = "id", ignore = true)
    Action actionDtoToAction(ActionDTO actionDto);

    Action updateActionDtoToAction(UpdateActionDTO updateActionDTO);

    ActionEntity toEntity(Action action);

    Action toDomain(ActionEntity actionEntity);
}
