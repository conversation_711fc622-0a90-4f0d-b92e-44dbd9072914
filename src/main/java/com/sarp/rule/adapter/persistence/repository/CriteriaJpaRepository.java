package com.sarp.rule.adapter.persistence.repository;

import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import java.util.Collection;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CriteriaJpaRepository extends JpaRepository<CriteriaEntity, UUID> {
    long countCriteriaEntityByCriteriaGroup_Id(UUID criteriaGroupId);

    long countByIdIn(Collection<UUID> ids);
}
