package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.CriteriaConfigMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaConfigJpaRepository;
import com.sarp.rule.domain.entity.CriteriaConfig;
import com.sarp.rule.domain.repository.CriteriaConfigRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CriteriaConfigRepositoryImpl implements CriteriaConfigRepository {

    private final CriteriaConfigJpaRepository criteriaConfigJpaRepository;
    private final CriteriaConfigMapper criteriaConfigMapper;

    @Override
    public CriteriaConfig save(CriteriaConfig criteriaConfig) {
        CriteriaConfigEntity criteriaEntity = criteriaConfigMapper.toCriteriaConfigEntity(criteriaConfig);
        CriteriaConfigEntity savedCriteriaEntity = criteriaConfigJpaRepository.save(criteriaEntity);
        return criteriaConfigMapper.toCriteriaConfigDomain(savedCriteriaEntity);
    }

    @Override
    public List<CriteriaConfig> findByCriteriaIdIn(List<UUID> criteriaIds) {
        return criteriaConfigJpaRepository.findByCriteriaIdIn(criteriaIds).stream()
                .map(criteriaConfigMapper::toCriteriaConfigDomain)
                .toList();
    }

    @Override
    public void saveAll(List<CriteriaConfig> criteriaConfigs) {
        criteriaConfigJpaRepository.saveAll(criteriaConfigs.stream()
                .map(criteriaConfigMapper::toCriteriaConfigEntity)
                .toList());
    }
}
