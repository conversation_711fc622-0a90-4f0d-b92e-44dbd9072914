package com.sarp.rule.adapter.persistence.model.entity;

import com.sarp.rule.adapter.persistence.model.converter.AllowedOperatorListConverter;
import com.sarp.rule.adapter.persistence.model.enums.*;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import lombok.*;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "criteria")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CriteriaEntity extends AuditEntity {

    @Id
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String description;

    @Column(nullable = false)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private CriteriaType type;

    @Column(name = "request_type", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private RequestType requestType;

    @Column(name = "mapping_field")
    private String mappingField;

    @Column(name = "system_defined_criteria_type")
    @ColumnTransformer(write = "?::system_defined_criteria_type")
    private String systemDefinedCriteriaType;

    @Column(name = "field_type", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private FieldType fieldType;

    @Column(name = "selection_type")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private SelectionType selectionType;

    @Column(name = "allowed_values", columnDefinition = "text[]")
    @JdbcTypeCode(SqlTypes.ARRAY)
    private List<String> allowedValues;

    @Column(name = "min_value")
    private BigDecimal minValue;

    @Column(name = "max_value")
    private BigDecimal maxValue;

    @Column(name = "start_date_time")
    private LocalDateTime startDateTime;

    @Column(name = "end_date_time")
    private LocalDateTime endDateTime;

    @Column(name = "start_time")
    private LocalTime startTime;

    @Column(name = "end_time")
    private LocalTime endTime;

    @ManyToOne(optional = false)
    @JoinColumn(name = "criteria_group_id", nullable = false)
    private CriteriaGroupEntity criteriaGroup;

    @OneToOne(mappedBy = "criteria")
    private CriteriaConfigEntity criteriaConfig;

    @JdbcTypeCode(SqlTypes.JSON)
    @Convert(converter = AllowedOperatorListConverter.class)
    @Column(name = "allowed_operators", columnDefinition = "jsonb")
    private List<ComparisonOperator> allowedOperators;
}
