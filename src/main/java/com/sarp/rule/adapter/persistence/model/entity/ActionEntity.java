package com.sarp.rule.adapter.persistence.model.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.sarp.rule.adapter.persistence.model.enums.ActionType;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "action")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActionEntity {
    @Id
    private UUID id;

    @Column(name = "action_type", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private ActionType actionType;

    @Column(name = "parameters", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private JsonNode parameters;

    @Column(name = "rule_id", nullable = false)
    private UUID ruleId;
}
