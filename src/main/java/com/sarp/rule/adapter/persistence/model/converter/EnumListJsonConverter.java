package com.sarp.rule.adapter.persistence.model.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.List;

@Converter
public abstract class EnumListJsonConverter<E extends Enum<E>> implements AttributeConverter<List<E>, String> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final Class<E> enumType;

    protected EnumListJsonConverter(Class<E> enumType) {
        this.enumType = enumType;
    }

    @Override
    public String convertToDatabaseColumn(List<E> attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (Exception e) {
            throw new IllegalArgumentException("Error converting enum list to JSON", e);
        }
    }

    @Override
    public List<E> convertToEntityAttribute(String dbData) {
        try {
            return objectMapper.readValue(
                    dbData, objectMapper.getTypeFactory().constructCollectionType(List.class, enumType));
        } catch (Exception e) {
            throw new IllegalArgumentException("Error converting JSON to enum list", e);
        }
    }
}
