package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.CriteriaMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaJpaRepository;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.repository.CriteriaRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CriteriaRepositoryImpl implements CriteriaRepository {

    private final CriteriaJpaRepository criteriaJpaRepository;
    private final CriteriaMapper criteriaMapper;

    @Override
    public Criteria save(Criteria criteria) {
        CriteriaEntity criteriaEntity = criteriaMapper.toCriteriaEntity(criteria);
        CriteriaEntity savedCriteriaEntity = criteriaJpaRepository.save(criteriaEntity);
        return criteriaMapper.toCriteriaDomain(savedCriteriaEntity);
    }

    @Override
    public boolean existsById(UUID id) {
        return criteriaJpaRepository.existsById(id);
    }

    @Override
    public Criteria update(Criteria criteria) {
        return save(criteria);
    }

    @Override
    public Optional<Criteria> findById(UUID id) {
        return criteriaJpaRepository.findById(id).map(criteriaMapper::toCriteriaDomain);
    }

    @Override
    public long countByIds(List<UUID> ids) {
        return criteriaJpaRepository.countByIdIn(ids);
    }

    @Override
    public long countByCriteriaGroupId(UUID criteriaGroupId) {
        return criteriaJpaRepository.countCriteriaEntityByCriteriaGroup_Id(criteriaGroupId);
    }

    @Override
    public void deleteById(UUID id) {
        criteriaJpaRepository.deleteById(id);
    }

    @Override
    public List<Criteria> findAllById(List<UUID> ids) {
        return criteriaJpaRepository.findAllById(ids).stream()
                .map(criteriaMapper::toCriteriaDomain)
                .toList();
    }
}
