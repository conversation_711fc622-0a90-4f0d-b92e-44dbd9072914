package com.sarp.rule.adapter.persistence.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "product")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductEntity {

    @Id
    private UUID id;

    @Column(nullable = false)
    private UUID productId;

    @Column
    private UUID variantId;

    @Column(nullable = false)
    private String name;

    @Column
    private String iataCode;

    @Column
    private String supplierName;

    @Column
    private String retailerName;

    @Column(nullable = false)
    private String categoryName;

    @Column
    private String parentCategoryName;

    @Column
    private String description;
}
