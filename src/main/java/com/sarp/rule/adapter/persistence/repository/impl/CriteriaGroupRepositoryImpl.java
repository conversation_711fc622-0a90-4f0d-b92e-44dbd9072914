package com.sarp.rule.adapter.persistence.repository.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.adapter.persistence.mapper.CriteriaGroupMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaGroupJpaRepository;
import com.sarp.rule.adapter.persistence.util.PaginatedResultMapper;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CriteriaGroupRepositoryImpl implements CriteriaGroupRepository {
    private final CriteriaGroupJpaRepository criteriaGroupJpaRepository;
    private final CriteriaGroupMapper criteriaGroupMapper;

    @Override
    public CriteriaGroup save(CriteriaGroup criteriaGroup) {
        return criteriaGroupMapper.toDomain(
                criteriaGroupJpaRepository.save(criteriaGroupMapper.toEntityWithoutCriteriaList(criteriaGroup)));
    }

    @Override
    public Optional<CriteriaGroup> findByIdWithoutCriteriaList(UUID id) {
        return criteriaGroupJpaRepository.findById(id).map(criteriaGroupMapper::toDomainWithoutCriteriaList);
    }

    public Integer getHighestDisplayOrder() {
        return criteriaGroupJpaRepository.findHighestDisplayOrder().orElse(0);
    }

    @Override
    public boolean existsById(UUID id) {
        return criteriaGroupJpaRepository.existsById(id);
    }

    @Override
    public void deleteById(UUID id) {
        criteriaGroupJpaRepository.deleteById(id);
    }

    @Override
    public Optional<CriteriaGroup> findByIdWithCriteria(UUID id) {
        return criteriaGroupJpaRepository.findByIdWithCriteria(id).map(criteriaGroupMapper::toDomain);
    }

    @Override
    public PaginatedResult<CriteriaGroup> findAllWithCriteriaDetails(PagingParams pagingParams) {
        Pageable pageable = PageRequest.of(
                pagingParams.getPageNumber(), pagingParams.getPageSize(), Sort.by(Sort.Direction.ASC, "displayOrder"));
        Page<CriteriaGroupEntity> entityPage = criteriaGroupJpaRepository.findAll(pageable);
        return PaginatedResultMapper.map(entityPage, criteriaGroupMapper::toDomain);
    }

    @Override
    public PaginatedResult<CriteriaGroupWithDetails> findAllWithDetailsByRuleType(
            RuleType ruleType, PagingParams pagingParams) {
        Pageable pageable = PageRequest.of(
                pagingParams.getPageNumber(), pagingParams.getPageSize(), Sort.by(Sort.Direction.ASC, "display_order"));

        String ruleTypeJsonStr = null;
        if (ruleType != null) {
            List<String> ruleTypes =
                    List.of(criteriaGroupMapper.toRuleType(ruleType).name());
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                ruleTypeJsonStr = objectMapper.writeValueAsString(ruleTypes);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        Page<CriteriaGroupEntity> entityPage =
                criteriaGroupJpaRepository.findAllWithDetailsByRuleType(pageable, ruleTypeJsonStr);
        return PaginatedResultMapper.map(entityPage, criteriaGroupMapper::entityToDetails);
    }

    @Override
    public List<CriteriaGroup> updateAll(List<CriteriaGroup> criteriaGroups) {
        List<CriteriaGroupEntity> entities = criteriaGroups.stream()
                .map(criteriaGroupMapper::toEntityWithoutCriteriaList)
                .toList();
        List<CriteriaGroupEntity> updatedEntities = criteriaGroupJpaRepository.saveAll(entities);
        return updatedEntities.stream()
                .map(criteriaGroupMapper::toDomainWithoutCriteriaList)
                .toList();
    }

    @Override
    public List<CriteriaGroup> findAllById(List<UUID> ids) {
        return criteriaGroupJpaRepository.findAllById(ids).stream()
                .map(criteriaGroupMapper::toDomain)
                .toList();
    }

    @Override
    public void saveAll(List<CriteriaGroup> criteriaGroups) {
        criteriaGroupJpaRepository.saveAll(criteriaGroups.stream()
                .map(criteriaGroupMapper::toEntityWithoutCriteriaList)
                .toList());
    }
}
