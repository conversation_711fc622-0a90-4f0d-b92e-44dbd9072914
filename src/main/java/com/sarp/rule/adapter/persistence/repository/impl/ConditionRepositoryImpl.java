package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.ConditionMapper;
import com.sarp.rule.adapter.persistence.repository.ConditionJpaRepository;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.repository.ConditionRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConditionRepositoryImpl implements ConditionRepository {
    private final ConditionJpaRepository conditionJpaRepository;
    private final ConditionMapper conditionMapper;

    @Override
    public boolean existsByCriteriaId(UUID id) {
        return conditionJpaRepository.existsByCriteriaId(id);
    }

    @Override
    public List<Condition> saveAll(List<Condition> conditions) {
        return conditionMapper.toDomainList(conditionJpaRepository.saveAll(conditionMapper.toEntityList(conditions)));
    }
}
