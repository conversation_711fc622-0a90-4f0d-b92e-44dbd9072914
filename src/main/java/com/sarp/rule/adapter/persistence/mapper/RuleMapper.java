package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.adapter.persistence.model.entity.RuleEntity;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ConditionSetGroupMapper.class, BundleMapper.class, ProductMapper.class})
public interface RuleMapper {

    /**
     * Converts a RuleEntity to its domain model representation
     *
     * @param ruleEntity the entity to convert
     * @return the domain model representation
     */
    @Mapping(target = "effectiveDates", source = "ruleEntity", qualifiedByName = "mapEffectiveDates")
    Rule toDomain(RuleEntity ruleEntity);

    /**
     * Converts a Rule to its entity representation
     *
     * @param rule the domain model to convert
     * @return the entity representation
     */
    @Mapping(target = "effectiveDateFrom", source = "effectiveDates.from")
    @Mapping(target = "effectiveDateTo", source = "effectiveDates.to")
    @Mapping(target = "bundles", source = "bundles", qualifiedByName = "toEntity")
    RuleEntity toEntity(Rule rule);

    @Named("mapEffectiveDates")
    default EffectiveDates mapEffectiveDates(RuleEntity ruleEntity) {
        if (ruleEntity == null) {
            return null;
        }
        return new EffectiveDates(ruleEntity.getEffectiveDateFrom(), ruleEntity.getEffectiveDateTo());
    }
}
