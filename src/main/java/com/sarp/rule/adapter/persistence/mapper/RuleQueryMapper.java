package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.rule.domain.command.rule.RuleByIdQuery;
import java.util.UUID;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", builder = @Builder())
public class RuleQueryMapper {

    public RuleByIdQuery toQuery(UUID ruleSetBundleConfigId) {
        return new RuleByIdQuery(ruleSetBundleConfigId);
    }
}
