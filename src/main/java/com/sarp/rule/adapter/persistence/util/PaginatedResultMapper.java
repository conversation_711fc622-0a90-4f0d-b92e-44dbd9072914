package com.sarp.rule.adapter.persistence.util;

import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.function.Function;
import org.springframework.data.domain.Page;

public final class PaginatedResultMapper {

    private PaginatedResultMapper() {
        // Utility class; prevent instantiation
    }

    public static <E, D> PaginatedResult<D> map(Page<E> entityPage, Function<? super E, D> toDomainMapper) {
        List<D> domainItems =
                entityPage.getContent().stream().map(toDomainMapper).toList();

        return new PaginatedResult<>(
                domainItems,
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.getTotalElements(),
                entityPage.getTotalPages());
    }
}
