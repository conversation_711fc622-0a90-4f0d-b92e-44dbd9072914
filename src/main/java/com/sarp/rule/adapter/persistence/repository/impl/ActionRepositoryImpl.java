package com.sarp.rule.adapter.persistence.repository.impl;

import com.sarp.rule.adapter.persistence.mapper.ActionMapper;
import com.sarp.rule.adapter.persistence.repository.ActionJpaRepository;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.repository.ActionRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ActionRepositoryImpl implements ActionRepository {

    private final ActionJpaRepository actionJpaRepository;
    private final ActionMapper actionMapper;

    @Override
    public List<Action> findByIds(List<UUID> actionIds) {
        return actionJpaRepository.findAllByIdIn(actionIds).stream()
                .map(actionMapper::toDomain)
                .toList();
    }
}
