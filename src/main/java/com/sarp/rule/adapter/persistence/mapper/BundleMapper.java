package com.sarp.rule.adapter.persistence.mapper;

import com.sarp.generated.openapi.api.dto.BundleDTO;
import com.sarp.generated.openapi.api.dto.BundleResponseDTO;
import com.sarp.generated.openapi.api.dto.VariantDTO;
import com.sarp.rule.adapter.persistence.model.entity.BundleEntity;
import com.sarp.rule.adapter.persistence.model.entity.BundleProductEntity;
import com.sarp.rule.adapter.persistence.model.enums.BundleStatus;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.mapstruct.*;

@Mapper(
        componentModel = "spring",
        builder = @Builder,
        uses = {ProductMapper.class})
public interface BundleMapper {

    @Named("toEntity")
    default BundleEntity toEntity(Bundle bundle) {
        BundleEntity entity = toEntityWithoutProducts(bundle);
        if (entity != null && bundle != null) {
            entity.setBundleProducts(mapProductsWithQuantities(bundle.getProductWithQuantities(), entity));
        }
        return entity;
    }

    @Mapping(target = "bundleProducts", ignore = true)
    BundleEntity toEntityWithoutProducts(Bundle bundle);

    @Mapping(target = "productWithQuantities", source = "bundleProducts")
    Bundle toDomain(BundleEntity bundleEntity);

    default BundleStatus toBundleStatus(com.sarp.rule.domain.valueobject.bundle.BundleStatus domainBundleStatus) {
        return BundleStatus.valueOf(domainBundleStatus.name());
    }

    @Mapping(target = "productWithQuantities", source = "variants", qualifiedByName = "variantsToProductWithQuantities")
    Bundle bundleDTOToBundle(BundleDTO bundleDTO);

    @Mapping(source = "productWithQuantities", target = "productsWithQuantities")
    BundleResponseDTO bundleToBundleResponseDTO(Bundle bundle);

    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "product", source = "product")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "bundle", ignore = true)
    BundleProductEntity toBundleProductEntity(ProductWithQuantity productWithQuantity);

    @Mapping(target = "product", source = "product")
    @Mapping(target = "quantity", source = "quantity")
    ProductWithQuantity toProductWithQuantity(BundleProductEntity bundleProductEntity);

    @Named("variantsToProductWithQuantities")
    default Set<ProductWithQuantity> variantsToProductWithQuantities(List<VariantDTO> variants) {
        if (variants == null) {
            return java.util.Collections.emptySet();
        }

        return variants.stream()
                .map(variant -> ProductWithQuantity.fromVariantId(variant.getVariantId(), variant.getQuantity()))
                .collect(Collectors.toSet());
    }

    default Set<BundleProductEntity> mapProductsWithQuantities(
            Set<ProductWithQuantity> products, BundleEntity context) {

        if (products == null) {
            return Collections.emptySet();
        }

        return products.stream()
                .map(productWithQuantity -> {
                    BundleProductEntity bp = toBundleProductEntity(productWithQuantity);
                    bp.setBundle(context);
                    return bp;
                })
                .collect(Collectors.toSet());
    }

    default Set<ProductWithQuantity> mapBundleProductsToProductWithQuantities(Set<BundleProductEntity> bundleProducts) {
        if (bundleProducts == null) {
            return Collections.emptySet();
        }
        return bundleProducts.stream().map(this::toProductWithQuantity).collect(Collectors.toSet());
    }
}
