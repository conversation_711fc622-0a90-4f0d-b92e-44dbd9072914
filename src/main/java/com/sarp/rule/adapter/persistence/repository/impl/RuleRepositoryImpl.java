package com.sarp.rule.adapter.persistence.repository.impl;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_NOT_FOUND;

import com.querydsl.core.types.Predicate;
import com.sarp.rule.adapter.persistence.mapper.RuleMapper;
import com.sarp.rule.adapter.persistence.model.entity.QRuleEntity;
import com.sarp.rule.adapter.persistence.model.entity.RuleEntity;
import com.sarp.rule.adapter.persistence.model.enums.RuleStatus;
import com.sarp.rule.adapter.persistence.repository.RuleJpaRepository;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.repository.RuleRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortDirection;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import io.github.perplexhub.rsql.RSQLQueryDslSupport;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class RuleRepositoryImpl implements RuleRepository {

    private static final String DEFAULT_SORT_FIELD = "id";
    private static final Sort.Direction DEFAULT_SORT_DIRECTION = Sort.Direction.DESC;

    private final RuleJpaRepository ruleJpaRepository;
    private final RuleMapper ruleMapper;

    @Override
    public PaginatedResult<Rule> findRulesMatchingCriteria(
            String rsqlQuery, PagingParams pagingParams, SortingParams sortingParams) {

        Predicate predicate = buildPredicate(rsqlQuery);
        Pageable pageable = buildPageable(pagingParams, sortingParams);

        Page<RuleEntity> rulePage = executeQuery(predicate, pageable);

        return mapToPaginatedResult(rulePage, pagingParams);
    }

    @Override
    public Optional<Rule> findById(UUID ruleId) {
        return ruleJpaRepository.findById(ruleId).map(ruleMapper::toDomain);
    }

    @Override
    public Rule save(Rule rule) {
        RuleEntity ruleEntity = ruleMapper.toEntity(rule);
        RuleEntity savedEntity = ruleJpaRepository.save(ruleEntity);
        return ruleMapper.toDomain(savedEntity);
    }

    @Override
    public void deleteById(UUID ruleId) {
        RuleEntity ruleEntity =
                ruleJpaRepository.findById(ruleId).orElseThrow(() -> new RuleNotFoundException(RULE_NOT_FOUND));

        ruleEntity.setRuleStatus(RuleStatus.DELETED);
        ruleJpaRepository.save(ruleEntity);
    }

    @Override
    public void updateStatusesToWaitingForActionByConditionSetGroupId(UUID conditionSetGroupId) {
        List<RuleEntity> rules = ruleJpaRepository.findAllByConditionSetGroupsId(conditionSetGroupId);

        rules.forEach(rule -> rule.setRuleStatus(RuleStatus.WAITING_FOR_ACTION));

        ruleJpaRepository.saveAll(rules);
    }

    private Predicate buildPredicate(String rsqlQuery) {
        if (rsqlQuery == null || rsqlQuery.trim().isEmpty()) {
            return null;
        }
        return RSQLQueryDslSupport.toPredicate(rsqlQuery, QRuleEntity.ruleEntity);
    }

    private Pageable buildPageable(PagingParams pagingParams, SortingParams sortingParams) {
        Sort sort = buildSort(sortingParams);
        return PageRequest.of(pagingParams.getPageNumber(), pagingParams.getPageSize(), sort);
    }

    private Sort buildSort(SortingParams sortingParams) {
        if (sortingParams == null || sortingParams.isEmpty()) {
            return getDefaultSort();
        }

        List<Sort.Order> orders = sortingParams.getSortFields().stream()
                .map(sortField -> sortField.getDirection() == SortDirection.ASC
                        ? Sort.Order.asc(sortField.getFieldName())
                        : Sort.Order.desc(sortField.getFieldName()))
                .toList();

        return Sort.by(orders);
    }

    private Sort getDefaultSort() {
        return Sort.by(DEFAULT_SORT_DIRECTION, DEFAULT_SORT_FIELD);
    }

    private Page<RuleEntity> executeQuery(Predicate predicate, Pageable pageable) {
        return predicate != null ? ruleJpaRepository.findAll(predicate, pageable) : ruleJpaRepository.findAll(pageable);
    }

    private PaginatedResult<Rule> mapToPaginatedResult(Page<RuleEntity> page, PagingParams pagingParams) {
        List<Rule> rules = page.getContent().stream().map(ruleMapper::toDomain).toList();

        return new PaginatedResult<>(
                rules,
                pagingParams.getPageNumber(),
                pagingParams.getPageSize(),
                page.getTotalElements(),
                page.getTotalPages());
    }
}
