package com.sarp.rule.adapter.persistence.repository.impl;

import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupExceptionMessageCode.CONDITION_SET_GROUP_NOT_FOUND;

import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.adapter.persistence.model.entity.ConditionSetGroupEntity;
import com.sarp.rule.adapter.persistence.repository.ConditionSetGroupJpaRepository;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.exception.ConditionSetGroupNotFoundException;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConditionSetGroupRepositoryImpl implements ConditionSetGroupRepository {
    private final ConditionSetGroupJpaRepository conditionSetGroupJpaRepository;
    private final ConditionSetGroupMapper conditionSetGroupMapper;

    @Override
    public List<ConditionSetGroup> saveAll(List<ConditionSetGroup> conditionSetGroups) {
        List<ConditionSetGroupEntity> conditionSetGroupEntities = conditionSetGroups.stream()
                .map(conditionSetGroupMapper::toEntity)
                .toList();
        List<ConditionSetGroupEntity> savedEntities = conditionSetGroupJpaRepository.saveAll(conditionSetGroupEntities);
        return savedEntities.stream().map(conditionSetGroupMapper::toDomain).toList();
    }

    @Override
    public ConditionSetGroup findById(UUID id) {
        return conditionSetGroupJpaRepository
                .findById(id)
                .map(conditionSetGroupMapper::toDomain)
                .orElseThrow(() -> new ConditionSetGroupNotFoundException(CONDITION_SET_GROUP_NOT_FOUND, id));
    }

    @Override
    public List<ConditionSetGroup> findAllById(List<UUID> ids) {
        return conditionSetGroupJpaRepository.findAllById(ids).stream()
                .map(conditionSetGroupMapper::toDomain)
                .toList();
    }

    @Override
    public boolean existsById(UUID id) {
        return conditionSetGroupJpaRepository.existsById(id);
    }

    @Override
    public PaginatedResult<ConditionSetGroup> findAll(PagingParams pagingParams) {
        Pageable pageable = PageRequest.of(pagingParams.getPageNumber(), pagingParams.getPageSize());
        var entityPage = conditionSetGroupJpaRepository.findAll(pageable);
        return mapToPaginatedResult(entityPage);
    }

    @Override
    public ConditionSetGroup save(ConditionSetGroup conditionSetGroup) {
        ConditionSetGroupEntity savedConditionSetGroupEntity = conditionSetGroupMapper.toEntity(conditionSetGroup);
        conditionSetGroupJpaRepository.save(savedConditionSetGroupEntity);
        return conditionSetGroupMapper.toDomain(savedConditionSetGroupEntity);
    }

    @Override
    public void delete(ConditionSetGroup conditionSetGroup) {
        ConditionSetGroupEntity conditionSetGroupEntity = conditionSetGroupMapper.toEntity(conditionSetGroup);
        conditionSetGroupJpaRepository.delete(conditionSetGroupEntity);
    }

    private PaginatedResult<ConditionSetGroup> mapToPaginatedResult(Page<ConditionSetGroupEntity> entityPage) {
        List<ConditionSetGroup> conditionSetGroups = entityPage.getContent().stream()
                .map(conditionSetGroupMapper::toDomain)
                .toList();
        return new PaginatedResult<>(
                conditionSetGroups,
                entityPage.getNumber(),
                entityPage.getSize(),
                entityPage.getTotalElements(),
                entityPage.getTotalPages());
    }
}
