package com.sarp.rule.adapter.persistence.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.generated.openapi.api.dto.ConditionSetDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionSetDTO;
import com.sarp.rule.adapter.persistence.model.entity.ConditionEntity;
import com.sarp.rule.adapter.persistence.model.entity.ConditionSetEntity;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.ConditionSet;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(
        componentModel = "spring",
        uses = {ConditionNodeMapper.class, ActionMapper.class, CriteriaMapper.class})
public interface ConditionSetMapper {

    @Mapping(target = "id", ignore = true)
    ConditionSet conditionSetDTOToConditionSet(ConditionSetDTO conditionSetDTO);

    ConditionSet updateConditionSetDTOToConditionSet(UpdateConditionSetDTO updateConditionSetDTO);

    ConditionSetEntity toEntity(ConditionSet conditionSet);

    ConditionSet toDomain(ConditionSetEntity conditionSetEntity);

    @Mapping(target = "value", source = ".", qualifiedByName = "mapStringListToObjectList")
    Condition conditionEntityToCondition(ConditionEntity conditionEntity);

    @Named("mapStringListToObjectList")
    default List<Object> mapStringListToObjectList(ConditionEntity entity) {
        if (entity.getValue() == null) {
            return Collections.emptyList();
        }

        ObjectMapper objectMapper = new ObjectMapper();
        return entity.getValue().stream()
                .map(value -> {
                    if (value instanceof String str) {
                        try {
                            return objectMapper.readValue(str, Object.class);
                        } catch (Exception e) {
                            // Not a valid JSON, return as string
                            return str;
                        }
                    }
                    return value;
                })
                .toList();
    }
}
