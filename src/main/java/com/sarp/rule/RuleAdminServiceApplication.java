package com.sarp.rule;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.config.DataConfig;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@ComponentScan(
        includeFilters = {
            @ComponentScan.Filter(
                    type = FilterType.ANNOTATION,
                    value = {DomainService.class})
        })
@ComponentScan(basePackages = {"com.sarp.rule", "com.sarp.commons.kafka", "com.sarp.core"})
@OpenAPIDefinition(
        info =
                @Info(
                        title = "Rule Admin Service API",
                        version = "1.0",
                        description = "Rule Admin Service API Documentation"))
@Import(DataConfig.class)
public class RuleAdminServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(RuleAdminServiceApplication.class, args);
    }
}
