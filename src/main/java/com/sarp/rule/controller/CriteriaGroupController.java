package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupRequestDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupWithDetailsResponseDTO;
import com.sarp.generated.openapi.api.dto.RuleTypeDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaGroupRequestDTO;
import com.sarp.generated.openapi.api.paths.CriteriaGroupApi;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.application.handler.criteriagroup.CreateCriteriaGroupCommandHandler;
import com.sarp.rule.application.handler.criteriagroup.CriteriaGroupByIdQueryHandler;
import com.sarp.rule.application.handler.criteriagroup.CriteriaGroupDetailQueryHandler;
import com.sarp.rule.application.handler.criteriagroup.CriteriaGroupsQueryHandler;
import com.sarp.rule.application.handler.criteriagroup.DeleteCriteriaGroupCommandHandler;
import com.sarp.rule.application.handler.criteriagroup.UpdateCriteriaGroupCommandHandler;
import com.sarp.rule.application.mapper.criteriagroup.CreateCriteriaGroupCommandMapper;
import com.sarp.rule.application.mapper.criteriagroup.CriteriaGroupQueryMapper;
import com.sarp.rule.application.mapper.criteriagroup.CriteriaGroupsQueryMapper;
import com.sarp.rule.application.mapper.criteriagroup.DeleteCriteriaGroupCommandMapper;
import com.sarp.rule.application.mapper.criteriagroup.UpdateCriteriaGroupCommandMapper;
import com.sarp.rule.domain.query.criteriagroup.CriteriaGroupWithDetailQuery;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class CriteriaGroupController implements CriteriaGroupApi {

    private final CreateCriteriaGroupCommandHandler createHandler;
    private final CreateCriteriaGroupCommandMapper createMapper;

    private final UpdateCriteriaGroupCommandHandler updateHandler;
    private final UpdateCriteriaGroupCommandMapper updateMapper;

    private final DeleteCriteriaGroupCommandHandler deleteHandler;
    private final DeleteCriteriaGroupCommandMapper deleteMapper;

    private final CriteriaGroupByIdQueryHandler criteriaGroupByIdQueryHandler;
    private final CriteriaGroupQueryMapper criteriaGroupQueryMapper;

    private final CriteriaGroupsQueryHandler criteriaGroupsQueryHandler;
    private final CriteriaGroupsQueryMapper criteriaGroupsQueryMapper;

    private final CriteriaGroupDetailQueryHandler criteriaGroupDetailQueryHandler;

    @Override
    public ResponseEntity<BaseResponseDTO> create(CreateCriteriaGroupRequestDTO criteriaGroupRequestDTO) {
        CriteriaGroupApplicationDTO applicationDTO =
                createHandler.handle(createMapper.toCommand(criteriaGroupRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                createMapper.toResponse(applicationDTO), "Criteria Group created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> update(
            UUID id, UpdateCriteriaGroupRequestDTO updateCriteriaGroupRequestDTO) {

        CriteriaGroupApplicationDTO applicationDTO =
                updateHandler.handle(updateMapper.toCommand(id, updateCriteriaGroupRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                updateMapper.toResponse(applicationDTO), "Criteria Group updated successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> delete(UUID id) {
        deleteHandler.handle(deleteMapper.toCommand(id));

        return BaseResponseBuilder.buildEmptyResponse("Criteria Group", "deleted");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getCriteriaGroup(UUID id) {
        CriteriaGroupApplicationDTO applicationDTO =
                criteriaGroupByIdQueryHandler.handle(criteriaGroupQueryMapper.toQuery(id));

        CriteriaGroupWithCriteriaResponseDTO responseDTO = criteriaGroupQueryMapper.toResponse(applicationDTO);

        return BaseResponseBuilder.buildSingleResponse(responseDTO, "Criteria Group retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getAll(Integer page, Integer size) {
        PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO> applicationResult =
                criteriaGroupsQueryHandler.handle(criteriaGroupsQueryMapper.toQuery(page, size));

        Page<CriteriaGroupWithCriteriaResponseDTO> responseDtoResult =
                criteriaGroupsQueryMapper.toResponsePage(applicationResult);

        return BaseResponseBuilder.buildListResponse(
                responseDtoResult, responseDtoResult.getContent(), "Criteria Groups retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getAllWithDetails(Integer page, Integer size, RuleTypeDTO allowedRule) {
        CriteriaGroupWithDetailQuery criteriaGroupWithDetailQuery =
                criteriaGroupsQueryMapper.toCriteriaGroupWithDetailQuery(page, size, allowedRule);

        PaginatedResultApplicationDTO<CriteriaGroupWithDetailsApplicationDTO> result =
                criteriaGroupDetailQueryHandler.handle(criteriaGroupWithDetailQuery);

        Page<CriteriaGroupWithDetailsResponseDTO> responseDtoResult =
                criteriaGroupsQueryMapper.toDetailsResponsePage(result);

        return BaseResponseBuilder.buildListResponse(
                responseDtoResult,
                responseDtoResult.getContent(),
                "Criteria Groups with details retrieved successfully");
    }
}
