package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.ConditionSetGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateConditionSetGroupRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionSetGroupRequestDTO;
import com.sarp.generated.openapi.api.paths.ConditionSetGroupApi;
import com.sarp.rule.application.handler.conditionsetgroup.ConditionSetGroupQueryHandler;
import com.sarp.rule.application.handler.conditionsetgroup.CreateConditionSetGroupCommandHandler;
import com.sarp.rule.application.handler.conditionsetgroup.DeleteConditionSetGroupCommandHandler;
import com.sarp.rule.application.handler.conditionsetgroup.UpdateConditionSetGroupCommandHandler;
import com.sarp.rule.application.mapper.conditionsetgroup.ConditionSetGroupQueryMapper;
import com.sarp.rule.application.mapper.conditionsetgroup.ConditionSetGroupsQueryMapper;
import com.sarp.rule.application.mapper.conditionsetgroup.CreateConditionSetGroupCommandMapper;
import com.sarp.rule.application.mapper.conditionsetgroup.DeleteConditionSetGroupCommandMapper;
import com.sarp.rule.application.mapper.conditionsetgroup.UpdateConditionSetGroupCommandMapper;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupCreatedEvent;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ConditionSetGroupController implements ConditionSetGroupApi {

    private final CreateConditionSetGroupCommandHandler createConditionSetGroupCommandHandler;
    private final CreateConditionSetGroupCommandMapper createConditionSetGroupCommandMapper;
    private final ConditionSetGroupQueryHandler conditionSetGroupQueryHandler;
    private final ConditionSetGroupQueryMapper conditionSetGroupQueryMapper;
    private final ConditionSetGroupsQueryMapper conditionSetGroupsQueryMapper;
    private final UpdateConditionSetGroupCommandHandler updateConditionSetGroupCommandHandler;
    private final UpdateConditionSetGroupCommandMapper updateConditionSetGroupCommandMapper;
    private final DeleteConditionSetGroupCommandHandler deleteConditionSetGroupCommandHandler;
    private final DeleteConditionSetGroupCommandMapper deleteConditionSetGroupCommandMapper;

    @Override
    public ResponseEntity<BaseResponseDTO> create(CreateConditionSetGroupRequestDTO createConditionSetGroupRequestDTO) {

        ConditionSetGroupCreatedEvent conditionSetGroupCreatedEvent = createConditionSetGroupCommandHandler.handle(
                createConditionSetGroupCommandMapper.toCommand(createConditionSetGroupRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                createConditionSetGroupCommandMapper.toResponse(conditionSetGroupCreatedEvent),
                "Condition Set Group created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getConditionSetGroupById(UUID id) {
        var conditionSetGroup = conditionSetGroupQueryHandler.handle(conditionSetGroupQueryMapper.toQuery(id));
        return BaseResponseBuilder.buildSingleResponse(
                conditionSetGroupQueryMapper.toResponse(conditionSetGroup),
                "Condition Set Group retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getConditionSetGroups(Integer page, Integer size) {
        var domainPaginatedResult =
                conditionSetGroupQueryHandler.handle(conditionSetGroupsQueryMapper.toQuery(page, size));

        Page<ConditionSetGroupResponseDTO> conditionSetGroupDTOPage =
                conditionSetGroupQueryMapper.toResponsePage(domainPaginatedResult);

        return BaseResponseBuilder.buildListResponse(
                conditionSetGroupDTOPage,
                conditionSetGroupDTOPage.getContent(),
                "Condition Set Groups retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> updateConditionSetGroupById(
            UUID id, UpdateConditionSetGroupRequestDTO updateConditionSetGroupRequestDTO) {
        var conditionSetGroupUpdatedEvent = updateConditionSetGroupCommandHandler.handle(
                updateConditionSetGroupCommandMapper.toCommand(id, updateConditionSetGroupRequestDTO));
        return BaseResponseBuilder.buildSingleResponse(
                updateConditionSetGroupCommandMapper.toResponse(conditionSetGroupUpdatedEvent),
                "Condition Set Group updated successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> deleteConditionSetGroupById(UUID id) {
        deleteConditionSetGroupCommandHandler.handle(deleteConditionSetGroupCommandMapper.toCommand(id));
        return BaseResponseBuilder.buildEmptyResponse("Condition Set Group", "delete");
    }
}
