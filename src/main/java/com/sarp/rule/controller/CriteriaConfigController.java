package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.SaveCriteriaConfigRequestDTO;
import com.sarp.generated.openapi.api.paths.CriteriaConfigApi;
import com.sarp.rule.application.handler.CriteriaConfigSaveCommandHandler;
import com.sarp.rule.domain.command.criteriaconfig.CriteriaConfigSaveCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class CriteriaConfigController implements CriteriaConfigApi {

    private final CriteriaConfigSaveCommandHandler criteriaConfigSaveCommandHandler;

    @Override
    public ResponseEntity<BaseResponseDTO> saveCriteriaConfig(
            SaveCriteriaConfigRequestDTO saveCriteriaConfigRequestDTO) {
        criteriaConfigSaveCommandHandler.handle(CriteriaConfigSaveCommand.fromRequestDto(saveCriteriaConfigRequestDTO));

        return BaseResponseBuilder.buildEmptyResponse("Criteria Config", "saved");
    }
}
