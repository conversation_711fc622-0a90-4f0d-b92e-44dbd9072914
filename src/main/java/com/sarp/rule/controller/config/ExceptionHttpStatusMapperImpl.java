package com.sarp.rule.controller.config;

import com.sarp.core.controller.config.ExceptionHttpStatusMapper;
import com.sarp.rule.domain.exception.BundleDomainException;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.exception.BundleSetConfigDomainException;
import com.sarp.rule.domain.exception.ConditionDomainException;
import com.sarp.rule.domain.exception.ConditionNodeDomainException;
import com.sarp.rule.domain.exception.ConditionSetDomainException;
import com.sarp.rule.domain.exception.ConditionSetGroupDomainException;
import com.sarp.rule.domain.exception.ConditionSetGroupNotFoundException;
import com.sarp.rule.domain.exception.ConditionSetGroupTemplateDomainException;
import com.sarp.rule.domain.exception.CriteriaConfigDomainException;
import com.sarp.rule.domain.exception.CriteriaDomainException;
import com.sarp.rule.domain.exception.CriteriaGroupDomainException;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.exception.ProductConditionSetGroupDomainException;
import com.sarp.rule.domain.exception.ProductDomainException;
import com.sarp.rule.domain.exception.ProductNotFoundException;
import com.sarp.rule.domain.exception.RuleActionDomainException;
import com.sarp.rule.domain.exception.RuleDomainException;
import java.util.Map;
import java.util.Optional;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class ExceptionHttpStatusMapperImpl implements ExceptionHttpStatusMapper {
    private static final Map<String, HttpStatus> EXCEPTION_TYPE_TO_STATUS_MAP = Map.ofEntries(
            Map.entry(BundleDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ProductConditionSetGroupDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(RuleDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(RuleActionDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ProductDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(CriteriaGroupDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(CriteriaDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(CriteriaConfigDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ConditionSetGroupTemplateDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ConditionSetGroupDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ConditionSetDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ConditionNodeDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(ConditionDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(BundleSetConfigDomainException.EXCEPTION_TYPE_CODE, HttpStatus.BAD_REQUEST),
            Map.entry(BundleNotFoundException.EXCEPTION_TYPE_CODE, HttpStatus.NOT_FOUND),
            Map.entry(ConditionSetGroupNotFoundException.EXCEPTION_TYPE_CODE, HttpStatus.NOT_FOUND),
            Map.entry(CriteriaNotFoundException.EXCEPTION_TYPE_CODE, HttpStatus.NOT_FOUND),
            Map.entry(CriteriaGroupNotFoundException.EXCEPTION_TYPE_CODE, HttpStatus.NOT_FOUND),
            Map.entry(ProductNotFoundException.EXCEPTION_TYPE_CODE, HttpStatus.NOT_FOUND));

    @Override
    public Optional<HttpStatus> mapToHttpStatus(String exceptionTypeCode) {
        return Optional.ofNullable(EXCEPTION_TYPE_TO_STATUS_MAP.get(exceptionTypeCode));
    }
}
