package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.ConditionSetGroupTemplateResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateConditionSetGroupTemplateRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionSetGroupTemplateRequestDTO;
import com.sarp.generated.openapi.api.paths.ConditionSetGroupTemplateApi;
import com.sarp.rule.application.handler.conditionsetgrouptemplate.ConditionSetGroupTemplateQueryHandler;
import com.sarp.rule.application.handler.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommandHandler;
import com.sarp.rule.application.handler.conditionsetgrouptemplate.DeleteConditionSetGroupTemplateCommandHandler;
import com.sarp.rule.application.handler.conditionsetgrouptemplate.UpdateConditionSetGroupTemplateCommandHandler;
import com.sarp.rule.application.mapper.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommandMapper;
import com.sarp.rule.application.mapper.conditionsetgrouptemplate.DeleteConditionSetGroupTemplateCommandMapper;
import com.sarp.rule.application.mapper.conditionsetgrouptemplate.GetConditionSetGroupTemplateQueryMapper;
import com.sarp.rule.application.mapper.conditionsetgrouptemplate.GetConditionSetGroupTemplatesQueryMapper;
import com.sarp.rule.application.mapper.conditionsetgrouptemplate.UpdateConditionSetGroupTemplateCommandMapper;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateCreatedEvent;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ConditionSetGroupTemplateController implements ConditionSetGroupTemplateApi {

    private final CreateConditionSetGroupTemplateCommandHandler createConditionSetGroupTemplateCommandHandler;
    private final UpdateConditionSetGroupTemplateCommandHandler updateConditionSetGroupTemplateCommandHandler;
    private final DeleteConditionSetGroupTemplateCommandHandler deleteConditionSetGroupTemplateCommandHandler;
    private final ConditionSetGroupTemplateQueryHandler conditionSetGroupTemplateQueryHandler;
    private final GetConditionSetGroupTemplatesQueryMapper getConditionSetGroupTemplatesQueryMapper;
    private final GetConditionSetGroupTemplateQueryMapper getConditionSetGroupTemplateQueryMapper;
    private final DeleteConditionSetGroupTemplateCommandMapper deleteConditionSetGroupTemplateCommandMapper;
    private final UpdateConditionSetGroupTemplateCommandMapper updateConditionSetGroupTemplateCommandMapper;
    private final CreateConditionSetGroupTemplateCommandMapper createConditionSetGroupTemplateCommandMapper;

    @Override
    public ResponseEntity<BaseResponseDTO> create(
            CreateConditionSetGroupTemplateRequestDTO createConditionSetGroupTemplateRequestDTO) {
        ConditionSetGroupTemplateCreatedEvent conditionSetGroupTemplateCreatedEvent =
                createConditionSetGroupTemplateCommandHandler.handle(
                        createConditionSetGroupTemplateCommandMapper.toCommand(
                                createConditionSetGroupTemplateRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                createConditionSetGroupTemplateCommandMapper.toResponse(conditionSetGroupTemplateCreatedEvent),
                "ConditionSetGroupTemplate created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> deleteConditionSetGroupTemplateById(UUID id) {
        var deletedEvent = deleteConditionSetGroupTemplateCommandHandler.handle(
                deleteConditionSetGroupTemplateCommandMapper.toCommand(id));
        return BaseResponseBuilder.buildSingleResponse(deletedEvent, "ConditionSetGroupTemplate deleted successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getConditionSetGroupTemplateById(UUID id) {
        var conditionSetGroupTemplate =
                conditionSetGroupTemplateQueryHandler.handle(getConditionSetGroupTemplateQueryMapper.toQuery(id));
        return BaseResponseBuilder.buildSingleResponse(
                getConditionSetGroupTemplateQueryMapper.toResponse(conditionSetGroupTemplate),
                "ConditionSetGroupTemplate retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getConditionSetGroupTemplates(Integer page, Integer size) {
        var domainPaginatedResult = conditionSetGroupTemplateQueryHandler.handle(
                getConditionSetGroupTemplatesQueryMapper.toQuery(page, size));

        Page<ConditionSetGroupTemplateResponseDTO> responsePage =
                getConditionSetGroupTemplateQueryMapper.toResponsePage(domainPaginatedResult);
        return BaseResponseBuilder.buildListResponse(
                responsePage, responsePage.getContent(), "ConditionSetGroupTemplates retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> updateConditionSetGroupTemplateById(
            UUID id, UpdateConditionSetGroupTemplateRequestDTO updateConditionSetGroupTemplateRequestDTO) {
        var updatedEvent = updateConditionSetGroupTemplateCommandHandler.handle(
                updateConditionSetGroupTemplateCommandMapper.toCommand(id, updateConditionSetGroupTemplateRequestDTO));
        return BaseResponseBuilder.buildSingleResponse(
                updateConditionSetGroupTemplateCommandMapper.toResponse(updatedEvent),
                "ConditionSetGroupTemplate updated successfully");
    }
}
