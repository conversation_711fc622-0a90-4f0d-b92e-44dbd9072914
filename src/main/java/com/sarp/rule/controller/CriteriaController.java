package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.CreateCriteriaRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaRequestDTO;
import com.sarp.generated.openapi.api.paths.CriteriaApi;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.handler.criteria.CreateCriteriaCommandHandler;
import com.sarp.rule.application.handler.criteria.CriteriaByIdQueryHandler;
import com.sarp.rule.application.handler.criteria.DeleteCriteriaCommandHandler;
import com.sarp.rule.application.handler.criteria.UpdateCriteriaCommandHandler;
import com.sarp.rule.application.mapper.criteria.CreateCriteriaCommandMapper;
import com.sarp.rule.application.mapper.criteria.CriteriaByIdQueryMapper;
import com.sarp.rule.application.mapper.criteria.DeleteCriteriaCommandMapper;
import com.sarp.rule.application.mapper.criteria.UpdateCriteriaCommandMapper;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class CriteriaController implements CriteriaApi {

    private final CreateCriteriaCommandHandler createHandler;
    private final CreateCriteriaCommandMapper createMapper;

    private final UpdateCriteriaCommandHandler updateHandler;
    private final UpdateCriteriaCommandMapper updateMapper;

    private final CriteriaByIdQueryHandler criteriaByIdQueryHandler;
    private final CriteriaByIdQueryMapper criteriaByIdQueryMapper;

    private final DeleteCriteriaCommandHandler deleteHandler;
    private final DeleteCriteriaCommandMapper deleteMapper;

    @Override
    public ResponseEntity<BaseResponseDTO> create(CreateCriteriaRequestDTO createCriteriaRequestDTO) {
        CriteriaApplicationDTO applicationDTO = createHandler.handle(createMapper.toCommand(createCriteriaRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                createMapper.toResponse(applicationDTO), "Criteria created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> update(UUID id, UpdateCriteriaRequestDTO updateCriteriaRequestDTO) {

        CriteriaApplicationDTO applicationDTO =
                updateHandler.handle(updateMapper.toCommand(id, updateCriteriaRequestDTO));

        return BaseResponseBuilder.buildSingleResponse(
                updateMapper.toResponse(applicationDTO), "Criteria updated successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getById(UUID id) {
        CriteriaApplicationDTO applicationDTO = criteriaByIdQueryHandler.handle(criteriaByIdQueryMapper.toQuery(id));

        return BaseResponseBuilder.buildSingleResponse(
                criteriaByIdQueryMapper.toResponse(applicationDTO), "Criteria retrieved successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> deleteCriteriaById(UUID id) {
        deleteHandler.handle(deleteMapper.toCommand(id));

        return BaseResponseBuilder.buildEmptyResponse("Criteria", " deleted succesfully");
    }
}
