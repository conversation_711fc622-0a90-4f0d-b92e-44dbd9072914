package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.CreateMarketRequestDTO;
import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateMarketRequestDTO;
import com.sarp.generated.openapi.api.paths.MarketApi;
import com.sarp.rule.adapter.persistence.mapper.MarketQueryMapper;
import com.sarp.rule.application.handler.market.*;
import com.sarp.rule.application.mapper.market.CreateMarketCommandMapper;
import com.sarp.rule.application.mapper.market.UpdateMarketCommandMapper;
import com.sarp.rule.domain.command.market.CreateMarketCommand;
import com.sarp.rule.domain.command.market.DeleteMarketCommand;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import com.sarp.rule.domain.query.market.MarketByIdQuery;
import com.sarp.rule.domain.query.market.MarketsQuery;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class MarketController implements MarketApi {

    private final CreateMarketCommandHandler createMarketCommandHandler;
    private final CreateMarketCommandMapper createMarketCommandMapper;

    private final UpdateMarketCommandHandler updateMarketCommandHandler;
    private final UpdateMarketCommandMapper updateMarketCommandMapper;

    private final DeleteMarketCommandHandler deleteMarketCommandHandler;

    private final MarketsQueryHandler marketsQueryHandler;
    private final MarketByIdQueryHandler marketByIdQueryHandler;
    private final MarketQueryMapper marketQueryMapper;

    @Override
    public ResponseEntity<BaseResponseDTO> create(CreateMarketRequestDTO request) {
        CreateMarketCommand createMarketCommand = createMarketCommandMapper.toCommand(request);

        MarketResponseDTO marketResponseDTO = createMarketCommandHandler.handle(createMarketCommand);

        return BaseResponseBuilder.buildSingleResponse(marketResponseDTO, "Market created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getAll(@ParameterObject Pageable pageable) {
        MarketsQuery query = MarketsQuery.of(pageable);

        Page<MarketResponseDTO> responseDTOS = marketsQueryHandler.handle(query);

        return BaseResponseBuilder.buildListResponse(responseDTOS, responseDTOS.getContent(), "Markets");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getById(UUID id) {
        MarketByIdQuery marketByQueryId = marketQueryMapper.toQuery(id);

        MarketResponseDTO marketResponseDTO = marketByIdQueryHandler.handle(marketByQueryId);

        return BaseResponseBuilder.buildSingleResponse(marketResponseDTO, "Market fetched successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> update(UUID id, UpdateMarketRequestDTO updateMarketRequestDTO) {
        UpdateMarketCommand updateMarketCommand = updateMarketCommandMapper.toCommand(id, updateMarketRequestDTO);

        MarketResponseDTO marketResponseDTO = updateMarketCommandHandler.handle(updateMarketCommand);

        return BaseResponseBuilder.buildSingleResponse(marketResponseDTO, "Market updated successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> deleteById(UUID id) {
        deleteMarketCommandHandler.handle(new DeleteMarketCommand(id));

        return BaseResponseBuilder.buildEmptyResponse("Market", "deleted");
    }
}
