package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.paths.ProductApi;
import com.sarp.rule.application.dto.product.ProductApplicationDTO;
import com.sarp.rule.application.handler.product.ProductsQueryHandler;
import com.sarp.rule.domain.command.product.ProductsQuery;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ProductController implements ProductApi {

    private final ProductsQueryHandler productsQueryHandler;

    @Override
    public ResponseEntity<BaseResponseDTO> getAllProducts(
            @RequestParam(value = "search", required = false) String rsqlQuery, @ParameterObject Pageable pageable) {
        ProductsQuery query = ProductsQuery.of(pageable, rsqlQuery);

        Page<ProductApplicationDTO> responseDTOs = productsQueryHandler.handle(query);

        return BaseResponseBuilder.buildListResponse(
                responseDTOs, responseDTOs.getContent(), "Products fetched successfully");
    }
}
