package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.GetActionsByIdsRequestDTO;
import com.sarp.generated.openapi.api.paths.ActionApi;
import com.sarp.rule.application.handler.action.ActionsByIdsQueryHandler;
import com.sarp.rule.application.mapper.action.ActionsByIdsQueryMapper;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.query.action.ActionsByIdsQuery;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ActionController implements ActionApi {
    private final ActionsByIdsQueryHandler actionsByIdsQueryHandler;
    private final ActionsByIdsQueryMapper actionsByIdsQueryMapper;

    public ResponseEntity<BaseResponseDTO> getActionsByIds(
            @Valid @RequestBody GetActionsByIdsRequestDTO getActionsByIdsRequestDTO) {
        ActionsByIdsQuery query = actionsByIdsQueryMapper.toQuery(getActionsByIdsRequestDTO);
        List<Action> actions = actionsByIdsQueryHandler.handle(query);
        return BaseResponseBuilder.buildSingleResponse(
                actionsByIdsQueryMapper.toResponse(actions), "Actions retrieved successfully");
    }
}
