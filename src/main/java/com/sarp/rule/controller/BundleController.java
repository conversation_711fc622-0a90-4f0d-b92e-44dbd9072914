package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.*;
import com.sarp.generated.openapi.api.paths.BundleApi;
import com.sarp.rule.application.handler.bundle.*;
import com.sarp.rule.application.mapper.bundle.*;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.event.bundle.BundlesCreatedEvent;
import com.sarp.rule.domain.query.bundle.BundlesByIdsQuery;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class BundleController implements BundleApi {
    private final CreateBundleCommandHandler createBundleCommandHandler;
    private final CreateBundlesCommandMapper createBundlesCommandMapper;

    private final UpdateBundleCommandMapper updateBundleCommandMapper;
    private final UpdateBundleCommandHandler updateBundleCommandHandler;

    private final BundleQueryHandler bundleQueryHandler;
    private final GetBundleQueryMapper getBundleQueryMapper;

    private final GetBundlesQueryMapper getBundlesQueryMapper;

    private final DeleteBundleCommandHandler deleteBundleCommandHandler;
    private final DeleteBundleCommandMapper deleteBundleCommandMapper;
    private final BundlesByIdsQueryHandler bundlesByIdsQueryHandler;
    private final BundlesByIdsQueryMapper bundlesByIdsQueryMapper;

    public ResponseEntity<BaseResponseDTO> create(CreateBundlesRequestDTO createBundlesRequestDTO) {
        BundlesCreatedEvent bundleCreatedEvent =
                createBundleCommandHandler.handle(createBundlesCommandMapper.toCommand(createBundlesRequestDTO));
        return BaseResponseBuilder.buildSingleResponse(
                createBundlesCommandMapper.toResponse(bundleCreatedEvent), "Bundle(s) created successfully");
    }

    public ResponseEntity<BaseResponseDTO> update(UUID id, UpdateBundleRequestDTO updateBundleRequestDTO) {
        var bundleUpdatedEvent =
                updateBundleCommandHandler.handle(updateBundleCommandMapper.toCommand(id, updateBundleRequestDTO));
        return BaseResponseBuilder.buildSingleResponse(
                updateBundleCommandMapper.toResponse(bundleUpdatedEvent), "Bundle updated successfully");
    }

    public ResponseEntity<BaseResponseDTO> getBundleById(UUID id) {
        var bundle = bundleQueryHandler.handle(getBundleQueryMapper.toQuery(id));
        return BaseResponseBuilder.buildSingleResponse(
                getBundleQueryMapper.toResponse(bundle), "Bundle retrieved successfully");
    }

    public ResponseEntity<BaseResponseDTO> getBundles(Integer page, Integer size, BundleStatusDTO statusDTO) {
        var domainPaginatedResult = bundleQueryHandler.handle(getBundlesQueryMapper.toQuery(page, size, statusDTO));

        Page<BundleResponseDTO> responseDtoPage = getBundleQueryMapper.toResponsePage(domainPaginatedResult);
        return BaseResponseBuilder.buildListResponse(
                responseDtoPage, responseDtoPage.getContent(), "Bundles retrieved successfully");
    }

    public ResponseEntity<BaseResponseDTO> deleteBundleById(UUID id) {
        deleteBundleCommandHandler.handle(deleteBundleCommandMapper.toCommand(id));
        return BaseResponseBuilder.buildEmptyResponse("Bundle", "delete");
    }

    public ResponseEntity<BaseResponseDTO> getBundlesByIds(BundlesByIdsRequestDTO request) {
        BundlesByIdsQuery query = bundlesByIdsQueryMapper.toQuery(request);
        List<Bundle> bundles = bundlesByIdsQueryHandler.handle(query);
        return BaseResponseBuilder.buildSingleResponse(
                bundlesByIdsQueryMapper.toResponse(bundles), "Bundles retrieved successfully");
    }
}
