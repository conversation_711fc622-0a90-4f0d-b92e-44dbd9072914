package com.sarp.rule.controller;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.core.controller.util.BaseResponseBuilder;
import com.sarp.generated.openapi.api.dto.ActivateRuleRequestDTO;
import com.sarp.generated.openapi.api.dto.CreateRuleRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateRuleRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateRuleResponseDTO;
import com.sarp.generated.openapi.api.paths.RuleApi;
import com.sarp.rule.adapter.persistence.mapper.RuleQueryMapper;
import com.sarp.rule.application.dto.rule.RuleApplicationDTO;
import com.sarp.rule.application.handler.CreateRuleCommandHandler;
import com.sarp.rule.application.handler.DeleteRuleCommandHandler;
import com.sarp.rule.application.handler.RulesQueryHandler;
import com.sarp.rule.application.handler.rule.ActivateRuleCommandHandler;
import com.sarp.rule.application.handler.rule.RuleByIdQueryHandler;
import com.sarp.rule.application.handler.rule.UpdateRuleCommandHandler;
import com.sarp.rule.application.mapper.rule.ActivateRuleCommandMapper;
import com.sarp.rule.application.mapper.rule.CreateRuleCommandMapper;
import com.sarp.rule.application.mapper.rule.UpdateRuleCommandMapper;
import com.sarp.rule.domain.command.rule.*;
import com.sarp.rule.domain.event.rule.RuleCreatedEvent;
import com.sarp.rule.domain.event.rule.RuleUpdatedEvent;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class RuleController implements RuleApi {

    private final CreateRuleCommandHandler createRuleCommandHandler;
    private final CreateRuleCommandMapper createRuleCommandMapper;

    private final UpdateRuleCommandHandler updateRuleCommandHandler;
    private final UpdateRuleCommandMapper updateRuleCommandMapper;

    private final ActivateRuleCommandHandler activateRuleCommandHandler;
    private final ActivateRuleCommandMapper activateRuleCommandMapper;

    private final RuleByIdQueryHandler ruleByIdQueryHandler;
    private final RuleQueryMapper ruleQueryMapper;

    private final RulesQueryHandler rulesQueryHandler;

    private final DeleteRuleCommandHandler deleteRuleCommandHandler;

    @Override
    public ResponseEntity<BaseResponseDTO> create(CreateRuleRequestDTO createRuleRequestDTO) {
        CreateRuleCommand createRuleCommand = createRuleCommandMapper.toCommand(createRuleRequestDTO);

        RuleCreatedEvent ruleCreatedEvent = createRuleCommandHandler.handle(createRuleCommand);

        return BaseResponseBuilder.buildSingleResponse(
                createRuleCommandMapper.toResponse(ruleCreatedEvent), "Rule created successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getById(UUID ruleId) {
        RuleByIdQuery ruleByIdQuery = ruleQueryMapper.toQuery(ruleId);

        RuleApplicationDTO ruleApplicationDTO = ruleByIdQueryHandler.handle(ruleByIdQuery);

        return BaseResponseBuilder.buildSingleResponse(ruleApplicationDTO, "Rule details fetched successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> update(UUID id, UpdateRuleRequestDTO updateRuleRequestDTO) {
        UpdateRuleCommand updateRuleCommand = updateRuleCommandMapper.toCommand(id, updateRuleRequestDTO);
        RuleUpdatedEvent ruleUpdatedEvent = updateRuleCommandHandler.handle(updateRuleCommand);
        UpdateRuleResponseDTO responseDTO = updateRuleCommandMapper.toResponseDTO(ruleUpdatedEvent);
        return BaseResponseBuilder.buildSingleResponse(responseDTO, "Rule updated successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> activateRule(UUID id, ActivateRuleRequestDTO activateRuleRequestDTO) {
        ActivateRuleCommand activateRuleCommand = activateRuleCommandMapper.toCommand(id, activateRuleRequestDTO);
        activateRuleCommandHandler.handle(activateRuleCommand);
        return BaseResponseBuilder.buildEmptyResponse("Rule status", "updated");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> getAllRules(
            @RequestParam(value = "search", required = false) String rsqlQuery, @ParameterObject Pageable pageable) {
        RulesQuery query = RulesQuery.of(pageable, rsqlQuery);

        Page<RuleApplicationDTO> responseDTOs = rulesQueryHandler.handle(query);

        return BaseResponseBuilder.buildListResponse(
                responseDTOs, responseDTOs.getContent(), "Rules fetched successfully");
    }

    @Override
    public ResponseEntity<BaseResponseDTO> deleteById(UUID id) {
        deleteRuleCommandHandler.handle(new DeleteRuleCommand(id));
        return BaseResponseBuilder.buildEmptyResponse("Rule", "deleted successfully");
    }
}
