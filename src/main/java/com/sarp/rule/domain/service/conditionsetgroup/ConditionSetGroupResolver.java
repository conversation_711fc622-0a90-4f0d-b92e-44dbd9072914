package com.sarp.rule.domain.service.conditionsetgroup;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.springframework.stereotype.Service;

/*
   This service is responsible for arranging condition set groups for rule updates.
   It ensures that condition set groups that are the same as the old ones retain their IDs,
   while new or modified condition set groups are assigned new IDs to prevent conflicts.
*/
@Service
@DomainService
public class ConditionSetGroupResolver {
    public Set<ConditionSetGroup> resolve(
            Set<ConditionSetGroup> newConditionSetGroups, Map<UUID, ConditionSetGroup> oldConditionSetGroupsMap) {
        List<ConditionSetGroup> sameConditionSetGroups = newConditionSetGroups.stream()
                .filter(newConditionSetGroup -> oldConditionSetGroupsMap.containsKey(newConditionSetGroup.getId())
                        && oldConditionSetGroupsMap
                                .get(newConditionSetGroup.getId())
                                .equals(newConditionSetGroup))
                .toList();

        List<ConditionSetGroup> notSameConditionSetGroups = newConditionSetGroups.stream()
                .filter(newConditionSetGroup -> !sameConditionSetGroups.contains(newConditionSetGroup))
                .map(ConditionSetGroup::createWithNewId)
                .toList();

        Set<ConditionSetGroup> arrangedConditionSetGroups = new HashSet<>(sameConditionSetGroups);
        arrangedConditionSetGroups.addAll(notSameConditionSetGroups);
        return arrangedConditionSetGroups;
    }
}
