package com.sarp.rule.domain.service.product;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_BUNDLE_TARGET_REQUIRED;
import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_PRODUCT_TARGET_REQUIRED;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleDomainException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

public class BundleProductMapping implements ProductMappingStrategy {
    @Override
    public Rule enrichRuleWithProducts(Rule rule, List<Product> foundProducts) {
        if (CollectionUtils.isEmpty(foundProducts)) {
            throw new RuleDomainException(RULE_PRODUCT_TARGET_REQUIRED);
        }

        Map<UUID, Product> productsByVariantId =
                foundProducts.stream().collect(Collectors.toMap(Product::getVariantId, Function.identity()));

        Set<Bundle> updatedBundles = rule.getBundles().stream()
                .map(bundle -> bundle.withUpdatedProducts(productsByVariantId))
                .collect(Collectors.toSet());

        return rule.withUpdatedBundles(updatedBundles);
    }

    @Override
    public List<UUID> extractProductVariantIds(Rule rule) {
        if (rule.getBundles() == null) {
            throw new RuleDomainException(RULE_BUNDLE_TARGET_REQUIRED);
        }

        Set<UUID> variantIds = new HashSet<>();
        rule.getBundles().forEach(bundle -> {
            if (bundle.getProductWithQuantities() != null) {
                bundle.getProductWithQuantities()
                        .forEach(pwq -> variantIds.add(pwq.product().getVariantId()));
            }
        });

        return new ArrayList<>(variantIds);
    }
}
