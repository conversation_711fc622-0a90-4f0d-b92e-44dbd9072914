package com.sarp.rule.domain.service;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_VARIANT_ID_LIST_CANNOT_BE_EMPTY;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.exception.ProductNotFoundException;
import com.sarp.rule.domain.exception.RuleDomainException;
import com.sarp.rule.domain.exception.messagecode.ProductExceptionMessage;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class RuleProductVerifier {

    /**
     * Verifies that the provided list of found products satisfies the product requirements of the
     * given rule. This is pure domain logic with no knowledge of persistence.
     *
     * @param rule The Rule aggregate to verify.
     * @param foundProducts The list of Products that were fetched from the database by the
     *     application layer.
     * @throws ProductNotFoundException if the list of found products does not match the rule's
     *     requirements.
     */
    public void verifyProductsFor(List<UUID> variantIds, List<Product> foundProducts) {
        if (CollectionUtils.isEmpty(variantIds)) {
            throw new RuleDomainException(RULE_VARIANT_ID_LIST_CANNOT_BE_EMPTY);
        }

        Set<UUID> requiredVariantIds = new HashSet<>(variantIds);
        Set<UUID> foundVariantIds =
                foundProducts.stream().map(Product::getVariantId).collect(Collectors.toSet());

        if (foundVariantIds.size() != requiredVariantIds.size()) {
            Set<UUID> missingIds = new HashSet<>(requiredVariantIds);
            missingIds.removeAll(foundVariantIds);

            throw new ProductNotFoundException(ProductExceptionMessage.PRODUCTS_NOT_FOUND_BY_VARIANT_IDS, missingIds);
        }
    }
}
