package com.sarp.rule.domain.service;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class RuleBundleVerifier {
    public void verifyBundlesFor(List<UUID> bundleIds, Set<Bundle> foundBundles) {

        if (CollectionUtils.isEmpty(bundleIds)) return;

        Set<UUID> requiredBundleIds = new HashSet<>(bundleIds);
        Set<UUID> foundBundleIds = foundBundles.stream().map(Bundle::getId).collect(Collectors.toSet());

        if (foundBundleIds.size() != requiredBundleIds.size()) {
            Set<UUID> missingIds = new HashSet<>(requiredBundleIds);
            missingIds.removeAll(foundBundleIds);

            throw new BundleNotFoundException(BundleExceptionMessageCode.BUNDLES_NOT_FOUND, missingIds);
        }
    }
}
