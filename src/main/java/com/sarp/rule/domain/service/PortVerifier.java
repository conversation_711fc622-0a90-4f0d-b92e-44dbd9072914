package com.sarp.rule.domain.service;

import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORTS_INACTIVE_BY_PORT_CODES;
import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORTS_NOT_FOUND_BY_PORT_CODES;
import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORT_CODE_LIST_CANNOT_BE_EMPTY;

import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.exception.PortDomainException;
import com.sarp.rule.domain.exception.PortNotFoundException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortVerifier {
    /**
     * Verifies that all requested port codes exist in the provided list of ports.
     *
     * @param portCodes List of port codes to verify
     * @param foundPorts List of Port objects retrieved from the system
     * @throws PortDomainException if the port codes list is empty
     * @throws PortNotFoundException if any requested port code is not found
     */
    public void verifyPortExists(List<String> portCodes, Set<Port> foundPorts) {
        if (portCodes.isEmpty()) {
            throw new PortDomainException(PORT_CODE_LIST_CANNOT_BE_EMPTY);
        }

        Set<String> requiredPortCodes = new HashSet<>(portCodes);
        Set<String> foundPortCodes = foundPorts.stream().map(Port::getPortCode).collect(Collectors.toSet());

        if (requiredPortCodes.size() != foundPortCodes.size()) {
            Set<String> missingPortCodes = new HashSet<>(requiredPortCodes);
            missingPortCodes.removeAll(foundPortCodes);

            throw new PortNotFoundException(PORTS_NOT_FOUND_BY_PORT_CODES, missingPortCodes);
        }
    }

    /**
     * Verifies that all ports in the provided list are active.
     * This method checks only the active status of ports and does not verify their existence.
     * The existence check should be performed separately using verifyPortExists method.
     *
     * @param portCodes List of port codes to verify (must not be empty)
     * @param foundPorts List of Port objects to check for active status
     * @throws PortDomainException if the port codes list is empty
     * @throws PortNotFoundException if any ports are found to be inactive, including the list of inactive port codes
     */
    public void verifyPortsActive(List<String> portCodes, Set<Port> foundPorts) {
        if (portCodes.isEmpty()) {
            throw new PortDomainException(PORT_CODE_LIST_CANNOT_BE_EMPTY);
        }

        Set<String> inactivePortCodes = foundPorts.stream()
                .filter(port -> !port.isActive())
                .map(Port::getPortCode)
                .collect(Collectors.toSet());

        if (!inactivePortCodes.isEmpty()) {
            throw new PortNotFoundException(PORTS_INACTIVE_BY_PORT_CODES, inactivePortCodes);
        }
    }
}
