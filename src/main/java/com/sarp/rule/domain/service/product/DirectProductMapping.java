package com.sarp.rule.domain.service.product;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_PRODUCT_TARGET_REQUIRED;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleDomainException;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import org.springframework.util.CollectionUtils;

public class DirectProductMapping implements ProductMappingStrategy {
    @Override
    public Rule enrichRuleWithProducts(Rule rule, List<Product> foundProducts) {
        if (CollectionUtils.isEmpty(foundProducts)) {
            throw new RuleDomainException(RULE_PRODUCT_TARGET_REQUIRED);
        }

        return rule.withUpdatedDirectProducts(new HashSet<>(foundProducts));
    }

    @Override
    public List<UUID> extractProductVariantIds(Rule rule) {
        if (rule.getProducts() == null) {
            throw new RuleDomainException(RULE_PRODUCT_TARGET_REQUIRED);
        }

        return rule.getProducts().stream().map(Product::getVariantId).toList();
    }
}
