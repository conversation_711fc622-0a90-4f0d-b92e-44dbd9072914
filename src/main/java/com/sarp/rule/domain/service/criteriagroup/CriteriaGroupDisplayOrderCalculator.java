package com.sarp.rule.domain.service.criteriagroup;

import com.sarp.core.domain.base.DomainService;
import lombok.RequiredArgsConstructor;

@DomainService
@RequiredArgsConstructor
public class CriteriaGroupDisplayOrderCalculator {

    public Integer calculateNextDisplayOrder(Integer currentMaxDisplayOrder) {
        if (currentMaxDisplayOrder == null) {
            return 1;
        }
        return currentMaxDisplayOrder + 1;
    }
}
