package com.sarp.rule.domain.service.product;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.PRODUCT_MAPPING_STRATEGY_NOT_FOUND;

import com.sarp.rule.domain.exception.RuleDomainException;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.EnumMap;
import java.util.Map;

public class ProductMappingFactory {

    private static final Map<RuleType, ProductMappingStrategy> strategies;

    static {
        strategies = new EnumMap<>(RuleType.class);

        DirectProductMapping directMapping = new DirectProductMapping();

        strategies.put(RuleType.BUNDLE, new BundleProductMapping());
        strategies.put(RuleType.ALA_CARTE, directMapping);
        strategies.put(RuleType.PRICING, directMapping);
    }

    public static ProductMappingStrategy getStrategy(RuleType ruleType) {
        ProductMappingStrategy strategy = strategies.get(ruleType);
        if (strategy == null) {
            throw new RuleDomainException(PRODUCT_MAPPING_STRATEGY_NOT_FOUND, ruleType);
        }
        return strategy;
    }

    private ProductMappingFactory() {}
}
