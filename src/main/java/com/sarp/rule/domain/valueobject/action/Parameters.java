package com.sarp.rule.domain.valueobject.action;

import java.util.Map;

public record Parameters(Map<String, Object> parameters) {
    public Parameters {
        if (parameters == null || parameters.isEmpty()) {
            throw new IllegalArgumentException("Parameters cannot be null or empty");
        }
    }

    public static Parameters of(Map<String, Object> parameters) {
        return new Parameters(parameters);
    }
}
