package com.sarp.rule.domain.valueobject.product;

import static com.sarp.rule.domain.exception.messagecode.ProductWithQuantityExceptionMessage.PRODUCT_CANNOT_BE_NULL;
import static com.sarp.rule.domain.exception.messagecode.ProductWithQuantityExceptionMessage.VARIANT_ID_CANNOT_BE_NULL;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.exception.ProductWithQuantityDomainException;
import java.util.UUID;

public record ProductWithQuantity(Product product, int quantity) {

    public static ProductWithQuantity of(Product product, Integer quantity) {
        if (product == null) {
            throw new ProductWithQuantityDomainException(PRODUCT_CANNOT_BE_NULL);
        }

        return new ProductWithQuantity(product, quantity != null ? quantity : 1);
    }

    public static ProductWithQuantity fromVariantId(UUID variantId, int quantity) {
        if (variantId == null) {
            throw new ProductWithQuantityDomainException(VARIANT_ID_CANNOT_BE_NULL);
        }

        Product product = Product.builder().variantId(variantId).build();

        return of(product, quantity);
    }
}
