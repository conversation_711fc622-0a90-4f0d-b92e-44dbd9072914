package com.sarp.rule.domain.valueobject.criteria;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.*;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaDomainException;
import java.util.HashSet;
import java.util.List;
import lombok.Getter;

@Getter
public enum FieldType {
    INTEGER(List.of(
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS,
            ComparisonOperator.GREATER_THAN,
            ComparisonOperator.GREATER_OR_EQUAL,
            ComparisonOperator.LESS_THAN,
            ComparisonOperator.LESS_OR_EQUAL,
            ComparisonOperator.BETWEEN,
            ComparisonOperator.NOT_BETWEEN,
            ComparisonOperator.CONTAINS_ANY,
            ComparisonOperator.CONTAINS_NONE)) {
        @Override
        public void validateRequiredFields(Criteria criteria) {
            if (criteria.getMinValue() == null) {
                throw new CriteriaDomainException(CRITERIA_MIN_VALUE_CAN_NOT_BE_NULL);
            }
            if (criteria.getMaxValue() == null) {
                throw new CriteriaDomainException(CRITERIA_MAX_VALUE_CAN_NOT_BE_NULL);
            }
        }
    },
    DECIMAL_NUMBER(List.of(
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS,
            ComparisonOperator.GREATER_THAN,
            ComparisonOperator.GREATER_OR_EQUAL,
            ComparisonOperator.LESS_THAN,
            ComparisonOperator.LESS_OR_EQUAL,
            ComparisonOperator.BETWEEN,
            ComparisonOperator.NOT_BETWEEN,
            ComparisonOperator.CONTAINS_ANY,
            ComparisonOperator.CONTAINS_NONE)) {
        @Override
        public void validateRequiredFields(Criteria criteria) {
            if (criteria.getMinValue() == null) {
                throw new CriteriaDomainException(CRITERIA_MIN_VALUE_CAN_NOT_BE_NULL);
            }
            if (criteria.getMaxValue() == null) {
                throw new CriteriaDomainException(CRITERIA_MAX_VALUE_CAN_NOT_BE_NULL);
            }
        }
    },
    TEXT(List.of(
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS,
            ComparisonOperator.CONTAINS_ANY,
            ComparisonOperator.CONTAINS_NONE)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    },
    BOOLEAN(List.of(ComparisonOperator.EQUALS, ComparisonOperator.NOT_EQUALS)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    },
    LIST(List.of(
            ComparisonOperator.CONTAINS_ANY,
            ComparisonOperator.CONTAINS_ALL,
            ComparisonOperator.CONTAINS_NONE,
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            if (criteria.getSelectionType() == null) {
                throw new CriteriaDomainException(CRITERIA_SELECTION_TYPE_CAN_NOT_BE_NULL);
            }
            if (criteria.getAllowedValues() == null
                    || criteria.getAllowedValues().isEmpty()) {
                throw new CriteriaDomainException(CRITERIA_VALUES_CAN_NOT_BE_EMPTY);
            }
        }
    },
    DATETIME(List.of(
            ComparisonOperator.GREATER_THAN,
            ComparisonOperator.LESS_THAN,
            ComparisonOperator.BETWEEN,
            ComparisonOperator.NOT_BETWEEN)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    },
    DATE(List.of(
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS,
            ComparisonOperator.GREATER_THAN,
            ComparisonOperator.GREATER_OR_EQUAL,
            ComparisonOperator.LESS_THAN,
            ComparisonOperator.LESS_OR_EQUAL,
            ComparisonOperator.BETWEEN,
            ComparisonOperator.NOT_BETWEEN)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    },
    TIME(List.of(
            ComparisonOperator.EQUALS,
            ComparisonOperator.NOT_EQUALS,
            ComparisonOperator.GREATER_THAN,
            ComparisonOperator.GREATER_OR_EQUAL,
            ComparisonOperator.LESS_THAN,
            ComparisonOperator.LESS_OR_EQUAL,
            ComparisonOperator.BETWEEN,
            ComparisonOperator.NOT_BETWEEN)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    },
    OND(List.of(ComparisonOperator.CUSTOM)) {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /* no implementation */
        }
    };

    private final List<ComparisonOperator> allowedOperators;

    FieldType(List<ComparisonOperator> allowedOperators) {
        this.allowedOperators = allowedOperators;
    }

    public boolean isOperatorsEligible(List<ComparisonOperator> declaredOperators) {
        return new HashSet<>(allowedOperators).containsAll(declaredOperators);
    }

    public abstract void validateRequiredFields(Criteria criteria) throws CriteriaDomainException;
}
