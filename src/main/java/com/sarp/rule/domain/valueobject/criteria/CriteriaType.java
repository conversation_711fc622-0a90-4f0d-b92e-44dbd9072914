package com.sarp.rule.domain.valueobject.criteria;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_MAPPING_FIELD_CAN_NOT_BE_EMPTY;

import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaDomainException;

public enum CriteriaType {
    USER_DEFINED {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            if (criteria.getMappingField() == null || criteria.getMappingField().isBlank()) {
                throw new CriteriaDomainException(CRITERIA_MAPPING_FIELD_CAN_NOT_BE_EMPTY);
            }
        }
    },
    SYSTEM_DEFINED {
        @Override
        public void validateRequiredFields(Criteria criteria) throws CriteriaDomainException {
            /*
             * System-defined criteria types do not require additional fields to be validated.
             * They are typically predefined and managed by the system.
             */
        }
    };

    public abstract void validateRequiredFields(Criteria criteria) throws CriteriaDomainException;
}
