package com.sarp.rule.domain.valueobject.pagination;

import lombok.Getter;

@Getter
public class SortField {
    private final String fieldName;
    private final SortDirection direction;

    private SortField(String fieldName, SortDirection direction) {
        this.fieldName = fieldName;
        this.direction = direction;
    }

    public static SortField of(String fieldName, boolean isAscending) {
        return new SortField(fieldName, isAscending ? SortDirection.ASC : SortDirection.DESC);
    }
}
