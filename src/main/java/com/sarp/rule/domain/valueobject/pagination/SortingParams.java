package com.sarp.rule.domain.valueobject.pagination;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

@Getter
public class SortingParams {
    private final List<SortField> sortFields;

    private SortingParams(List<SortField> sortFields) {
        this.sortFields = sortFields != null ? sortFields : new ArrayList<>();
    }

    public static SortingParams empty() {
        return new SortingParams(Collections.emptyList());
    }

    public static SortingParams of(List<SortField> sortFields) {
        return new SortingParams(sortFields);
    }

    public static SortingParams of(String field, SortDirection direction) {
        List<SortField> fields = new ArrayList<>();
        fields.add(SortField.of(field, direction == SortDirection.ASC));
        return new SortingParams(fields);
    }

    public boolean isEmpty() {
        return sortFields.isEmpty();
    }

    public List<SortField> getSortFields() {
        return Collections.unmodifiableList(sortFields);
    }
}
