package com.sarp.rule.domain.valueobject.criteria;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

public record CriteriaWithConfigDetails(
        UUID criteriaId,
        String criteriaName,
        String description,
        CriteriaType type,
        RequestType requestType,
        String mappingField,
        FieldType fieldType,
        SelectionType selectionType,
        List<String> allowedValues,
        BigInteger minValue,
        BigInteger maxValue,
        List<ComparisonOperator> allowedOperators,
        LocalDateTime startDateTime,
        LocalDateTime endDateTime,
        LocalTime startTime,
        LocalTime endTime,
        Integer displayOrder,
        List<RuleType> allowedRules,
        List<RuleType> mandatoryRules) {}
