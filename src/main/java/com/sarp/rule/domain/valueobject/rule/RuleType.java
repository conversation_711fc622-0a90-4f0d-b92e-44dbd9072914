package com.sarp.rule.domain.valueobject.rule;

import com.sarp.rule.domain.valueobject.action.ActionType;
import java.util.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum RuleType {
    BUNDLE(List.of(ActionType.PERCENTAGE_DISCOUNT)),
    // Price action is added temporarily for offer service compatibility
    ALA_CARTE(List.of(ActionType.PERCENTAGE_DISCOUNT, ActionType.PRICE)),
    PRICING(List.of(ActionType.PRICE));

    private final List<ActionType> compatibleActionTypes;

    public boolean isCompatibleActionType(ActionType actionType) {
        return compatibleActionTypes.contains(actionType);
    }
}
