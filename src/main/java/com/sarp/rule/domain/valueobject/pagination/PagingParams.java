package com.sarp.rule.domain.valueobject.pagination;

import com.sarp.rule.domain.exception.DomainException;
import lombok.Getter;

@Getter
public class PagingParams {
    private final int pageNumber;
    private final int pageSize;

    private static final int DEFAULT_PAGE_NUMBER = 0;
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 100;

    private PagingParams(int pageNumber, int pageSize) {
        if (pageNumber < 0) {
            throw new DomainException("Page number cannot be negative.");
        }
        if (pageSize > MAX_PAGE_SIZE) {
            throw new DomainException("Page size cannot exceed " + MAX_PAGE_SIZE + ".");
        }
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }

    public static PagingParams of(Integer pageNumber, Integer pageSize) {
        int effectivePageNumber = (pageNumber == null) ? DEFAULT_PAGE_NUMBER : pageNumber;
        int effectivePageSize = (pageSize == null) ? DEFAULT_PAGE_SIZE : pageSize;
        return new PagingParams(effectivePageNumber, effectivePageSize);
    }
}
