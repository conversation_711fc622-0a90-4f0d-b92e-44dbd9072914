package com.sarp.rule.domain.valueobject.common;

import com.sarp.rule.domain.exception.DomainException;
import java.time.Instant;

public record EffectiveDates(Instant from, Instant to) {

    public EffectiveDates {
        if (from == null || to == null) {
            throw new DomainException("Effective date range cannot be null");
        }
        if (from.isAfter(to)) {
            throw new DomainException("Effective date 'from' must be before 'to'");
        }
    }

    public static EffectiveDates of(Instant from, Instant to) {
        return new EffectiveDates(from, to);
    }
}
