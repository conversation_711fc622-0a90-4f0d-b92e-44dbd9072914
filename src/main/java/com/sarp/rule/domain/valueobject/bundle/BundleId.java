package com.sarp.rule.domain.valueobject.bundle;

import com.sarp.rule.domain.valueobject.BaseId;
import java.util.UUID;

public class BundleId extends BaseId<UUID> {

    public BundleId(UUID value) {
        super(value);
    }

    public static BundleId of(UUID value) {
        return new BundleId(value);
    }

    public static BundleId generateNew() {
        return new BundleId(UUID.randomUUID());
    }
}
