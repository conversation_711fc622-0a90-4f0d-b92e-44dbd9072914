package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CriteriaGroupRepository {
    CriteriaGroup save(CriteriaGroup criteriaGroup);

    Optional<CriteriaGroup> findByIdWithoutCriteriaList(UUID id);

    Integer getHighestDisplayOrder();

    boolean existsById(UUID id);

    void deleteById(UUID id);

    Optional<CriteriaGroup> findByIdWithCriteria(UUID id);

    PaginatedResult<CriteriaGroup> findAllWithCriteriaDetails(PagingParams pagingParams);

    PaginatedResult<CriteriaGroupWithDetails> findAllWithDetailsByRuleType(
            RuleType ruleType, PagingParams pagingParams);

    List<CriteriaGroup> updateAll(List<CriteriaGroup> criteriaGroups);

    List<CriteriaGroup> findAllById(List<UUID> ids);

    void saveAll(List<CriteriaGroup> criteriaGroups);
}
