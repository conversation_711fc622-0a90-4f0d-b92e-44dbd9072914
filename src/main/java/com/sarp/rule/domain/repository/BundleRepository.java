package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface BundleRepository {
    Bundle save(Bundle bundle);

    void delete(Bundle bundle);

    List<Bundle> saveAll(List<Bundle> bundles);

    Optional<Bundle> findById(UUID id);

    PaginatedResult<Bundle> findAllByStatus(BundleStatus status, PagingParams pagingParams);

    PaginatedResult<Bundle> findAllByStatusNot(BundleStatus bundleStatus, PagingParams pagingParams);

    List<Bundle> findAllById(List<UUID> ids);

    List<Bundle> findAllActiveByIdIn(List<UUID> ids);
}
