package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.Criteria;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CriteriaRepository {
    Criteria save(Criteria criteria);

    boolean existsById(UUID id);

    Criteria update(Criteria criteria);

    Optional<Criteria> findById(UUID id);

    long countByIds(List<UUID> ids);

    long countByCriteriaGroupId(UUID criteriaGroupId);

    void deleteById(UUID id);

    List<Criteria> findAllById(List<UUID> ids);
}
