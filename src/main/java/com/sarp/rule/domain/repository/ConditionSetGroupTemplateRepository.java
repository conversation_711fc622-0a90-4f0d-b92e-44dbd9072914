package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.Optional;
import java.util.UUID;

public interface ConditionSetGroupTemplateRepository {
    ConditionSetGroupTemplate save(ConditionSetGroupTemplate template);

    Optional<ConditionSetGroupTemplate> findById(UUID id);

    void deleteById(UUID id);

    PaginatedResult<ConditionSetGroupTemplate> findAll(PagingParams pagingParams);
}
