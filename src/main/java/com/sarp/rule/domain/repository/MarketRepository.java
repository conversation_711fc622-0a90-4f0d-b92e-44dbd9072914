package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface MarketRepository {
    PaginatedResult<Market> findAllMarkets(PagingParams pagingParams);

    Optional<Market> findById(UUID marketId);

    Market save(Market market);

    void deleteById(UUID marketId);

    List<Market> findAllMarketsByMarketIdsIn(List<UUID> marketIds);
}
