package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import java.util.List;
import java.util.UUID;

public interface ProductRepository {
    PaginatedResult<Product> findProductsMatchingCriteria(
            String rsqlQuery, PagingParams pagingParams, SortingParams sortingParams);

    List<UUID> findAllVariantIdsByProductEntityIds(List<UUID> productEntityIds);

    List<Product> findAllByIdIn(List<UUID> ids);

    List<Product> findAllByVariantIdIn(List<UUID> variantIds);

    List<Product> saveAll(List<Product> products);
}
