package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.List;
import java.util.UUID;

public interface ConditionSetGroupRepository {
    List<ConditionSetGroup> saveAll(List<ConditionSetGroup> conditionSetGroups);

    ConditionSetGroup findById(UUID id);

    List<ConditionSetGroup> findAllById(List<UUID> ids);

    PaginatedResult<ConditionSetGroup> findAll(PagingParams pagingParams);

    ConditionSetGroup save(ConditionSetGroup conditionSetGroup);

    void delete(ConditionSetGroup conditionSetGroup);

    boolean existsById(UUID id);
}
