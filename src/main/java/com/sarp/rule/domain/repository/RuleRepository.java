package com.sarp.rule.domain.repository;

import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import java.util.Optional;
import java.util.UUID;

public interface RuleRepository {
    PaginatedResult<Rule> findRulesMatchingCriteria(
            String rsqlQuery, PagingParams pagingParams, SortingParams sortingParams);

    Optional<Rule> findById(UUID ruleId);

    Rule save(Rule rule);

    void deleteById(UUID ruleId);

    void updateStatusesToWaitingForActionByConditionSetGroupId(UUID conditionSetGroupId);
}
