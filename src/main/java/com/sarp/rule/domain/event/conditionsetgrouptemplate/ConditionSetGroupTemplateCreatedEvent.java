package com.sarp.rule.domain.event.conditionsetgrouptemplate;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import java.time.Instant;
import lombok.Getter;

@DomainEvent
@Getter
public class ConditionSetGroupTemplateCreatedEvent extends ConditionSetGroupTemplateEvent {

    private final ConditionSetGroupTemplate conditionSetGroupTemplate;

    public ConditionSetGroupTemplateCreatedEvent(ConditionSetGroupTemplate conditionSetGroupTemplate) {
        super(Instant.now());
        this.conditionSetGroupTemplate = conditionSetGroupTemplate;
    }
}
