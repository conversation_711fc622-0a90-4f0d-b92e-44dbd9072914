package com.sarp.rule.domain.event.bundle;

import com.sarp.core.domain.base.DomainEvent;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;

@DomainEvent
@Getter
public class BundleDeletedEvent extends BundleEvent {
    private final UUID deletedBundleId;

    public BundleDeletedEvent(UUID deletedBundleId) {
        super(Instant.now());
        this.deletedBundleId = deletedBundleId;
    }
}
