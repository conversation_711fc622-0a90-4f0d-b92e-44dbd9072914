package com.sarp.rule.domain.event.conditionsetgroup;

import com.sarp.core.domain.base.DomainEvent;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;

@DomainEvent
@Getter
public class ConditionSetGroupDeletedEvent extends ConditionSetGroupEvent {
    private final UUID deletedConditionSetGroupId;

    public ConditionSetGroupDeletedEvent(UUID deletedConditionSetGroupId) {
        super(Instant.now());
        this.deletedConditionSetGroupId = deletedConditionSetGroupId;
    }
}
