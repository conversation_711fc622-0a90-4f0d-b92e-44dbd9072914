package com.sarp.rule.domain.event.criteria;

import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.event.DomainEvent;
import java.time.LocalDateTime;
import lombok.Getter;

@Getter
public class CriteriaEvent implements DomainEvent<Criteria> {
    private final Criteria criteria;
    private final LocalDateTime createdAt;

    public CriteriaEvent(Criteria rule, LocalDateTime createdAt) {
        this.criteria = rule;
        this.createdAt = createdAt;
    }
}
