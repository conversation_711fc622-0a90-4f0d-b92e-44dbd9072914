package com.sarp.rule.domain.event.conditionsetgroup;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import java.time.Instant;
import lombok.Getter;

@DomainEvent
@Getter
public class ConditionSetGroupUpdatedEvent extends ConditionSetGroupEvent {
    private final ConditionSetGroup conditionSetGroup;

    public ConditionSetGroupUpdatedEvent(ConditionSetGroup conditionSetGroup) {
        super(Instant.now());
        this.conditionSetGroup = conditionSetGroup;
    }
}
