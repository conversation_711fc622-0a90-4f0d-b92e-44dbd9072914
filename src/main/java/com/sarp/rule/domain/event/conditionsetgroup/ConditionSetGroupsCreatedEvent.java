package com.sarp.rule.domain.event.conditionsetgroup;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import java.time.Instant;
import java.util.List;
import lombok.Getter;

@Getter
@DomainEvent
public class ConditionSetGroupsCreatedEvent {

    private final List<ConditionSetGroup> conditionSetGroups;
    private final Instant createdAt;

    public ConditionSetGroupsCreatedEvent(List<ConditionSetGroup> conditionSetGroups, Instant createdAt) {
        this.conditionSetGroups = conditionSetGroups;
        this.createdAt = createdAt;
    }
}
