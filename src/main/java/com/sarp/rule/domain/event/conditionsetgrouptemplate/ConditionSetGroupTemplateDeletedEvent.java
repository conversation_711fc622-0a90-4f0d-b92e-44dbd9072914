package com.sarp.rule.domain.event.conditionsetgrouptemplate;

import com.sarp.core.domain.base.DomainEvent;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;

@DomainEvent
@Getter
public class ConditionSetGroupTemplateDeletedEvent extends ConditionSetGroupTemplateEvent {
    private final UUID deletedConditionSetGroupTemplateId;

    public ConditionSetGroupTemplateDeletedEvent(UUID deletedConditionSetGroupTemplateId) {
        super(Instant.now());
        this.deletedConditionSetGroupTemplateId = deletedConditionSetGroupTemplateId;
    }
}
