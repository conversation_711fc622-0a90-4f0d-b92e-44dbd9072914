package com.sarp.rule.domain.event.rule;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Getter;

@DomainEvent
@Getter
public class RuleDeletedEvent {
    private final UUID deletedRuleId;
    private final Instant deletedAt;
    private final List<UUID> conditionSets;
    private final RuleType ruleType;

    public RuleDeletedEvent(UUID deletedRuleId, Instant deletedAt, List<UUID> conditionSets, RuleType ruleType) {
        this.deletedRuleId = deletedRuleId;
        this.deletedAt = deletedAt;
        this.conditionSets = conditionSets;
        this.ruleType = ruleType;
    }
}
