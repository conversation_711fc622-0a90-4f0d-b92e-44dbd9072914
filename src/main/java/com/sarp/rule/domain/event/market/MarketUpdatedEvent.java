package com.sarp.rule.domain.event.market;

import com.sarp.core.domain.base.DomainEvent;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Getter;

@Getter
@DomainEvent
public class MarketUpdatedEvent {
    private final UUID marketId;
    private final String marketName;
    private final List<String> portCodes;
    private final Instant updatedAt;

    public MarketUpdatedEvent(UUID marketId, String marketName, List<String> portCodes) {
        this.marketId = marketId;
        this.marketName = marketName;
        this.portCodes = portCodes;
        this.updatedAt = Instant.now();
    }
}
