package com.sarp.rule.domain.event.market;

import com.sarp.core.domain.base.DomainEvent;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;

@Getter
@DomainEvent
public class MarketDeletedEvent {
    private final UUID deleteMarketId;
    private final Instant deletedAt;

    public MarketDeletedEvent(UUID deleteMarketId) {
        this.deleteMarketId = deleteMarketId;
        this.deletedAt = Instant.now();
    }
}
