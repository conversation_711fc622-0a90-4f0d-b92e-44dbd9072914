package com.sarp.rule.domain.event.rule;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.entity.Rule;
import java.time.Instant;
import lombok.Getter;

@Getter
@DomainEvent
public class RuleCreatedEvent {

    private final Rule rule;
    private final ConditionSetGroupTemplate conditionSetGroupTemplate;
    private final Instant createdAt;

    public RuleCreatedEvent(Rule rule, ConditionSetGroupTemplate conditionSetGroupTemplate) {
        this.rule = rule;
        this.conditionSetGroupTemplate = conditionSetGroupTemplate;
        this.createdAt = Instant.now();
    }
}
