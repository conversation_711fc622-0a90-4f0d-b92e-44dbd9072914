package com.sarp.rule.domain.event.conditionsetgroup;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import java.time.Instant;
import lombok.Getter;

@Getter
@DomainEvent
public class ConditionSetGroupCreatedEvent extends ConditionSetGroupEvent {
    private final ConditionSetGroup conditionSetGroup;

    public ConditionSetGroupCreatedEvent(ConditionSetGroup conditionSetGroup) {
        super(Instant.now());
        this.conditionSetGroup = conditionSetGroup;
    }
}
