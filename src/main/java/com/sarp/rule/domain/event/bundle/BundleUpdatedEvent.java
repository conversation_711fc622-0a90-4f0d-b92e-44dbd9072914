package com.sarp.rule.domain.event.bundle;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.Bundle;
import java.time.Instant;
import lombok.Getter;

@DomainEvent
@Getter
public class BundleUpdatedEvent extends BundleEvent {
    private final Bundle bundle;

    public BundleUpdatedEvent(Bundle bundle) {
        super(Instant.now());
        this.bundle = bundle;
    }
}
