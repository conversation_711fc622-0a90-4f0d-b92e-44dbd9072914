package com.sarp.rule.domain.event;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.Condition;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
@DomainEvent
public class ConditionsSavedEvent {
    private final List<Condition> conditions;
    private final LocalDateTime eventDateTime;
}
