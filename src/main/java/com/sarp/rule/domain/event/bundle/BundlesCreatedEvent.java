package com.sarp.rule.domain.event.bundle;

import com.sarp.core.domain.base.DomainEvent;
import com.sarp.rule.domain.entity.Bundle;
import java.time.Instant;
import java.util.List;
import lombok.Getter;

@DomainEvent
@Getter
public class BundlesCreatedEvent extends BundleEvent {
    private final List<Bundle> bundles;

    public BundlesCreatedEvent(List<Bundle> bundles) {
        super(Instant.now());
        this.bundles = bundles;
    }
}
