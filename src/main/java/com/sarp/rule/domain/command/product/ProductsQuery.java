package com.sarp.rule.domain.command.product;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortField;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import java.util.List;
import lombok.Builder;
import lombok.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@Builder
@Value
public class ProductsQuery implements UseCase {
    String rsqlQuery;
    PagingParams pagingParams;
    SortingParams sortingParams;

    public static ProductsQuery of(Pageable pageable, String rsqlQuery) {
        return ProductsQuery.builder()
                .rsqlQuery(rsqlQuery)
                .pagingParams(convertToPagingParams(pageable))
                .sortingParams(convertToSortingParams(pageable.getSort()))
                .build();
    }

    private static PagingParams convertToPagingParams(Pageable pageable) {
        return PagingParams.of(pageable.getPageNumber(), pageable.getPageSize());
    }

    private static SortingParams convertToSortingParams(Sort sort) {
        if (sort.isUnsorted()) {
            return SortingParams.empty();
        }

        List<SortField> sortFields = sort.stream()
                .map(order -> SortField.of(order.getProperty(), order.getDirection() == Sort.Direction.ASC))
                .toList();

        return SortingParams.of(sortFields);
    }

    public boolean hasFilters() {
        return rsqlQuery != null && !rsqlQuery.trim().isEmpty();
    }

    public boolean hasSorting() {
        return !sortingParams.isEmpty();
    }
}
