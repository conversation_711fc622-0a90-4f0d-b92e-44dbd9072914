package com.sarp.rule.domain.command.conditionsetgrouptemplate;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public class CreateConditionSetGroupTemplateCommand implements UseCase {
    private final ConditionSetGroupTemplate conditionSetGroupTemplate;
}
