package com.sarp.rule.domain.command.criteria;

import com.sarp.core.application.model.UseCase;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class CountCriteriaByIdsQuery implements UseCase {
    private final List<UUID> criteriaIds;

    public static CountCriteriaByIdsQuery of(List<UUID> criteriaIds) {
        return new CountCriteriaByIdsQuery(criteriaIds);
    }
}
