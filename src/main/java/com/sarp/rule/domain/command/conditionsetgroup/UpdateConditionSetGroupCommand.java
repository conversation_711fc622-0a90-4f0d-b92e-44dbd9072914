package com.sarp.rule.domain.command.conditionsetgroup;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class UpdateConditionSetGroupCommand implements UseCase {

    private final UUID conditionSetGroupId;

    private final ConditionSetGroup conditionSetGroup;
}
