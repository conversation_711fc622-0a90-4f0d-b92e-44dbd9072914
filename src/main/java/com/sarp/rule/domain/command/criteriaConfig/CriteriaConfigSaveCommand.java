package com.sarp.rule.domain.command.criteriaconfig;

import com.sarp.core.application.model.UseCase;
import com.sarp.generated.openapi.api.dto.CriteriaConfigSaveDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupConfigSaveDTO;
import com.sarp.generated.openapi.api.dto.SaveCriteriaConfigRequestDTO;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class CriteriaConfigSaveCommand implements UseCase {

    private final List<GroupConfigSave> groups;

    @Builder
    @Getter
    public static class CriteriaConfigSave {
        private UUID criteriaId;
        private List<RuleType> allowedRules;
        private List<RuleType> mandatoryRules;
        private Integer displayOrder;

        public static CriteriaConfigSave fromRequestDto(CriteriaConfigSaveDTO criteria) {
            return CriteriaConfigSave.builder()
                    .criteriaId(criteria.getCriteriaId())
                    .allowedRules(criteria.getAllowedRuleTypes().stream()
                            .map(allowed -> RuleType.valueOf(allowed.name()))
                            .toList())
                    .mandatoryRules(criteria.getMandatoryRuleTypes().stream()
                            .map(mandatory -> RuleType.valueOf(mandatory.name()))
                            .toList())
                    .displayOrder(criteria.getDisplayOrder())
                    .build();
        }
    }

    @Builder
    @Getter
    public static class GroupConfigSave {
        private UUID groupId;
        private Integer displayOrder;
        private List<CriteriaConfigSave> criterias;

        public static GroupConfigSave fromRequestDto(CriteriaGroupConfigSaveDTO group) {
            return GroupConfigSave.builder()
                    .groupId(group.getGroupId())
                    .displayOrder(group.getDisplayOrder())
                    .criterias(group.getCriterias().stream()
                            .map(CriteriaConfigSave::fromRequestDto)
                            .toList())
                    .build();
        }
    }

    public static CriteriaConfigSaveCommand fromRequestDto(SaveCriteriaConfigRequestDTO requestDTO) {
        return CriteriaConfigSaveCommand.builder()
                .groups(requestDTO.getGroups().stream()
                        .map(GroupConfigSave::fromRequestDto)
                        .toList())
                .build();
    }
}
