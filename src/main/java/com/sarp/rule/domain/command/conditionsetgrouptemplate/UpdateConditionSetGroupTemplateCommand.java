package com.sarp.rule.domain.command.conditionsetgrouptemplate;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public class UpdateConditionSetGroupTemplateCommand implements UseCase {
    private final UUID conditionSetGroupTemplateId;
    private final ConditionSetGroupTemplate conditionSetGroupTemplate;
}
