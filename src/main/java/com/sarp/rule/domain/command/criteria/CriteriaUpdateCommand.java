package com.sarp.rule.domain.command.criteria;

import com.sarp.rule.domain.valueobject.criteria.SelectionType;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class CriteriaUpdateCommand {
    private final UUID criteriaId;
    private final String name;
    private final String description;
    private final String mappingField;
    private final SelectionType selectionType;

    public static CriteriaUpdateCommand of(UUID id, String name, String description) {
        return CriteriaUpdateCommand.builder()
                .criteriaId(id)
                .name(name)
                .description(description)
                .build();
    }
}
