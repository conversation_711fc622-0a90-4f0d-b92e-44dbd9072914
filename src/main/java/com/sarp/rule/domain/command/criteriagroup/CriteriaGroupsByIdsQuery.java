package com.sarp.rule.domain.command.criteriagroup;

import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class CriteriaGroupsByIdsQuery {
    private final List<UUID> criteriaGroupIds;

    public static CriteriaGroupsByIdsQuery of(List<UUID> criteriaGroupIds) {
        return new CriteriaGroupsByIdsQuery(criteriaGroupIds);
    }
}
