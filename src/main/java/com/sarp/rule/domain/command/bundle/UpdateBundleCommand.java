package com.sarp.rule.domain.command.bundle;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.bundle.BundleType;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class UpdateBundleCommand implements UseCase {
    private final UUID bundleId;
    private final String name;
    private final String description;
    private final BundleType type;
    private final BundleStatus status;
    private final List<ProductWithQuantity> productWithQuantities;
}
