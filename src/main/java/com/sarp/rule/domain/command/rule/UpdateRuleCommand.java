package com.sarp.rule.domain.command.rule;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import com.sarp.rule.domain.valueobject.rule.RuleStatus;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.Set;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class UpdateRuleCommand implements UseCase {
    private UUID id;
    private String name;
    private RuleType ruleType;
    private Short priority;
    private EffectiveDates effectiveDates;
    private RuleStatus ruleStatus;
    private boolean saveAsDraft;

    private Set<ConditionSetGroup> conditionSetGroups;
    private Set<UUID> bundleIds;
    private Set<UUID> variantIds;
}
