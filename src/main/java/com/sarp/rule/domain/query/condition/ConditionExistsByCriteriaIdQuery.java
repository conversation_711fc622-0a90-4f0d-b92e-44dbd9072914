package com.sarp.rule.domain.query.condition;

import com.sarp.core.application.model.UseCase;
import java.util.UUID;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class ConditionExistsByCriteriaIdQuery implements UseCase {
    private final UUID criteriaId;

    public static ConditionExistsByCriteriaIdQuery of(UUID criteriaId) {
        return new ConditionExistsByCriteriaIdQuery(criteriaId);
    }
}
