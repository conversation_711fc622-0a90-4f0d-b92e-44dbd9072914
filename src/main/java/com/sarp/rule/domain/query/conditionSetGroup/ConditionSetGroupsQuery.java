package com.sarp.rule.domain.query.conditionSetGroup;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class ConditionSetGroupsQuery implements UseCase {
    private final PagingParams pagingParams;

    public static ConditionSetGroupsQuery of(Integer page, Integer size) {
        return ConditionSetGroupsQuery.builder()
                .pagingParams(PagingParams.of(page, size))
                .build();
    }
}
