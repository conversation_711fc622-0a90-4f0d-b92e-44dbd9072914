package com.sarp.rule.domain.query.conditionsetgrouptemplate;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class ConditionSetGroupTemplatesQuery implements UseCase {
    private final PagingParams pagingParams;

    public static ConditionSetGroupTemplatesQuery of(Integer page, Integer size) {
        return ConditionSetGroupTemplatesQuery.builder()
                .pagingParams(PagingParams.of(page, size))
                .build();
    }
}
