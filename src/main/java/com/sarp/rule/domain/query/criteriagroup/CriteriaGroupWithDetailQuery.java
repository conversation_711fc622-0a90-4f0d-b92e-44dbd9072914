package com.sarp.rule.domain.query.criteriagroup;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class CriteriaGroupWithDetailQuery implements UseCase {

    private final RuleType ruleType;
    private final PagingParams pagingParams;

    public static CriteriaGroupWithDetailQuery of(RuleType ruleType, int page, int size) {
        return new CriteriaGroupWithDetailQuery(ruleType, PagingParams.of(page, size));
    }
}
