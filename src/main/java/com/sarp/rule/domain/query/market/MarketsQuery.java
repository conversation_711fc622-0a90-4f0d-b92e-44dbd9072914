package com.sarp.rule.domain.query.market;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.pagination.SortingParams;
import lombok.Builder;
import lombok.Value;
import org.springframework.data.domain.Pageable;

@Builder
@Value
public class MarketsQuery implements UseCase {
    PagingParams pagingParams;
    SortingParams sortingParams;

    public static MarketsQuery of(Pageable pageable) {
        return MarketsQuery.builder()
                .pagingParams(convertToPagingParams(pageable))
                .build();
    }

    private static PagingParams convertToPagingParams(Pageable pageable) {
        return PagingParams.of(pageable.getPageNumber(), pageable.getPageSize());
    }
}
