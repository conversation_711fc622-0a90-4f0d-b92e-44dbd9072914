package com.sarp.rule.domain.query.bundle;

import com.sarp.core.application.model.UseCase;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class GetBundlesQuery implements UseCase {
    private final PagingParams pagingParams;
    private final BundleStatus status;

    public static GetBundlesQuery of(Integer page, Integer size, BundleStatus status) {
        return GetBundlesQuery.builder()
                .pagingParams(PagingParams.of(page, size))
                .status(status)
                .build();
    }
}
