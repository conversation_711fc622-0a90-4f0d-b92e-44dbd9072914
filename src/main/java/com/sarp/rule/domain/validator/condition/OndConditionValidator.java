package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;
import static com.sarp.rule.domain.exception.messagecode.MarketExceptionMessageCode.MARKET_NOT_FOUND;
import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORT_NOT_FOUND;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.exception.ConditionDomainException;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.exception.PortNotFoundException;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.valueobject.criteria.Location;
import com.sarp.rule.domain.valueobject.criteria.OndCriteria;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

public class OndConditionValidator extends BaseConditionValidator {

    private final PortRepository portRepository;
    private final MarketRepository marketRepository;

    public OndConditionValidator(
            ObjectMapper objectMapper, PortRepository portRepository, MarketRepository marketRepository) {
        super(objectMapper);
        this.portRepository = portRepository;
        this.marketRepository = marketRepository;
    }

    // TODO validation for no inclusive port codes and market ids will be discussed

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();

        values.forEach(this::validateSingleValue);
    }

    private void validateSingleValue(Object value) {
        try {
            OndCriteria ondValue = objectMapper.convertValue(value, OndCriteria.class);

            validateOriginAndDestination(ondValue.origin());
            validateOriginAndDestination(ondValue.destination());

        } catch (IllegalArgumentException e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }

    private void validateOriginAndDestination(Location location) {
        validatePortCodes(location.inclusive().portCodes());
        validatePortCodes(location.exclusive().portCodes());
        validateMarketIds(location.inclusive().marketIds());
        validateMarketIds(location.exclusive().marketIds());
    }

    private void validatePortCodes(List<String> portCodes) {
        if (portCodes.isEmpty()) return;

        List<Port> foundPorts = portRepository.findAllByPortCodeIn(portCodes);
        validateEntities(
                portCodes,
                foundPorts,
                Port::getPortCode,
                unmatched -> new PortNotFoundException(PORT_NOT_FOUND, unmatched));
    }

    private void validateMarketIds(List<UUID> marketIds) {
        if (marketIds.isEmpty()) return;

        List<Market> foundMarkets = marketRepository.findAllMarketsByMarketIdsIn(marketIds);
        validateEntities(
                marketIds,
                foundMarkets,
                Market::getId,
                unmatched -> new MarketNotFoundException(MARKET_NOT_FOUND, unmatched));
    }

    private <T, E> void validateEntities(
            List<T> requested,
            List<E> found,
            Function<E, T> idExtractor,
            Function<List<T>, RuntimeException> exceptionFactory) {
        List<T> foundIds = found.stream().map(idExtractor).toList();
        List<T> unmatched =
                requested.stream().filter(id -> !foundIds.contains(id)).toList();
        if (!unmatched.isEmpty()) {
            throw exceptionFactory.apply(unmatched);
        }
    }
}
