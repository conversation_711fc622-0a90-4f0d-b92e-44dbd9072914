package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.*;
import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_REQUEST_TYPE_CAN_NOT_BE_NULL;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaDomainException;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 * A Domain Service responsible for validating the state of a Criteria aggregate. It checks for
 * invariants and business criteria consistency.
 */
public class CriteriaValidator {

    private final Criteria criteria;

    public CriteriaValidator(Criteria criteria) {
        this.criteria = criteria;
    }

    /** Executes all validation checks against the Criteria aggregate. */
    public void validate() {
        checkRequiredFields();
        checkCriteriaTypeSpecificLogic();
        checkFieldTypeAndOperatorsSpecificLogic();
    }

    /** Validates that all fundamental fields are present. */
    private void checkRequiredFields() {
        if (this.criteria.getName() == null || this.criteria.getName().isBlank()) {
            throw new CriteriaDomainException(CRITERIA_NAME_CAN_NOT_BE_EMPTY);
        }

        if (this.criteria.getDescription() == null
                || this.criteria.getDescription().isBlank()) {
            throw new CriteriaDomainException(CRITERIA_DESCRIPTION_CAN_NOT_BE_EMPTY);
        }

        if (this.criteria.getType() == null) {
            throw new CriteriaDomainException(CRITERIA_TYPE_CAN_NOT_BE_NULL);
        }

        if (this.criteria.getRequestType() == null) {
            throw new CriteriaDomainException(CRITERIA_REQUEST_TYPE_CAN_NOT_BE_NULL);
        }

        if (this.criteria.getFieldType() == null) {
            throw new CriteriaDomainException(CRITERIA_FIELD_TYPE_CAN_NOT_BE_NULL);
        }
    }

    /** Validates logic specific to the FieldType. */
    private void checkFieldTypeAndOperatorsSpecificLogic() {
        if (this.criteria.getType().equals(CriteriaType.SYSTEM_DEFINED)) {
            return;
        }

        FieldType fieldType = this.criteria.getFieldType();
        List<ComparisonOperator> allowedOperators = this.criteria.getAllowedOperators();

        fieldType.validateRequiredFields(this.criteria);

        if (!CollectionUtils.isEmpty(allowedOperators) && !fieldType.isOperatorsEligible(allowedOperators)) {
            throw new CriteriaDomainException(CRITERIA_ALLOWED_OPERATORS_IS_NOT_VALID);
        }
    }

    /** Validates logic specific to the CriteriaType. */
    private void checkCriteriaTypeSpecificLogic() {
        CriteriaType criteriaType = this.criteria.getType();

        criteriaType.validateRequiredFields(this.criteria);
    }
}
