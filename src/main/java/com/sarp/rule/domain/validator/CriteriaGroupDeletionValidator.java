package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_HAS_ASSOCIATED_CRITERIA;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.domain.exception.CriteriaGroupDomainException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;

@DomainService
@RequiredArgsConstructor
public class CriteriaGroupDeletionValidator {
    /**
     * Validates if a criteria group can be deleted based on the count of its associated criteria.
     *
     * @param criteriaGroupId The ID of the criteria group to validate.
     * @param associatedCriteriaCount The number of criteria associated with the group.
     * @throws CriteriaGroupDomainException if the criteria group cannot be deleted.
     */
    public void ensureDeletable(UUID criteriaGroupId, long associatedCriteriaCount) {
        if (associatedCriteriaCount > 0) {
            throw new CriteriaGroupDomainException(
                    CRITERIA_GROUP_HAS_ASSOCIATED_CRITERIA, criteriaGroupId, associatedCriteriaCount);
        }
    }
}
