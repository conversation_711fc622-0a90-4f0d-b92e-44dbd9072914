package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.*;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.exception.BundleDomainException;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * A Domain Service responsible for validating the state of a Bundle Aggregate. It checks for
 * invariants and business rule consistency.
 */
public class BundleValidator {

    public static final int BUNDLE_VARIANT_LIMIT = 20;

    private final Bundle bundle;

    public BundleValidator(Bundle bundle) {
        this.bundle = bundle;
    }

    /** Executes all validation checks against the Bundle aggregate. */
    public void validate() {
        checkRequiredFields();
        checkBusinessRules();
    }

    /** Validates that all fundamental fields are present and valid. */
    private void checkRequiredFields() {
        if (bundle.getId() == null) {
            throw new BundleDomainException(BUNDLE_ID_CANNOT_BE_NULL);
        }

        if (bundle.getName() == null || bundle.getName().isBlank()) {
            throw new BundleDomainException(BUNDLE_NAME_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (bundle.getDescription() == null || bundle.getDescription().isBlank()) {
            throw new BundleDomainException(BUNDLE_DESCRIPTION_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (bundle.getStatus() == null) {
            throw new BundleDomainException(BUNDLE_STATUS_CANNOT_BE_NULL);
        }

        if (bundle.getType() == null) {
            throw new BundleDomainException(BUNDLE_TYPE_CANNOT_BE_NULL);
        }
    }

    /** Validates business rules and constraints. */
    private void checkBusinessRules() {
        if (bundle.getProductWithQuantities() != null
                && bundle.getProductWithQuantities().isEmpty()) {
            throw new BundleDomainException(BUNDLE_AT_LEAST_ONE_PRODUCT_WITH_QUANTITY);
        }
        validateProductConsistency();
    }

    /** Validates that products within the bundle are consistent and valid. */
    private void validateProductConsistency() {
        if (!CollectionUtils.isEmpty(bundle.getProductWithQuantities())) {
            if (isBundleVariantLimitExceeded()) {
                throw new BundleDomainException(BUNDLE_PRODUCT_QUANTITY_EXCEEDS_LIMIT);
            }

            for (ProductWithQuantity productWithQuantity : bundle.getProductWithQuantities()) {
                if (productWithQuantity == null || productWithQuantity.product() == null) {
                    throw new BundleDomainException(BUNDLE_CANNOT_CONTAIN_NULL_PRODUCTS);
                }
            }
        }
    }

    private boolean isBundleVariantLimitExceeded() {
        return bundle.getProductWithQuantities().stream()
                        .collect(Collectors.groupingBy(a -> a.product().getVariantId()))
                        .size()
                > BUNDLE_VARIANT_LIMIT;
    }
}
