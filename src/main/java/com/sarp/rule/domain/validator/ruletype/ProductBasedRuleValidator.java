package com.sarp.rule.domain.validator.ruletype;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_PRODUCT_CANNOT_CONTAIN_BUNDLES;
import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_PRODUCT_TARGET_REQUIRED;

import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleDomainException;
import org.springframework.util.CollectionUtils;

public class ProductBasedRuleValidator implements RuleTypeValidator {
    @Override
    public void validateRuleTypeSpecificLogic(Rule rule) {
        if (CollectionUtils.isEmpty(rule.getProducts())) {
            throw new RuleDomainException(RULE_PRODUCT_TARGET_REQUIRED);
        }
        if (!CollectionUtils.isEmpty(rule.getBundles())) {
            throw new RuleDomainException(RULE_PRODUCT_CANNOT_CONTAIN_BUNDLES);
        }
    }
}
