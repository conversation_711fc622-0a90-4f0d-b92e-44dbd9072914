package com.sarp.rule.domain.validator.condition;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.config.JacksonConfig;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import jakarta.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConditionValidatorFactory {

    private final PortRepository portRepository;
    private final MarketRepository marketRepository;
    private final JacksonConfig jacksonConfig;

    private static final Map<FieldType, ConditionValidator> validators = new EnumMap<>(FieldType.class);

    @PostConstruct
    public void init() {

        ObjectMapper objectMapper = jacksonConfig.objectMapper();

        final NumberConditionValidator numberValidator = new NumberConditionValidator(objectMapper);
        final DateTimeConditionValidator dateTimeValidator = new DateTimeConditionValidator(objectMapper);

        validators.put(FieldType.BOOLEAN, new BooleanConditionValidator(objectMapper));
        validators.put(FieldType.TEXT, new TextConditionValidator(objectMapper));
        validators.put(FieldType.INTEGER, numberValidator);
        validators.put(FieldType.DATE, dateTimeValidator);
        validators.put(FieldType.DATETIME, dateTimeValidator);
        validators.put(FieldType.TIME, new TimeConditionValidator(objectMapper));
        validators.put(FieldType.LIST, new ListConditionValidator(objectMapper));
        validators.put(FieldType.DECIMAL_NUMBER, numberValidator);
        validators.put(FieldType.OND, new OndConditionValidator(objectMapper, portRepository, marketRepository));
    }

    public static ConditionValidator getValidator(FieldType fieldType) {
        return validators.get(fieldType);
    }
}
