package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;
import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_OUT_OF_RANGE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.time.LocalTime;
import java.util.List;

public class TimeConditionValidator extends BaseConditionValidator {

    protected TimeConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        Criteria criteria = condition.getCriteria();

        LocalTime startTime = criteria.getStartTime();
        LocalTime endTime = criteria.getEndTime();
        try {
            values.forEach(value -> {
                LocalTime time = objectMapper.convertValue(value, LocalTime.class);
                if ((startTime != null && time.isBefore(startTime)) || (endTime != null && time.isAfter(endTime))) {
                    throw new ConditionDomainException(CONDITION_VALUE_OUT_OF_RANGE);
                }
            });
        } catch (Exception e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }
}
