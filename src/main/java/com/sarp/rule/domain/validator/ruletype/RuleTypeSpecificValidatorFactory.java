package com.sarp.rule.domain.validator.ruletype;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_TYPE_VALIDATOR_NOT_FOUND;

import com.sarp.rule.domain.exception.RuleDomainException;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.EnumMap;
import java.util.Map;

public class RuleTypeSpecificValidatorFactory {

    private static final Map<RuleType, RuleTypeValidator> validators;

    static {
        validators = new EnumMap<>(RuleType.class);

        ProductBasedRuleValidator productValidator = new ProductBasedRuleValidator();

        validators.put(RuleType.BUNDLE, new BundleRuleValidator());
        validators.put(RuleType.ALA_CARTE, productValidator);
        validators.put(RuleType.PRICING, productValidator);
    }

    public static RuleTypeValidator getValidator(RuleType ruleType) {
        RuleTypeValidator validator = validators.get(ruleType);
        if (validator == null) {
            throw new RuleDomainException(RULE_TYPE_VALIDATOR_NOT_FOUND, ruleType);
        }
        return validator;
    }

    private RuleTypeSpecificValidatorFactory() {}
}
