package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.INVALID_PORT_CODE_LENGTH;
import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORT_CODE_CANNOT_BE_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.PortExceptionMessageCode.PORT_CODE_MUST_CONTAIN_ONLY_LETTERS;

import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.exception.PortDomainException;

public class PortValidator {
    private final Port port;
    private static final int PORT_CODE_LENGTH = 3;

    public PortValidator(Port port) {
        this.port = port;
    }

    public void validate() {
        validatePortCode();
    }

    /**
     * Validates that the port code:
     * - Is exactly 3 characters long
     * - Contains only letters
     * - Is converted to uppercase for consistency
     *
     * @throws PortDomainException if validation fails
     */
    private void validatePortCode() {
        String portCode = port.getPortCode();

        if (portCode == null) {
            throw new PortDomainException(PORT_CODE_CANNOT_BE_EMPTY);
        }

        portCode = portCode.toUpperCase();

        if (portCode.length() != PORT_CODE_LENGTH) {
            throw new PortDomainException(INVALID_PORT_CODE_LENGTH, portCode.length());
        }

        // Check if contains only letters
        if (!portCode.matches("[A-Z]{3}")) {
            throw new PortDomainException(PORT_CODE_MUST_CONTAIN_ONLY_LETTERS);
        }
    }
}
