package com.sarp.rule.domain.validator.ruletype;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.*;

import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleDomainException;
import org.springframework.util.CollectionUtils;

public class BundleRuleValidator implements RuleTypeValidator {

    public static final int RULE_BUNDLE_LIMIT = 10;

    @Override
    public void validateRuleTypeSpecificLogic(Rule rule) {
        if (CollectionUtils.isEmpty(rule.getBundles())) {
            throw new RuleDomainException(RULE_BUNDLE_TARGET_REQUIRED);
        }
        if (rule.getBundles().size() > RULE_BUNDLE_LIMIT) {
            throw new RuleDomainException(RULE_BUNDLE_LIMIT_EXCEEDED, RULE_BUNDLE_LIMIT);
        }
        if (!CollectionUtils.isEmpty(rule.getProducts())) {
            throw new RuleDomainException(RULE_BUNDLE_CANNOT_CONTAIN_PRODUCTS);
        }
    }
}
