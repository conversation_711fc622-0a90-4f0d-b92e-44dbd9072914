package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.*;

import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.entity.ConditionSet;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleDomainException;
import com.sarp.rule.domain.validator.ruletype.RuleTypeSpecificValidatorFactory;
import com.sarp.rule.domain.validator.ruletype.RuleTypeValidator;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import org.springframework.util.CollectionUtils;

/**
 * A Domain Service responsible for validating the state of a Rule Aggregate. It checks for
 * invariants and business rule consistency.
 */
public class RuleValidator {

    private final Rule rule;

    public RuleValidator(Rule rule) {
        this.rule = rule;
    }

    /** Executes all validation checks against the Rule aggregate. */
    public void validate() {
        checkRequiredFields();
        checkRuleTypeSpecificLogic();
        validateActionsForRuleType();
    }

    /** Validates that all fundamental fields are present. */
    private void checkRequiredFields() {
        if (this.rule.getId() == null) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule ID");
        }
        if (this.rule.getName() == null || this.rule.getName().isBlank()) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule name");
        }
        if (this.rule.getRuleType() == null) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule type");
        }
        if (this.rule.getEffectiveDates() == null) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule effective dates");
        }
        if (this.rule.getPriority() == null) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule priority");
        }
        if (this.rule.getRuleStatus() == null) {
            throw new RuleDomainException(RULE_FIELD_CANNOT_BE_NULL, "Rule status");
        }
        if (CollectionUtils.isEmpty(this.rule.getConditionSetGroups())) {
            throw new RuleDomainException(RULE_CONDITION_NODE_LIST_CANNOT_BE_EMPTY);
        }
    }

    /** Validates logic specific to the RuleType. */
    private void checkRuleTypeSpecificLogic() {
        RuleTypeValidator validator = RuleTypeSpecificValidatorFactory.getValidator(rule.getRuleType());
        validator.validateRuleTypeSpecificLogic(rule);
    }

    /** Validates that all Actions are valid for the Rule's specific RuleType. */
    private void validateActionsForRuleType() {
        RuleType ruleType = this.rule.getRuleType();

        for (ConditionSetGroup conditionSetGroup : this.rule.getConditionSetGroups()) {
            for (ConditionSet conditionSet : conditionSetGroup.getConditionSets()) {
                for (Action action : conditionSet.getActions()) {
                    if (!action.isValidForRuleType(ruleType)) {
                        throw new RuleDomainException(
                                RULE_TYPE_INCOMPATIBLE_WITH_ACTION_TYPE,
                                action.getActionType().toString());
                    }
                }
            }
        }
    }
}
