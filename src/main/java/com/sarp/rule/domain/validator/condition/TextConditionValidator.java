package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.util.List;

public class TextConditionValidator extends BaseConditionValidator {

    protected TextConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        try {
            values.forEach(value -> {
                String text = objectMapper.convertValue(value, String.class);
                if (text == null || text.isBlank()) {
                    throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
                }
            });
        } catch (IllegalArgumentException e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }
}
