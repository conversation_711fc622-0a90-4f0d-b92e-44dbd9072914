package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ListConditionValidator extends BaseConditionValidator {

    protected ListConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        Criteria criteria = condition.getCriteria();

        Set<String> allowedSet = new HashSet<>(criteria.getAllowedValues());
        values.forEach(value -> {
            try {
                String stringValue = objectMapper.convertValue(value, String.class);
                if (!allowedSet.contains(stringValue)) {
                    throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
                }
            } catch (IllegalArgumentException e) {
                throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
            }
        });
    }
}
