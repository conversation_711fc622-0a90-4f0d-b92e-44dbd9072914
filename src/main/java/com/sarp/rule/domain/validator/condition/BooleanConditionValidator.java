package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.util.List;

public class <PERSON>oleanConditionValidator extends BaseConditionValidator {

    protected BooleanConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        try {
            values.forEach(value -> objectMapper.convertValue(value, Boolean.class));
        } catch (IllegalArgumentException e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }
}
