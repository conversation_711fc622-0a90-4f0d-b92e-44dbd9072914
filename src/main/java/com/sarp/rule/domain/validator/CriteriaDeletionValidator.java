package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_HAS_CONDITION_SET;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.domain.exception.CriteriaDomainException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;

@DomainService
@RequiredArgsConstructor
public class CriteriaDeletionValidator {
    /**
     * Validates if a criteria can be deleted based on the count of its associated criteria.
     *
     * @param criteriaId The ID of the criteria to validate.
     * @param isAssociatedConditionExist Flag indicating whether associated conditions exist
     * @throws CriteriaDomainException if the criteria cannot be deleted.
     */
    public void ensureDeletable(UUID criteriaId, boolean isAssociatedConditionExist) {
        if (isAssociatedConditionExist) {
            throw new CriteriaDomainException(CRITERIA_HAS_CONDITION_SET, criteriaId);
        }
    }
}
