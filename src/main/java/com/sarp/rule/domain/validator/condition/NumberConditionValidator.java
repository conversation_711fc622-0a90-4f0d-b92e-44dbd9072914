package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;
import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_OUT_OF_RANGE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.math.BigDecimal;
import java.util.List;

public class NumberConditionValidator extends BaseConditionValidator {

    protected NumberConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        Criteria criteria = condition.getCriteria();

        BigDecimal minValue = criteria.getMinValue();
        BigDecimal maxValue = criteria.getMaxValue();
        try {
            values.forEach(value -> {
                BigDecimal intValue = objectMapper.convertValue(value, BigDecimal.class);
                if ((minValue != null && intValue.compareTo(minValue) < 0)
                        || (maxValue != null && intValue.compareTo(maxValue) > 0)) {
                    throw new ConditionDomainException(CONDITION_VALUE_OUT_OF_RANGE);
                }
            });
        } catch (IllegalArgumentException e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }
}
