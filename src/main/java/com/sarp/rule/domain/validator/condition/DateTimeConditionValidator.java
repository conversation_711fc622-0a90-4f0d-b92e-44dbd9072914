package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_NOT_VALID;
import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_VALUE_OUT_OF_RANGE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.ConditionDomainException;
import java.time.LocalDateTime;
import java.util.List;

public class DateTimeConditionValidator extends BaseConditionValidator {

    protected DateTimeConditionValidator(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public void validateValues(Condition condition) {
        List<Object> values = condition.getValue();
        Criteria criteria = condition.getCriteria();

        LocalDateTime startDateTime = criteria.getStartDateTime();
        LocalDateTime endDateTime = criteria.getEndDateTime();
        try {
            values.forEach(value -> {
                LocalDateTime dateTime = objectMapper.convertValue(value, LocalDateTime.class);
                if ((startDateTime != null && dateTime.isBefore(startDateTime))
                        || (endDateTime != null && dateTime.isAfter(endDateTime))) {
                    throw new ConditionDomainException(CONDITION_VALUE_OUT_OF_RANGE);
                }
            });
        } catch (Exception e) {
            throw new ConditionDomainException(CONDITION_VALUE_NOT_VALID);
        }
    }
}
