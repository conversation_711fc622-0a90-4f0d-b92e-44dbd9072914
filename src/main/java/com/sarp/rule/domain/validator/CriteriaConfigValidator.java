package com.sarp.rule.domain.validator;

import static com.sarp.rule.domain.exception.messagecode.CriteriaConfigExceptionMessageCode.CRITERIA_CONFIG_CRITERIA_ID_DOES_NOT_EXIST;
import static com.sarp.rule.domain.exception.messagecode.CriteriaConfigExceptionMessageCode.CRITERIA_CONFIG_GROUP_ID_DOES_NOT_EXIST;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.domain.exception.CriteriaConfigDomainException;
import lombok.RequiredArgsConstructor;

@DomainService
@RequiredArgsConstructor
public class CriteriaConfigValidator {

    public void validateAllCriteriasExist(long requestedCriteriaCount, long existingCriteriaCount) {
        if (existingCriteriaCount != requestedCriteriaCount) {
            throw new CriteriaConfigDomainException(CRITERIA_CONFIG_CRITERIA_ID_DOES_NOT_EXIST);
        }
    }

    public void validateAllCriteriaGroupsExist(long requestedGroupCount, long existingGroupCount) {
        if (existingGroupCount != requestedGroupCount) {
            throw new CriteriaConfigDomainException(CRITERIA_CONFIG_GROUP_ID_DOES_NOT_EXIST);
        }
    }
}
