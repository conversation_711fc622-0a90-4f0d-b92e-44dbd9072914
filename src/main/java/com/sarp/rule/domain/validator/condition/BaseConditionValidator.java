package com.sarp.rule.domain.validator.condition;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.exception.ConditionDomainException;

public abstract class BaseConditionValidator implements ConditionValidator {

    protected final ObjectMapper objectMapper;

    protected BaseConditionValidator(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void validate(Condition condition) {
        checkRequiredFields(condition);
        validateValues(condition);
    }

    protected abstract void validateValues(Condition condition);

    public void checkRequiredFields(Condition condition) {
        if (condition.getOperator() == null || condition.getOperator().isEmpty()) {
            throw new ConditionDomainException(CONDITION_OPERATOR_CANNOT_BE_NULL);
        }
        if (condition.getValue() == null || condition.getValue().isEmpty()) {
            throw new ConditionDomainException(CONDITION_VALUE_CANNOT_BE_NULL);
        }
        if (condition.getCriteria() == null) {
            throw new ConditionDomainException(CONDITION_CRITERIA_CANNOT_BE_NULL);
        }
        if (!condition
                .getCriteria()
                .getAllowedOperators()
                .contains(ComparisonOperator.valueOf(condition.getOperator()))) {
            throw new ConditionDomainException(CONDITION_OPERATOR_NOT_ALLOWED);
        }
    }
}
