package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.ProductExceptionMessage.PRODUCT_VARIANT_ID_CANNOT_BE_NULL;

import com.sarp.core.domain.base.DomainEntity;
import com.sarp.rule.domain.exception.ProductDomainException;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode(of = "variantId")
@DomainEntity
public class Product {
    private UUID id;
    private UUID productId;
    private UUID variantId;
    private String name;
    private String iataCode;
    private String supplierName;
    private String retailerName;
    private String categoryName;
    private String parentCategoryName;
    private String description;

    private Product() {}

    private void validate() {
        if (variantId == null) {
            throw new ProductDomainException(PRODUCT_VARIANT_ID_CANNOT_BE_NULL);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final Product product;

        public Builder() {
            product = new Product();
        }

        public Builder id(UUID id) {
            product.id = id;
            return this;
        }

        public Builder productId(UUID productId) {
            product.productId = productId;
            return this;
        }

        public Builder variantId(UUID variantId) { // Or VariantId variantId
            product.variantId = variantId;
            return this;
        }

        public Builder name(String name) {
            product.name = name != null ? name.trim() : null;
            return this;
        }

        public Builder iataCode(String iataCode) {
            product.iataCode = iataCode != null ? iataCode.trim() : null;
            return this;
        }

        public Builder supplierName(String supplierName) {
            product.supplierName = supplierName != null ? supplierName.trim() : null;
            return this;
        }

        public Builder retailerName(String retailerName) {
            product.retailerName = retailerName != null ? retailerName.trim() : null;
            return this;
        }

        public Builder categoryName(String categoryName) {
            product.categoryName = categoryName != null ? categoryName.trim() : null;
            return this;
        }

        public Builder parentCategoryName(String parentCategoryName) {
            product.parentCategoryName = parentCategoryName != null ? parentCategoryName.trim() : null;
            return this;
        }

        public Builder description(String description) {
            product.description = description != null ? description.trim() : null;
            return this;
        }

        public Product build() {
            product.validate();
            return product;
        }
    }

    public Product updated(Product product) {
        return Product.builder()
                .id(this.id)
                .productId(product.getProductId())
                .variantId(product.getVariantId())
                .name(product.getName())
                .iataCode(product.getIataCode())
                .supplierName(product.getSupplierName())
                .retailerName(product.getRetailerName())
                .categoryName(product.getCategoryName())
                .parentCategoryName(product.getParentCategoryName())
                .description(product.getDescription())
                .build();
    }
}
