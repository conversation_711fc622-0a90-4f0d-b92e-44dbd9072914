package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.ConditionNodeExceptionMessageCode.CONDITION_ID_CANNOT_BE_NULL;

import com.sarp.rule.domain.exception.ConditionNodeDomainException;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode(of = "id")
public class ConditionNode {
    private UUID id;
    private Condition condition;
    private UUID nextNodeId;
    private UUID previousNodeId;

    private ConditionNode() {}

    private void validate() {
        if (condition == null) {
            throw new ConditionNodeDomainException(CONDITION_ID_CANNOT_BE_NULL);
        }
    }

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    public static ConditionNode.Builder builder() {
        return new ConditionNode.Builder();
    }

    public static class Builder {
        private final ConditionNode conditionNode;

        public Builder() {
            conditionNode = new ConditionNode();
            conditionNode.initialize();
        }

        public Builder id(UUID id) {
            conditionNode.id = id;
            return this;
        }

        public Builder condition(Condition condition) {
            conditionNode.condition = condition;
            return this;
        }

        public Builder nextNodeId(UUID nextNodeId) {
            conditionNode.nextNodeId = nextNodeId;
            return this;
        }

        public Builder previousNodeId(UUID previousNodeId) {
            conditionNode.previousNodeId = previousNodeId;
            return this;
        }

        public ConditionNode build() {
            conditionNode.validate();
            return conditionNode;
        }
    }
}
