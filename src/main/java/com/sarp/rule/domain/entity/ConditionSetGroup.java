package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupExceptionMessageCode.CONDITION_SET_GROUP_CONDITION_SETS_CANNOT_BE_NULL_OR_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupExceptionMessageCode.CONDITION_SET_GROUP_DESCRIPTION_CANNOT_BE_NULL_OR_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupExceptionMessageCode.CONDITION_SET_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.domain.exception.ConditionSetGroupDomainException;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

@ToString
@Getter
@EqualsAndHashCode(callSuper = false)
@AggregateRoot
public class ConditionSetGroup extends Audit {
    private UUID id;
    private String name;
    private String description;
    private Set<ConditionSet> conditionSets;

    private ConditionSetGroup() {}

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    private void validate() {

        if (name == null || name.isBlank()) {
            throw new ConditionSetGroupDomainException(CONDITION_SET_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (description == null || description.isBlank()) {
            throw new ConditionSetGroupDomainException(CONDITION_SET_GROUP_DESCRIPTION_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (CollectionUtils.isEmpty(conditionSets)) {
            throw new ConditionSetGroupDomainException(CONDITION_SET_GROUP_CONDITION_SETS_CANNOT_BE_NULL_OR_EMPTY);
        }
    }

    public static ConditionSetGroup.Builder builder() {
        return new ConditionSetGroup.Builder();
    }

    public ConditionSetGroup updated(String newName, String newDescription, Set<ConditionSet> newConditionSets) {
        return ConditionSetGroup.builder()
                .id(this.id)
                .name(newName != null ? newName : this.name)
                .description(newDescription != null ? newDescription : this.description)
                .conditionSets(newConditionSets != null ? newConditionSets : this.conditionSets)
                .build();
    }

    public static ConditionSetGroup createWithNewId(ConditionSetGroup conditionSetGroup) {
        return ConditionSetGroup.builder()
                .id(UUID.randomUUID())
                .name(conditionSetGroup.name)
                .description(conditionSetGroup.description)
                .conditionSets(conditionSetGroup.conditionSets)
                .build();
    }

    /**
     * Creates a new Builder instance initialized with the current state of this object. This allows
     * for the creation of a modified copy.
     *
     * @return A new pre-populated Builder.
     */
    public ConditionSetGroup.Builder toBuilder() {
        return new ConditionSetGroup.Builder()
                .id(this.id)
                .name(this.name)
                .description(this.description)
                .conditionSets(this.conditionSets)
                .createdAt(this.getCreatedAt())
                .updatedAt(this.getUpdatedAt())
                .createdBy(this.getCreatedBy())
                .updatedBy(this.getUpdatedBy());
    }

    public static class Builder {
        private final ConditionSetGroup conditionSetGroup;

        public Builder() {
            conditionSetGroup = new ConditionSetGroup();
            conditionSetGroup.initialize();
        }

        public Builder id(UUID id) {
            conditionSetGroup.id = id;
            return this;
        }

        public Builder name(String name) {
            conditionSetGroup.name = name != null ? name.trim() : null;
            return this;
        }

        public Builder description(String description) {
            conditionSetGroup.description = description != null ? description.trim() : null;
            return this;
        }

        public Builder conditionSets(Set<ConditionSet> conditionSets) {
            conditionSetGroup.conditionSets = conditionSets;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            conditionSetGroup.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(Instant updatedAt) {
            conditionSetGroup.setUpdatedAt(updatedAt);
            return this;
        }

        public Builder createdBy(String createdBy) {
            conditionSetGroup.setCreatedBy(createdBy);
            return this;
        }

        public Builder updatedBy(String updatedBy) {
            conditionSetGroup.setUpdatedBy(updatedBy);
            return this;
        }

        public ConditionSetGroup build() {
            conditionSetGroup.validate();
            return conditionSetGroup;
        }
    }
}
