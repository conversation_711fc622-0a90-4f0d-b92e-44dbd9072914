package com.sarp.rule.domain.entity;

import com.sarp.rule.domain.validator.condition.ConditionValidator;
import com.sarp.rule.domain.validator.condition.ConditionValidatorFactory;
import java.util.List;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode(of = "id")
public class Condition {
    private UUID id;
    private String operator;
    private List<Object> value;
    private Criteria criteria;

    private Condition() {}

    private void validate() {
        ConditionValidator conditionValidator = ConditionValidatorFactory.getValidator(criteria.getFieldType());
        conditionValidator.validate(this);
    }

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    public static Condition.Builder builder() {
        return new Condition.Builder();
    }

    public static class Builder {
        private final Condition condition;

        public Builder() {
            condition = new Condition();
            condition.initialize();
        }

        public Builder id(UUID id) {
            condition.id = id;
            return this;
        }

        public Builder operator(String operator) {
            condition.operator = operator;
            return this;
        }

        public Builder value(List<Object> value) {
            condition.value = value;
            return this;
        }

        public Builder criteria(Criteria criteria) {
            condition.criteria = criteria;
            return this;
        }

        public Condition build() {
            condition.validate();
            return condition;
        }
    }
}
