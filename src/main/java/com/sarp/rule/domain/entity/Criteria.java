package com.sarp.rule.domain.entity;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.validator.CriteriaValidator;
import com.sarp.rule.domain.valueobject.criteria.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

@Getter
@ToString
@EqualsAndHashCode(of = "id", callSuper = false)
@AggregateRoot
public class Criteria extends Audit {
    private UUID id;
    private String name;
    private String description;
    private CriteriaType type = CriteriaType.USER_DEFINED;
    private RequestType requestType = RequestType.OFFER_REQUEST;
    private String mappingField;
    private String systemDefinedCriteriaType;
    private FieldType fieldType;
    private SelectionType selectionType;
    private List<String> allowedValues;
    private BigDecimal minValue;
    private BigDecimal maxValue;
    private LocalDateTime startDateTime;
    private LocalDateTime endDateTime;
    private LocalTime startTime;
    private LocalTime endTime;
    private UUID criteriaGroupId;
    private List<ComparisonOperator> allowedOperators;

    private Criteria() {}

    private void validate() {
        CriteriaValidator validator = new CriteriaValidator(this);
        validator.validate();
    }

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }

        if (type == null) {
            type = CriteriaType.USER_DEFINED;
        }

        if (requestType == null) {
            requestType = RequestType.OFFER_REQUEST;
        }
    }

    public Criteria updated(String newName, String newDescription) {
        return Criteria.builder()
                .id(this.id)
                .name(newName != null ? newName : this.name)
                .description(newDescription != null ? newDescription : this.description)
                .type(this.type)
                .requestType(this.requestType)
                .mappingField(this.mappingField)
                .fieldType(this.fieldType)
                .selectionType(this.selectionType)
                .allowedValues(this.allowedValues)
                .minValue(this.minValue)
                .maxValue(this.maxValue)
                .startDateTime(this.startDateTime)
                .endDateTime(this.endDateTime)
                .startTime(this.startTime)
                .endTime(this.endTime)
                .criteriaGroupId(this.criteriaGroupId)
                .systemDefinedCriteriaType(this.systemDefinedCriteriaType)
                .build();
    }

    public static Criteria.Builder builder() {
        return new Criteria.Builder();
    }

    public void update(String name, String description) {
        if (name != null) this.name = name;
        if (description != null) this.description = description;
        this.validate();
    }

    public static class Builder {
        private final Criteria criteria;

        public Builder() {
            criteria = new Criteria();
            criteria.initialize();
        }

        public Criteria.Builder id(UUID id) {
            criteria.id = id;
            return this;
        }

        public Criteria.Builder name(String name) {
            criteria.name = name != null ? name.trim() : null;
            return this;
        }

        public Criteria.Builder description(String description) {
            criteria.description = description != null ? description.trim() : null;
            return this;
        }

        public Criteria.Builder type(CriteriaType type) {
            criteria.type = type;
            return this;
        }

        public Criteria.Builder requestType(RequestType requestType) {
            criteria.requestType = requestType;
            return this;
        }

        public Criteria.Builder mappingField(String mappingField) {
            criteria.mappingField = mappingField;
            return this;
        }

        public Criteria.Builder fieldType(FieldType fieldType) {
            criteria.fieldType = fieldType;
            return this;
        }

        public Criteria.Builder selectionType(SelectionType selectionType) {
            criteria.selectionType = selectionType;
            return this;
        }

        public Criteria.Builder allowedValues(List<String> allowedValues) {
            criteria.allowedValues = allowedValues;
            return this;
        }

        public Criteria.Builder minValue(BigDecimal minValue) {
            criteria.minValue = minValue;
            return this;
        }

        public Criteria.Builder maxValue(BigDecimal maxValue) {
            criteria.maxValue = maxValue;
            return this;
        }

        public Criteria.Builder startDateTime(LocalDateTime startDate) {
            criteria.startDateTime = startDate;
            return this;
        }

        public Criteria.Builder endDateTime(LocalDateTime endDate) {
            criteria.endDateTime = endDate;
            return this;
        }

        public Criteria.Builder startTime(LocalTime startTime) {
            criteria.startTime = startTime;
            return this;
        }

        public Criteria.Builder endTime(LocalTime endTime) {
            criteria.endTime = endTime;
            return this;
        }

        public Criteria.Builder criteriaGroupId(UUID criteriaGroupId) {
            criteria.criteriaGroupId = criteriaGroupId;
            return this;
        }

        public Criteria.Builder systemDefinedCriteriaType(String systemDefinedCriteriaType) {
            criteria.systemDefinedCriteriaType = systemDefinedCriteriaType;
            return this;
        }

        public Criteria.Builder createdAt(Instant createdAt) {
            criteria.setCreatedAt(createdAt);
            return this;
        }

        public Criteria.Builder createdBy(String createdBy) {
            criteria.setCreatedBy(createdBy);
            return this;
        }

        public Criteria.Builder updatedAt(Instant updatedAt) {
            criteria.setUpdatedAt(updatedAt);
            return this;
        }

        public Criteria.Builder updatedBy(String updatedBy) {
            criteria.setUpdatedBy(updatedBy);
            return this;
        }

        public Builder allowedOperators(List<ComparisonOperator> allowedOperators) {
            if (CollectionUtils.isEmpty(allowedOperators)) {
                criteria.allowedOperators = criteria.fieldType.getAllowedOperators();
            } else {
                criteria.allowedOperators = allowedOperators;
            }
            return this;
        }

        public Criteria build() {
            criteria.validate();
            return criteria;
        }
    }
}
