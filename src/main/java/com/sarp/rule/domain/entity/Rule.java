package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.*;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.domain.command.rule.ActivateRuleCommand;
import com.sarp.rule.domain.command.rule.UpdateRuleCommand;
import com.sarp.rule.domain.exception.RuleDomainException;
import com.sarp.rule.domain.service.product.ProductMappingFactory;
import com.sarp.rule.domain.service.product.ProductMappingStrategy;
import com.sarp.rule.domain.validator.RuleValidator;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import com.sarp.rule.domain.valueobject.rule.RuleStatus;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

@AggregateRoot
@ToString
@Getter
@EqualsAndHashCode(of = "id", callSuper = false)
public class Rule extends Audit {
    private UUID id;
    private String name;
    private RuleType ruleType;
    private Short priority;
    private EffectiveDates effectiveDates;
    private RuleStatus ruleStatus;

    private Set<ConditionSetGroup> conditionSetGroups;
    private Set<Bundle> bundles;
    private Set<Product> products;

    // Private constructor to enforce creation via Builder
    private Rule() {}

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    public void validate() {
        RuleValidator validator = new RuleValidator(this);
        validator.validate();
    }

    public Rule withUpdatedDirectProducts(Set<Product> newProducts) {
        return Rule.builder()
                .id(this.id)
                .name(this.name)
                .ruleType(this.ruleType)
                .priority(this.priority)
                .effectiveDates(this.effectiveDates)
                .ruleStatus(this.ruleStatus)
                .conditionSetGroups(this.conditionSetGroups)
                .bundles(this.bundles)
                .products(newProducts)
                .build();
    }

    public Rule withUpdatedBundles(Set<Bundle> updatedBundles) {
        return Rule.builder()
                .id(this.id)
                .name(this.name)
                .ruleType(this.ruleType)
                .priority(this.priority)
                .effectiveDates(this.effectiveDates)
                .ruleStatus(this.ruleStatus)
                .conditionSetGroups(this.conditionSetGroups)
                .bundles(updatedBundles)
                .products(this.products)
                .build();
    }

    public Rule enrichWithProducts(List<Product> foundProducts) {
        ProductMappingStrategy strategy = ProductMappingFactory.getStrategy(this.ruleType);
        return strategy.enrichRuleWithProducts(this, foundProducts);
    }

    private Set<ConditionSetGroup> takeOwnershipOfConditionSetGroups(Set<ConditionSetGroup> conditionSetGroups) {
        if (CollectionUtils.isEmpty(conditionSetGroups)) {
            throw new RuleDomainException(RULE_CONDITION_SET_GROUP_CANNOT_BE_NULL);
        }

        return conditionSetGroups.stream()
                .map(conditionSetGroup -> {
                    Set<ConditionSet> handledConditionSets = conditionSetGroup.getConditionSets().stream()
                            .map(cs -> {
                                Set<Action> handledActions = cs.getActions().stream()
                                        .map(action -> action.copyWithNewRuleId(this.id))
                                        .collect(Collectors.toSet());

                                return cs.toBuilder().actions(handledActions).build();
                            })
                            .collect(Collectors.toSet());

                    return conditionSetGroup.toBuilder()
                            .conditionSets(handledConditionSets)
                            .build();
                })
                .collect(Collectors.toSet());
    }

    public List<UUID> variantIds() {
        ProductMappingStrategy strategy = ProductMappingFactory.getStrategy(this.ruleType);
        return strategy.extractProductVariantIds(this);
    }

    public Rule updated(
            UpdateRuleCommand command,
            Set<Bundle> bundles,
            Set<Product> products,
            Set<ConditionSetGroup> conditionSetGroups) {
        return Rule.builder()
                .id(this.id)
                .name(command.getName() != null ? command.getName() : this.name)
                .ruleType(command.getRuleType() != null ? command.getRuleType() : this.ruleType)
                .priority(command.getPriority() != null ? command.getPriority() : this.priority)
                .effectiveDates(command.getEffectiveDates() != null ? command.getEffectiveDates() : this.effectiveDates)
                .saveAsDraft(command.isSaveAsDraft())
                .conditionSetGroups(conditionSetGroups)
                .bundles(bundles)
                .products(products)
                .build();
    }

    public Rule updateRuleStatusToInReview(ActivateRuleCommand command) {
        if (this.getRuleStatus() != RuleStatus.EXPIRED && this.getRuleStatus() != RuleStatus.PASSIVE) {
            throw new RuleDomainException(INVALID_RULE_STATUS_FOR_ACTIVATION);
        }

        if (this.getRuleStatus() == RuleStatus.EXPIRED && command.getEffectiveDates() == null) {
            throw new RuleDomainException(EFFECTIVE_DATES_CANNOT_BE_NULL);
        }

        return Rule.builder()
                .id(command.getId() != null ? command.getId() : this.id)
                .name(this.name)
                .ruleType(this.ruleType)
                .priority(this.priority)
                .effectiveDates(command.getEffectiveDates() != null ? command.getEffectiveDates() : this.effectiveDates)
                .conditionSetGroups(this.conditionSetGroups)
                .bundles(this.bundles)
                .products(this.products)
                .ruleStatus(RuleStatus.IN_REVIEW)
                .build();
    }

    public static Rule.Builder builder() {
        return new Rule.Builder();
    }

    public static class Builder {
        private final Rule rule;

        public Builder() {
            rule = new Rule();
            rule.initialize();
        }

        public Builder id(UUID id) {
            rule.id = id;
            return this;
        }

        public Builder name(String name) {
            rule.name = name;
            return this;
        }

        public Builder ruleType(RuleType ruleType) {
            rule.ruleType = ruleType;
            return this;
        }

        public Builder priority(Short priority) {
            rule.priority = priority;
            return this;
        }

        public Builder saveAsDraft(boolean saveAsDraft) {
            rule.ruleStatus = saveAsDraft ? RuleStatus.DRAFT : RuleStatus.ACTIVE;
            return this;
        }

        public Builder effectiveDates(EffectiveDates effectiveDates) {
            rule.effectiveDates = effectiveDates;
            return this;
        }

        public Builder ruleStatus(RuleStatus ruleStatus) {
            rule.ruleStatus = ruleStatus;
            return this;
        }

        public Builder conditionSetGroups(Set<ConditionSetGroup> conditionSetGroups) {
            rule.conditionSetGroups = rule.takeOwnershipOfConditionSetGroups(conditionSetGroups);
            return this;
        }

        public Builder bundles(Set<Bundle> bundles) {
            rule.bundles = bundles;
            return this;
        }

        public Builder products(Set<Product> products) {
            rule.products = products;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            rule.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(Instant updatedAt) {
            rule.setUpdatedAt(updatedAt);
            return this;
        }

        public Builder createdBy(String createdBy) {
            rule.setCreatedBy(createdBy);
            return this;
        }

        public Builder updatedBy(String updatedBy) {
            rule.setUpdatedBy(updatedBy);
            return this;
        }

        /**
         * Builds the Rule and ensures its validity. This is the enforcement point. Only a valid
         * Rule can be constructed.
         *
         * @return A fully validated Rule aggregate.
         * @throws RuleDomainException if validation fails.
         */
        public Rule build() {
            rule.validate();
            return rule;
        }
    }
}
