package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.RuleActionExceptionMessageCode.RULE_ACTION_ACTION_TYPE_CANNOT_BE_NULL;
import static com.sarp.rule.domain.exception.messagecode.RuleActionExceptionMessageCode.RULE_ACTION_PARAMETERS_CANNOT_BE_NULL;

import com.sarp.rule.domain.exception.RuleActionDomainException;
import com.sarp.rule.domain.valueobject.action.ActionType;
import com.sarp.rule.domain.valueobject.action.Parameters;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
@EqualsAndHashCode(exclude = "ruleId")
public class Action {

    private UUID id;

    private ActionType actionType;

    private Parameters parameters;

    private UUID ruleId;

    private Action() {}

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    private void validate() {
        if (actionType == null) {
            throw new RuleActionDomainException(RULE_ACTION_ACTION_TYPE_CANNOT_BE_NULL);
        }

        if (parameters == null) {
            throw new RuleActionDomainException(RULE_ACTION_PARAMETERS_CANNOT_BE_NULL);
        }
    }

    /**
     * Checks if this action is valid for the given RuleType by delegating the check to the RuleType
     * enum itself. This removes the responsibility of knowing compatibility rules from the Action
     * class.
     *
     * @param ruleType The type of the rule to validate against.
     * @return true if the action is valid for the rule type, false otherwise.
     */
    public boolean isValidForRuleType(RuleType ruleType) {
        if (this.actionType == null || ruleType == null) {
            return false;
        }
        return ruleType.isCompatibleActionType(this.actionType);
    }

    public Action copyWithNewRuleId(UUID ruleId) {
        return Action.builder()
                .id(this.id)
                .actionType(this.actionType)
                .parameters(this.parameters)
                .ruleId(ruleId)
                .build();
    }

    public static Action.Builder builder() {
        return new Action.Builder();
    }

    public static class Builder {
        private final Action action;

        public Builder() {
            action = new Action();
            action.initialize();
        }

        public Builder id(UUID id) {
            action.id = id;
            return this;
        }

        public Builder actionType(ActionType actionType) {
            action.actionType = actionType;
            return this;
        }

        public Builder parameters(Parameters parameters) {
            action.parameters = parameters;
            return this;
        }

        public Builder ruleId(UUID ruleId) {
            action.ruleId = ruleId;
            return this;
        }

        public Action build() {
            action.validate();
            return action;
        }
    }
}
