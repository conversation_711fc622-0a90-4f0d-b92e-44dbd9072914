package com.sarp.rule.domain.entity;

import com.sarp.rule.domain.validator.PortValidator;
import java.math.BigDecimal;
import lombok.*;

@ToString
@Getter
@EqualsAndHashCode(of = "portCode")
public class Port {
    private String portCode;
    private String portName;
    private String cityCode;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String portType;
    private boolean hasComfort;
    private boolean hasConvertibleCurrency;
    private boolean roundTripMandatory;
    private boolean domestic;
    private boolean active;
    private boolean refundable;
    private boolean award;
    private boolean starAward;
    private boolean ajetActive;
    private boolean spa;
    private boolean spaArrival;
    private boolean activeForWebAgent;
    private boolean deleted;

    // Private constructor to enforce builder usage
    private Port() {}

    private void validate() {
        PortValidator portValidator = new PortValidator(this);
        portValidator.validate();
    }

    public static Port.Builder builder() {
        return new Port.Builder();
    }

    public static class Builder {
        private final Port port;

        public Builder() {
            port = new Port();
        }

        public Builder portCode(String portCode) {
            port.portCode = portCode;
            return this;
        }

        public Builder portName(String portName) {
            port.portName = portName;
            return this;
        }

        public Builder cityCode(String cityCode) {
            port.cityCode = cityCode;
            return this;
        }

        public Builder longitude(BigDecimal longitude) {
            port.longitude = longitude;
            return this;
        }

        public Builder latitude(BigDecimal latitude) {
            port.latitude = latitude;
            return this;
        }

        public Builder portType(String portType) {
            port.portType = portType;
            return this;
        }

        public Builder hasComfort(boolean hasComfort) {
            port.hasComfort = hasComfort;
            return this;
        }

        public Builder hasConvertibleCurrency(boolean hasConvertibleCurrency) {
            port.hasConvertibleCurrency = hasConvertibleCurrency;
            return this;
        }

        public Builder roundTripMandatory(boolean isRoundTripMandatory) {
            port.roundTripMandatory = isRoundTripMandatory;
            return this;
        }

        public Builder domestic(boolean isDomestic) {
            port.domestic = isDomestic;
            return this;
        }

        public Builder active(boolean isActive) {
            port.active = isActive;
            return this;
        }

        public Builder refundable(boolean isRefundable) {
            port.refundable = isRefundable;
            return this;
        }

        public Builder award(boolean isAward) {
            port.award = isAward;
            return this;
        }

        public Builder starAward(boolean isStarAward) {
            port.starAward = isStarAward;
            return this;
        }

        public Builder ajetActive(boolean ajetActive) {
            port.ajetActive = ajetActive;
            return this;
        }

        public Builder spa(boolean isSpa) {
            port.spa = isSpa;
            return this;
        }

        public Builder spaArrival(boolean isSpaArrival) {
            port.spaArrival = isSpaArrival;
            return this;
        }

        public Builder activeForWebAgent(boolean isActiveForWebAgent) {
            port.activeForWebAgent = isActiveForWebAgent;
            return this;
        }

        public Builder deleted(boolean isDeleted) {
            port.deleted = isDeleted;
            return this;
        }

        public Port build() {
            port.validate();
            return port;
        }
    }
}
