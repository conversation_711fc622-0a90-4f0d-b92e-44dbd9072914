package com.sarp.rule.domain.entity;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@AggregateRoot
@ToString
@Getter
@EqualsAndHashCode(of = "id", callSuper = false)
public class Market extends Audit {
    private UUID id;
    private String name;
    private Set<Port> ports;

    // Private constructor to enforce builder usage
    private Market() {}

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    public void validate() {
        // to be implemented
    }

    public Market updated(UpdateMarketCommand updateMarketCommand, Set<Port> newPorts) {
        return Market.builder()
                .id(this.id)
                .name(
                        updateMarketCommand.getMarket().getName() != null
                                ? updateMarketCommand.getMarket().getName()
                                : this.name)
                .ports(newPorts)
                .createdBy(this.getCreatedBy())
                .createdAt(this.getCreatedAt())
                .build();
    }

    public static Market.Builder builder() {
        return new Market.Builder();
    }

    public static class Builder {
        private final Market market;

        public Builder() {
            market = new Market();
            market.initialize();
        }

        public Builder id(UUID id) {
            market.id = id;
            return this;
        }

        public Builder name(String name) {
            market.name = name;
            return this;
        }

        public Builder ports(Set<Port> ports) {
            market.ports = ports;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            market.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(Instant updatedAt) {
            market.setUpdatedAt(updatedAt);
            return this;
        }

        public Builder createdBy(String createdBy) {
            market.setCreatedBy(createdBy);
            return this;
        }

        public Builder updatedBy(String updatedBy) {
            market.setUpdatedBy(updatedBy);
            return this;
        }

        public Market build() {
            market.validate();
            return market;
        }
    }
}
