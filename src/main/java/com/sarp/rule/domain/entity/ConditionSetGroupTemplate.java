package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupTemplateExceptionMessageCode.CONDITION_SET_GROUP_TEMPLATE_AUTHOR_CANNOT_BE_NULL_OR_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupTemplateExceptionMessageCode.CONDITION_SET_GROUP_TEMPLATE_MUST_HAVE_AT_LEAST_ONE_CONDITION_SET_GROUP_ID;
import static com.sarp.rule.domain.exception.messagecode.ConditionSetGroupTemplateExceptionMessageCode.CONDITION_SET_GROUP_TEMPLATE_NAME_CANNOT_BE_NULL_OR_EMPTY;

import com.sarp.core.domain.base.DomainEntity;
import com.sarp.rule.domain.exception.ConditionSetGroupTemplateDomainException;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
@EqualsAndHashCode(of = "id", callSuper = false)
@DomainEntity
public class ConditionSetGroupTemplate extends Audit {

    private UUID id;
    private String name;
    private String author;
    private Set<UUID> conditionSetGroupIds;

    private ConditionSetGroupTemplate() {}

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    private void validate() {

        if (name == null || name.isBlank()) {
            throw new ConditionSetGroupTemplateDomainException(
                    CONDITION_SET_GROUP_TEMPLATE_NAME_CANNOT_BE_NULL_OR_EMPTY);
        }
        if (author == null || author.isBlank()) {
            throw new ConditionSetGroupTemplateDomainException(
                    CONDITION_SET_GROUP_TEMPLATE_AUTHOR_CANNOT_BE_NULL_OR_EMPTY);
        }
        if (conditionSetGroupIds != null && conditionSetGroupIds.isEmpty()) {
            throw new ConditionSetGroupTemplateDomainException(
                    CONDITION_SET_GROUP_TEMPLATE_MUST_HAVE_AT_LEAST_ONE_CONDITION_SET_GROUP_ID);
        }
    }

    public ConditionSetGroupTemplate updated(Set<UUID> conditionSetGroupIds) {
        return ConditionSetGroupTemplate.builder()
                .id(this.id)
                .name(this.name)
                .author(this.author)
                .conditionSetGroupIds(conditionSetGroupIds)
                .build();
    }

    public static ConditionSetGroupTemplate.Builder builder() {
        return new ConditionSetGroupTemplate.Builder();
    }

    public static class Builder {
        private final ConditionSetGroupTemplate conditionSetGroupTemplate;

        public Builder() {
            conditionSetGroupTemplate = new ConditionSetGroupTemplate();
            conditionSetGroupTemplate.initialize();
        }

        public ConditionSetGroupTemplate.Builder id(UUID id) {
            conditionSetGroupTemplate.id = id;
            return this;
        }

        public ConditionSetGroupTemplate.Builder name(String name) {
            conditionSetGroupTemplate.name = name;
            return this;
        }

        public ConditionSetGroupTemplate.Builder author(String author) {
            conditionSetGroupTemplate.author = author;
            return this;
        }

        public ConditionSetGroupTemplate.Builder conditionSetGroupIds(Set<UUID> conditionSetGroupIds) {
            conditionSetGroupTemplate.conditionSetGroupIds = conditionSetGroupIds;
            return this;
        }

        public ConditionSetGroupTemplate.Builder createdAt(Instant createdAt) {
            conditionSetGroupTemplate.setCreatedAt(createdAt);
            return this;
        }

        public ConditionSetGroupTemplate.Builder updatedAt(Instant updatedAt) {
            conditionSetGroupTemplate.setUpdatedAt(updatedAt);
            return this;
        }

        public ConditionSetGroupTemplate.Builder createdBy(String createdBy) {
            conditionSetGroupTemplate.setCreatedBy(createdBy);
            return this;
        }

        public ConditionSetGroupTemplate.Builder updatedBy(String updatedBy) {
            conditionSetGroupTemplate.setUpdatedBy(updatedBy);
            return this;
        }

        public ConditionSetGroupTemplate build() {
            conditionSetGroupTemplate.validate();
            return conditionSetGroupTemplate;
        }
    }
}
