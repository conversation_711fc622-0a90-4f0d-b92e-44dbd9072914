package com.sarp.rule.domain.entity;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.domain.exception.BundleDomainException;
import com.sarp.rule.domain.validator.BundleValidator;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.bundle.BundleType;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.time.Instant;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
@EqualsAndHashCode(of = "id", callSuper = false)
@AggregateRoot
public class Bundle extends Audit {

    private UUID id;
    private String name;
    private String description;
    private BundleType type;
    private BundleStatus status;
    private Set<ProductWithQuantity> productWithQuantities;
    private byte[] image;

    private void validate() {
        BundleValidator validator = new BundleValidator(this);
        validator.validate();
    }

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        if (status == null) {
            status = BundleStatus.ACTIVE;
        }
    }

    /**
     * Updates the bundle's products based on a mapping of found products while preserving quantities.
     * This method is specifically designed for the product update use case in RuleType, where
     * products need to be updated while maintaining their existing quantities and IDs.
     *
     * @param foundProductsByVariantId Map of variant IDs to updated Product instances. If a product
     *                                is not found in this map, the existing product is retained.
     * @return A new Bundle instance with updated products and preserved quantities
     * @see ProductWithQuantity
     */
    public Bundle withUpdatedProducts(Map<UUID, Product> foundProductsByVariantId) {
        Set<ProductWithQuantity> updatedProductWithQuantities = new HashSet<>();

        if (this.productWithQuantities != null) {
            for (ProductWithQuantity productWithQuantity : this.productWithQuantities) {
                Product updatedProduct = foundProductsByVariantId.getOrDefault(
                        productWithQuantity.product().getVariantId(), productWithQuantity.product());

                ProductWithQuantity updatedProductWithQuantity =
                        ProductWithQuantity.of(updatedProduct, productWithQuantity.quantity());
                updatedProductWithQuantities.add(updatedProductWithQuantity);
            }
        }

        return Bundle.builder()
                .id(this.id)
                .name(this.name)
                .description(this.description)
                .type(this.type)
                .status(this.status)
                .productWithQuantities(updatedProductWithQuantities)
                .image(this.image)
                .build();
    }

    public Bundle updated(
            String newName,
            String newDescription,
            BundleType newType,
            BundleStatus newStatus,
            Set<ProductWithQuantity> newProductWithQuantities) {

        return Bundle.builder()
                .id(this.id)
                .name(newName != null ? newName : this.name)
                .description(newDescription != null ? newDescription : this.description)
                .type(newType != null ? newType : this.type)
                .status(newStatus != null ? newStatus : this.status)
                .productWithQuantities(
                        newProductWithQuantities != null
                                ? new HashSet<>(newProductWithQuantities)
                                : this.productWithQuantities)
                .image(this.image)
                .build();
    }

    public void addProductWithQuantities(Set<ProductWithQuantity> productWithQuantities) {
        if (this.productWithQuantities == null) {
            this.productWithQuantities = productWithQuantities;
        } else {
            this.productWithQuantities.addAll(productWithQuantities);
        }
    }

    public static Bundle.Builder builder() {
        return new Bundle.Builder();
    }

    public static class Builder {
        private final Bundle bundle;

        public Builder() {
            bundle = new Bundle();
            bundle.initialize();
        }

        public Builder id(UUID id) {
            bundle.id = id;
            return this;
        }

        public Builder name(String name) {
            bundle.name = (name != null) ? name.trim() : null;
            return this;
        }

        public Builder description(String description) {
            bundle.description = (description != null) ? description.trim() : null;
            return this;
        }

        public Builder type(BundleType type) {
            bundle.type = type;
            return this;
        }

        public Builder productWithQuantities(Set<ProductWithQuantity> productWithQuantities) {
            bundle.productWithQuantities = productWithQuantities;
            return this;
        }

        public Builder image(byte[] image) {
            bundle.image = image;
            return this;
        }

        public Builder status(BundleStatus status) {
            bundle.status = status;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            bundle.setCreatedAt(createdAt);
            return this;
        }

        public Builder createdBy(String createdBy) {
            bundle.setCreatedBy(createdBy);
            return this;
        }

        public Builder updatedAt(Instant updatedAt) {
            bundle.setUpdatedAt(updatedAt);
            return this;
        }

        public Builder updatedBy(String updatedBy) {
            bundle.setUpdatedBy(updatedBy);
            return this;
        }

        /**
         * Builds the Bundle and ensures its validity. This is the enforcement point. Only a valid
         * Bundle can be constructed.
         *
         * @return A fully validated Bundle aggregate.
         * @throws BundleDomainException if validation fails.
         */
        public Bundle build() {
            bundle.validate();
            return bundle;
        }
    }
}
