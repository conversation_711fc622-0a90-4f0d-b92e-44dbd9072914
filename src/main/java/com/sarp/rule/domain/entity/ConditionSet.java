package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.ConditionSetExceptionMessageCode.ACTIONS_IN_CONDITION_SET_MUST_NOT_HAVE_THE_SAME_ACTION_TYPE;
import static com.sarp.rule.domain.exception.messagecode.ConditionSetExceptionMessageCode.CONDITION_SET_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY;

import com.sarp.rule.domain.exception.ConditionSetDomainException;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
@EqualsAndHashCode
public class ConditionSet {
    private UUID id;
    private String displayOrder;
    private Set<ConditionNode> conditionNodes;
    private Set<Action> actions;

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }

    private void validate() {
        if (displayOrder == null || displayOrder.isBlank()) {
            throw new ConditionSetDomainException(CONDITION_SET_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (actions != null
                && actions.stream().map(Action::getActionType).distinct().count() != actions.size()) {
            throw new ConditionSetDomainException(ACTIONS_IN_CONDITION_SET_MUST_NOT_HAVE_THE_SAME_ACTION_TYPE);
        }
    }

    public static ConditionSet.Builder builder() {
        return new ConditionSet.Builder();
    }

    public ConditionSet updated(String newDisplayOrder, Set<ConditionNode> newConditionNodes, Set<Action> newActions) {
        return ConditionSet.builder()
                .id(this.id)
                .displayOrder(newDisplayOrder != null ? newDisplayOrder : this.displayOrder)
                .conditionNodes(newConditionNodes != null ? newConditionNodes : this.conditionNodes)
                .actions(newActions != null ? newActions : this.actions)
                .build();
    }

    /**
     * Creates a new Builder instance initialized with the current state of this object. This allows
     * for the creation of a modified copy.
     *
     * @return A new pre-populated Builder.
     */
    public ConditionSet.Builder toBuilder() {
        return new ConditionSet.Builder()
                .id(this.id)
                .displayOrder(this.displayOrder)
                .conditionNodes(this.conditionNodes)
                .actions(this.actions);
    }

    public static class Builder {
        private final ConditionSet conditionSet;

        public Builder() {
            conditionSet = new ConditionSet();
            conditionSet.initialize();
        }

        public ConditionSet.Builder id(UUID id) {
            conditionSet.id = id;
            return this;
        }

        public ConditionSet.Builder conditionNodes(Set<ConditionNode> conditionNodes) {
            conditionSet.conditionNodes = conditionNodes;
            return this;
        }

        public ConditionSet.Builder displayOrder(String displayOrder) {
            conditionSet.displayOrder = displayOrder != null ? displayOrder.trim() : null;
            return this;
        }

        public ConditionSet.Builder actions(Set<Action> actions) {
            conditionSet.actions = actions;
            return this;
        }

        public ConditionSet build() {
            conditionSet.validate();
            return conditionSet;
        }
    }
}
