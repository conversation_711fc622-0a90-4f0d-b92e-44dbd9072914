package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.CriteriaConfigExceptionMessageCode.CRITERIA_CONFIG_CRITERIA_CANNOT_BE_NULL;
import static com.sarp.rule.domain.exception.messagecode.CriteriaConfigExceptionMessageCode.CRITERIA_CONFIG_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.CriteriaConfigExceptionMessageCode.CRITERIA_CONFIG_ID_CANNOT_BE_NULL;

import com.sarp.rule.domain.exception.CriteriaConfigDomainException;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode(of = "id")
public class CriteriaConfig {
    private UUID id;
    private Integer displayOrder;
    private List<RuleType> allowedRules;
    private List<RuleType> mandatoryRules;
    private UUID criteriaId;

    private CriteriaConfig() {}

    private void validate() {
        if (id == null) {
            throw new CriteriaConfigDomainException(CRITERIA_CONFIG_ID_CANNOT_BE_NULL);
        }

        if (displayOrder == null) {
            throw new CriteriaConfigDomainException(CRITERIA_CONFIG_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY);
        }

        if (criteriaId == null) {
            throw new CriteriaConfigDomainException(CRITERIA_CONFIG_CRITERIA_CANNOT_BE_NULL);
        }
    }

    private void initialize() {
        if (id == null) {
            id = UUID.randomUUID();
        }

        if (allowedRules == null) {
            allowedRules = List.of();
        }

        if (mandatoryRules == null) {
            mandatoryRules = List.of();
        }
    }

    public static CriteriaConfig.Builder builder() {
        return new CriteriaConfig.Builder();
    }

    public static class Builder {
        private final CriteriaConfig criteriaConfig;

        public Builder() {
            criteriaConfig = new CriteriaConfig();
            criteriaConfig.initialize();
        }

        public CriteriaConfig.Builder id(UUID id) {
            criteriaConfig.id = id;
            return this;
        }

        public CriteriaConfig.Builder displayOrder(Integer displayOrder) {
            criteriaConfig.displayOrder = displayOrder;
            return this;
        }

        public CriteriaConfig.Builder allowedRules(List<RuleType> allowedRules) {
            criteriaConfig.allowedRules = allowedRules;
            return this;
        }

        public CriteriaConfig.Builder mandatoryRules(List<RuleType> mandatoryRules) {
            criteriaConfig.mandatoryRules = mandatoryRules;
            return this;
        }

        public CriteriaConfig.Builder criteriaId(UUID criteriaId) {
            criteriaConfig.criteriaId = criteriaId;
            return this;
        }

        public CriteriaConfig build() {
            criteriaConfig.validate();
            return criteriaConfig;
        }
    }

    public CriteriaConfig update(Integer displayOrder, List<RuleType> allowedRules, List<RuleType> mandatoryRules) {
        return CriteriaConfig.builder()
                .id(getId())
                .criteriaId(getCriteriaId())
                .displayOrder(displayOrder)
                .allowedRules(allowedRules)
                .mandatoryRules(mandatoryRules)
                .build();
    }

    public static CriteriaConfig of(
            UUID criteriaId, Integer displayOrder, List<RuleType> allowedRules, List<RuleType> mandatoryRules) {
        return CriteriaConfig.builder()
                .criteriaId(criteriaId)
                .displayOrder(displayOrder)
                .allowedRules(allowedRules)
                .mandatoryRules(mandatoryRules)
                .build();
    }
}
