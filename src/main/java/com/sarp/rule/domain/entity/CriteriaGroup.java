package com.sarp.rule.domain.entity;

import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_ID_CANNOT_BE_NULL;
import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY;

import com.sarp.core.domain.base.AggregateRoot;
import com.sarp.rule.domain.exception.CriteriaGroupDomainException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.*;

@Getter
@ToString
@EqualsAndHashCode(of = "id")
@AggregateRoot
public class CriteriaGroup {

    private UUID id;

    private String name;

    private Integer displayOrder;

    @Getter(AccessLevel.NONE)
    private List<Criteria> criteriaList = new ArrayList<>();

    private void validate() {
        if (id == null) {
            throw new CriteriaGroupDomainException(CRITERIA_GROUP_ID_CANNOT_BE_NULL);
        }
        if (name == null || name.isBlank()) {
            throw new CriteriaGroupDomainException(CRITERIA_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY);
        }
        if (displayOrder == null) {
            throw new CriteriaGroupDomainException(CRITERIA_GROUP_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY);
        }
    }

    private void initialize() {
        if (id == null) {
            this.id = UUID.randomUUID();
        }
    }

    public CriteriaGroup updated(String newName) {
        return CriteriaGroup.builder()
                .id(this.id)
                .name(newName != null ? newName : this.name)
                .displayOrder(this.displayOrder)
                .build();
    }

    public List<Criteria> getCriteriaList() {
        return Collections.unmodifiableList(criteriaList);
    }

    public static CriteriaGroup.Builder builder() {
        return new CriteriaGroup.Builder();
    }

    public static class Builder {
        private CriteriaGroup criteriaGroup;

        public Builder() {
            criteriaGroup = new CriteriaGroup();
            criteriaGroup.initialize();
        }

        public Builder id(UUID id) {
            criteriaGroup.id = id;
            return this;
        }

        public Builder name(String name) {
            criteriaGroup.name = (name != null) ? name.trim() : null;
            return this;
        }

        public Builder displayOrder(Integer displayOrder) {
            criteriaGroup.displayOrder = displayOrder;
            return this;
        }

        public Builder criteriaList(List<Criteria> criteriaList) {
            criteriaGroup.criteriaList = criteriaList != null ? criteriaList : new ArrayList<>();
            return this;
        }

        public CriteriaGroup build() {
            CriteriaGroup builtCriteriaGroup = criteriaGroup;
            builtCriteriaGroup.validate();
            return builtCriteriaGroup;
        }
    }
}
