package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum CriteriaExceptionMessageCode implements ExceptionMessageCode {
    CRITERIA_NOT_FOUND("RUL-CRI-002", "criteria_not_found", "Criteria not found"),
    CRITERIA_NAME_CAN_NOT_BE_EMPTY("RUL-CRI-003", "criteria_name_cannot_be_empty", "Criteria name cannot be empty"),
    CRITERIA_CREATION_FAILED("RUL-CRI-004", "criteria_creation_failed", "Criteria creation failed"),
    CRITERIA_DESCRIPTION_CAN_NOT_BE_EMPTY(
            "RUL-CRI-005", "criteria_description_cannot_be_empty", "Criteria description cannot be empty"),
    CRITERIA_TYPE_CAN_NOT_BE_NULL("RUL-CRI-006", "criteria_type_cannot_be_null", "Criteria type cannot be null"),
    CRITERIA_REQUEST_TYPE_CAN_NOT_BE_NULL(
            "RUL-CRI-007", "criteria_request_type_cannot_be_null", "Criteria request type cannot be null"),
    CRITERIA_FIELD_TYPE_CAN_NOT_BE_NULL(
            "RUL-CRI-008", "criteria_field_type_cannot_be_null", "Criteria field type cannot be null"),
    CRITERIA_MAPPING_FIELD_CAN_NOT_BE_EMPTY(
            "RUL-CRI-009", "criteria_mapping_field_cannot_be_empty", "Criteria mapping field cannot be empty"),
    CRITERIA_VALUES_CAN_NOT_BE_EMPTY(
            "RUL-CRI-010", "criteria_values_cannot_be_empty", "Criteria values cannot be empty"),
    CRITERIA_MIN_VALUE_CAN_NOT_BE_NULL(
            "RUL-CRI-011", "criteria_min_value_cannot_be_null", "Criteria minimum value cannot be null"),
    CRITERIA_MAX_VALUE_CAN_NOT_BE_NULL(
            "RUL-CRI-012", "criteria_max_value_cannot_be_null", "Criteria maximum value cannot be null"),
    CRITERIA_CREATE_DATE_CAN_NOT_BE_NULL(
            "RUL-CRI-013", "criteria_create_date_cannot_be_null", "Criteria create date cannot be null"),
    CRITERIA_IS_LINKED_TO_RULE(
            "RUL-CRI-014", "criteria_is_linked_to_rule", "Criteria is linked to a rule and cannot be deleted"),
    CRITERIA_HAS_CONDITION_SET(
            "RUL-CRI-015", "criteria_has_condition_set", "Criteria has a condition set and cannot be deleted"),
    CRITERIA_SELECTION_TYPE_CAN_NOT_BE_NULL(
            "RUL-CRI-016", "criteria_selection_type_cannot_be_null", "Criteria selection type cannot be null"),
    CRITERIA_SYSTEM_DEFINED_TYPE_FIELD_CAN_NOT_BE_EMPTY(
            "RUL-CRI-009",
            "criteria_system_defined_type_field_cannot_be_empty",
            "System defined criteria type field cannot be empty"),
    CRITERIA_ALLOWED_OPERATORS_IS_NOT_VALID(
            "RUL-CRI-017", "criteria_allowed_operators_is_not_valid", "Criteria allowed operators is not valid");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    CriteriaExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
