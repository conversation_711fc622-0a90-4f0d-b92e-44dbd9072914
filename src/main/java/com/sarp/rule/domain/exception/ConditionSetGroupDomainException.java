package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class ConditionSetGroupDomainException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-COSG-001";

    public ConditionSetGroupDomainException(ExceptionMessageCode messageCode, Object... args) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), args);
    }

    public ConditionSetGroupDomainException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
