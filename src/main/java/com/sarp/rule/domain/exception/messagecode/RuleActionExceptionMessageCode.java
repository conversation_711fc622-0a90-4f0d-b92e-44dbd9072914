package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum RuleActionExceptionMessageCode implements ExceptionMessageCode {
    RULE_ACTION_PARAMETERS_CANNOT_BE_NULL(
            "RUL-ACT-003", "rule_action_parameters_cannot_be_null", "Rule action parameters cannot be null"),
    RULE_ACTION_ACTION_TYPE_CANNOT_BE_NULL(
            "RUL-ACT-004", "rule_action_action_type_cannot_be_null", "Rule action type cannot be null");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    RuleActionExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
