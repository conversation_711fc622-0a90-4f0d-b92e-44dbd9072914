package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class MarketNotFoundException extends BaseDomainException {

    public static final String EXCEPTION_TYPE_CODE = "RUL-MKT-002";

    public MarketNotFoundException(ExceptionMessageCode exceptionMessageCode, Object... args) {
        super(
                exceptionMessageCode.getMessageKey(),
                exceptionMessageCode.getErrorCode(),
                exceptionMessageCode.getDefaultMessage(),
                args);
    }

    public MarketNotFoundException(ExceptionMessageCode exceptionMessageCode) {
        super(
                exceptionMessageCode.getMessageKey(),
                exceptionMessageCode.getErrorCode(),
                exceptionMessageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
