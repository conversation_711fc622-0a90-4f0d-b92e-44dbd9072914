package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ProductExceptionMessage implements ExceptionMessageCode {
    PRODUCT_ID_CANNOT_BE_NULL("RUL-PRO-002", "product_id_cannot_be_null", "Product ID cannot be null."),
    PRODUCT_VARIANT_ID_CANNOT_BE_NULL(
            "RUL-PRO-003", "product_variant_id_cannot_be_null", "Product productId cannot be null."),
    PRODUCT_NOT_FOUND("RUL-PRO-004", "product_not_found", "Product not found."),

    PRODUCTS_NOT_FOUND_BY_VARIANT_IDS(
            "RUL-PRO-005",
            "products_not_found_by_variant_ids",
            "Products not found for the following variant IDs: {0}.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ProductExceptionMessage(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
