package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum BundleExceptionMessageCode implements ExceptionMessageCode {
    BUNDLE_ID_CANNOT_BE_NULL("RUL-BUN-002", "bundle_id_cannot_be_null", "Bundle ID cannot be null."),
    BUNDLE_NAME_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-BUN-003", "bundle_name_cannot_be_null_or_empty", "Bundle name must not be null or empty."),
    BUNDLE_DESCRIPTION_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-BUN-004",
            "bundle_description_cannot_be_null_or_empty",
            "Bundle description must not be null or empty."),
    BUNDLE_STATUS_CANNOT_BE_NULL("RUL-BUN-005", "bundle_status_cannot_be_null", "Bundle status cannot be null."),
    BUNDLE_EFFECTIVE_DATES_CANNOT_BE_NULL(
            "RUL-BUN-006", "bundle_effective_dates_cannot_be_null", "Bundle effective dates cannot be null."),
    BUNDLE_TYPE_CANNOT_BE_NULL("RUL-BUN-007", "bundle_type_cannot_be_null", "Bundle type cannot be null."),
    BUNDLE_AT_LEAST_ONE_PRODUCT_WITH_QUANTITY(
            "RUL-BUN-008",
            "bundle_at_least_one_product_with_quantity",
            "Bundle should include at least one product with quantity."),
    BUNDLE_LIST_CANNOT_BE_EMPTY("RUL-BUN-009", "bundle_list_cannot_be_empty", "The bundle list cannot be empty."),
    BUNDLE_VARIANT_ID_LIST_CANNOT_BE_EMPTY(
            "RUL-BUN-010", "bundle_variant_id_list_cannot_be_empty", "The bundle variant ID list cannot be empty."),
    BUNDLE_VARIANT_ID_NOT_UNIQUE(
            "RUL-BUN-011", "bundle_variant_must_be_unique", "The bundle product ID list must contain unique IDs."),
    BUNDLE_NOT_FOUND("RUL-BUN-012", "bundle_not_found", "Bundle not found."),
    BUNDLE_CANNOT_CONTAIN_NULL_PRODUCTS(
            "RUL-BUN-013", "bundle_cannot_contain_null_products", "Bundle cannot contain null products."),
    BUNDLES_NOT_FOUND("RUL-BUN-014", "bundles_not_found", "Bundles not found"),
    BUNDLE_PRODUCT_QUANTITY_EXCEEDS_LIMIT(
            "RUL-BUN-015",
            "bundle_product_quantity_exceeds_limit",
            "Bundle product quantity exceeds the allowed limit.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    BundleExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
