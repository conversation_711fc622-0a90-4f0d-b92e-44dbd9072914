package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ConditionSetGroupExceptionMessageCode implements ExceptionMessageCode {
    CONDITION_SET_GROUP_NOT_FOUND("RUL-CSG-002", "conditionSetGroup_not_found", "Condition Set Group not found"),
    CONDITION_SET_GROUP_PRODUCT_ID_CANNOT_BE_NULL(
            "RUL-CSG-003",
            "conditionSetGroup_product_id_cannot_be_null",
            "Condition Set Group product id cannot be null"),
    CONDITION_SET_GROUP_ID_CANNOT_BE_NULL(
            "RUL-CSG-004", "conditionSetGroup_id_cannot_be_null", "Condition Set Group ID cannot be null"),
    RULE_DEFINITION_ID_CANNOT_BE_NULL(
            "RUL-CSG-005",
            "conditionSetGroup_rule_definition_id_cannot_be_null",
            "Condition Set Group ruleDefinition Id cannot be null"),
    CONDITION_SET_GROUP_BUNDLE_ID_CANNOT_BE_NULL(
            "RUL-CSG-006", "conditionSetGroup_bundle_id_cannot_be_null", "Condition Set Group bundleId cannot be null"),
    CONDITION_SET_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CSG-007",
            "conditionSetGroup_name_cannot_be_null_or_empty",
            "Condition Set Group name cannot be null or empty"),
    CONDITION_SET_GROUP_DESCRIPTION_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CSG-008",
            "conditionSetGroup_description_cannot_be_null_or_empty",
            "Condition Set Group description cannot be null or empty"),
    CONDITION_SET_GROUP_CONDITION_SETS_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CSG-009",
            "conditionSetGroup_conditionSets_cannot_be_null_or_empty",
            "Condition Set Group conditionSets cannot be null or empty");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ConditionSetGroupExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
