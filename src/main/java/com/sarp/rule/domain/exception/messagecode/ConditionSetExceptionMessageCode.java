package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ConditionSetExceptionMessageCode implements ExceptionMessageCode {
    CONDITION_SET_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-COS-002",
            "condition_set_display_order_cannot_be_null_or_empty",
            "Condition set display order cannot be null or empty."),

    ACTIONS_IN_CONDITION_SET_MUST_NOT_HAVE_THE_SAME_ACTION_TYPE(
            "RUL-COS-003",
            "actions_in_condition_set_must_not_have_the_same_action_type",
            "Actions in condition set must not have the same action type.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ConditionSetExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
