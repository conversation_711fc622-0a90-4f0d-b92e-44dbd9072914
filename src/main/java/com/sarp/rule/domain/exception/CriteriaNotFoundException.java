package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class CriteriaNotFoundException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-CRI-002";

    public CriteriaNotFoundException(ExceptionMessageCode messageCode, Object object) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), object);
    }

    public CriteriaNotFoundException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
