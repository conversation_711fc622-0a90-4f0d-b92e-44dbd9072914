package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class CriteriaGroupNotFoundException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-CGR-002";

    public CriteriaGroupNotFoundException(ExceptionMessageCode messageCode, Object object) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), object);
    }

    public CriteriaGroupNotFoundException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
