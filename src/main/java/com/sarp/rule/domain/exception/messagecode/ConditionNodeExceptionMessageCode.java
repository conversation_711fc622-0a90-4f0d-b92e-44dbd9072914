package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ConditionNodeExceptionMessageCode implements ExceptionMessageCode {
    CONDITION_ID_CANNOT_BE_NULL("RUL-CON-003", "condition_id_cannot_be_null", "ConditionNode.id cannot be null");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ConditionNodeExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
