package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class RuleActionDomainException extends BaseDomainException {

    public static final String EXCEPTION_TYPE_CODE = "RUL-ACT-001";

    public RuleActionDomainException(ExceptionMessageCode messageCode, Object... args) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), args);
    }

    public RuleActionDomainException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
