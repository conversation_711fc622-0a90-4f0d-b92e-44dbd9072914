package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class ConditionNodeDomainException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-CON-002";

    public ConditionNodeDomainException(ExceptionMessageCode messageCode, Object... args) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), args);
    }

    public ConditionNodeDomainException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
