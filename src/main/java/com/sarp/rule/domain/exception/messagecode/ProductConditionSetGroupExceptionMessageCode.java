package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ProductConditionSetGroupExceptionMessageCode implements ExceptionMessageCode {
    PRODUCT_CONDITION_SET_GROUP_PRODUCT_ID_LIST_CANNOT_BE_EMPTY(
            "RUL-PRD-002",
            "product_condition_set_group_product_id_list_cannot_be_empty",
            "ProductConditionSetGroup.productIdList cannot be empty");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ProductConditionSetGroupExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
