package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class ConditionSetGroupNotFoundException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-CSG-002";

    public ConditionSetGroupNotFoundException(ExceptionMessageCode messageCode, Object object) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), object);
    }

    public ConditionSetGroupNotFoundException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
