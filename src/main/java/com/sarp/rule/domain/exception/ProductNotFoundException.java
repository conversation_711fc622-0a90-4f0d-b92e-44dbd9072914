package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class ProductNotFoundException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-PRO-004";

    public ProductNotFoundException(ExceptionMessageCode messageCode, Object object) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), object);
    }

    public ProductNotFoundException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
