package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum CriteriaConfigExceptionMessageCode implements ExceptionMessageCode {
    CRITERIA_CONFIG_ID_CANNOT_BE_NULL(
            "RUL-CRC-002", "criteria_config_id_cannot_be_null", "Criteria config ID cannot be null."),
    CRITERIA_CONFIG_CRITERIA_CANNOT_BE_NULL(
            "RUL-CRC-003", "criteria_config_criteria_cannot_be_null", "Criteria config criteria cannot be null."),
    CRITERIA_CONFIG_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CRC-004",
            "criteria_config_display_order_cannot_be_null_or_empty",
            "Criteria config display order cannot be null or empty."),
    CRITERIA_CONFIG_GROUP_ID_DOES_NOT_EXIST(
            "RUL-CRC-005", "criteria_config_group_id_does_not_exist", "Criteria config group ID does not exist."),
    CRITERIA_CONFIG_CRITERIA_ID_DOES_NOT_EXIST(
            "RUL-CRC-006", "criteria_config_criteria_id_does_not_exist", "Criteria config criteria ID does not exist.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    CriteriaConfigExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
