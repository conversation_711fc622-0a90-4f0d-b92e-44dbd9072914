package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ConditionExceptionMessageCode implements ExceptionMessageCode {
    CONDITION_LIST_CANNOT_BE_EMPTY("RUL-CON-004", "condition_list_cannot_be_empty", "Condition.list cannot be empty"),
    CONDITION_OPERATOR_CANNOT_BE_NULL(
            "RUL-CON-005", "condition_operator_cannot_be_null", "Condition.operator cannot be null"),
    CONDITION_VALUE_CANNOT_BE_NULL("RUL-CON-006", "condition_value_cannot_be_null", "Condition.value cannot be null"),
    CONDITION_CRITERIA_CANNOT_BE_NULL(
            "RUL-CON-007", "condition_criteria_cannot_be_null", "Condition.criteria cannot be null"),
    CONDITION_FAILED_TO_SAVE("RUL-CON-008", "condition_failed_to_save", "Condition failed to save"),
    CONDITION_VALUE_OUT_OF_RANGE("RUL-CON-009", "condition_value_out_of_range", "Condition value is out of range"),
    CONDITION_OPERATOR_NOT_ALLOWED(
            "RUL-CON-011", "condition_operator_not_allowed", "Condition operator is not allowed"),
    CONDITION_VALUE_NOT_VALID("RUL-CON-010", "condition_value_not_valid", "Condition value is is not valid");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ConditionExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
