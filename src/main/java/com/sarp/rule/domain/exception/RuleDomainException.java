package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class RuleDomainException extends BaseDomainException {

    public static final String EXCEPTION_TYPE_CODE = "RUL-RUL-001";

    public RuleDomainException(ExceptionMessageCode exceptionMessageCode, Object... args) {
        super(
                exceptionMessageCode.getMessageKey(),
                exceptionMessageCode.getErrorCode(),
                exceptionMessageCode.getDefaultMessage(),
                args);
    }

    public RuleDomainException(ExceptionMessageCode exceptionMessageCode) {
        super(
                exceptionMessageCode.getMessageKey(),
                exceptionMessageCode.getErrorCode(),
                exceptionMessageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
