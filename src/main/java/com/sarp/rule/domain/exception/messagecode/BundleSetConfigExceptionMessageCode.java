package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum BundleSetConfigExceptionMessageCode implements ExceptionMessageCode {
    BUNDLE_SET_CONFIG_RULE_TYPE_CAN_NOT_BE_NULL(
            "RUL-BSC-002", "bundle_set_config_rule_type_can_not_be_null", "Rule type cannot be null."),
    BUNDLE_SET_CONFIG_EFFECTIVE_DATES_CANNOT_BE_NULL(
            "RUL-BSC-003", "bundle_set_config_effective_dates_cannot_be_null", "Effective dates cannot be null."),
    BUNDLE_SET_CONFIG_NAME_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-BSC-004",
            "bundle_set_config_name_cannot_be_null_or_empty",
            "Bundle set config name cannot be null or empty.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    BundleSetConfigExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
