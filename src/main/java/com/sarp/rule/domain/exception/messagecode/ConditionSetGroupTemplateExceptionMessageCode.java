package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ConditionSetGroupTemplateExceptionMessageCode implements ExceptionMessageCode {
    CONDITION_SET_GROUP_TEMPLATE_NAME_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CSGT-003",
            "condition_set_group_template_name_cannot_be_null_or_empty",
            "Condition Set Group Template name cannot be null or empty"),
    CONDITION_SET_GROUP_TEMPLATE_AUTHOR_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CSGT-004",
            "condition_set_group_template_author_cannot_be_null_or_empty",
            "Condition Set Group Template author cannot be null or empty"),

    CONDITION_SET_GROUP_TEMPLATE_MUST_HAVE_AT_LEAST_ONE_CONDITION_SET_GROUP_ID(
            "RUL-CSGT-005",
            "condition_set_group_template_must_have_at_least_one_condition_set_group_id",
            "Condition Set Group Template must have at least one Condition Set Group ID");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ConditionSetGroupTemplateExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
