package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum PortExceptionMessageCode implements ExceptionMessageCode {
    PORT_NOT_FOUND("RUL-PRT-003", "port_not_found", "Port not found."),
    PORT_CODE_CANNOT_BE_EMPTY("RUL-PRT-004", "port_code_cannot_be_empty", "Port code cannot be empty."),
    PORT_CODE_LIST_CANNOT_BE_EMPTY("RUL-PRT-005", "port_code_list_cannot_be_empty", "Port code list cannot be empty."),
    PORTS_NOT_FOUND_BY_PORT_CODES(
            "RUL-PRT-006", "ports_not_found_by_port_codes", "Ports not found for the following port codes: {0}."),
    PORTS_INACTIVE_BY_PORT_CODES(
            "RUL-PRT-007", "ports_inactive_by_port_codes", "Ports inactive for the following port codes: {0}."),
    INVALID_PORT_CODE_LENGTH("RUL-PRT-008", "invalid_port_code_length", "Invalid port code length: {0}."),
    PORT_CODE_MUST_CONTAIN_ONLY_LETTERS(
            "RUL-PRT-009", "port_code_must_contain_only_letters", "Port code must contain only letters."),
    ;
    ;

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    PortExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
