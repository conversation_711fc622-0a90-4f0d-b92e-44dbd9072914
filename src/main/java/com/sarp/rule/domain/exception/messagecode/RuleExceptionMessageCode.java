package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum RuleExceptionMessageCode implements ExceptionMessageCode {
    RULE_ACTION_CAN_NOT_BE_NULL("RUL-RUL-002", "rule_action_can_not_be_null", "Rule action cannot be null."),
    RULE_DISPLAY_ORDER_CAN_NOT_BE_NULL(
            "RUL-RUL-003", "rule_display_order_can_not_be_null", "Rule display order cannot be null."),
    RULE_CONDITION_NODE_LIST_CANNOT_BE_EMPTY(
            "RUL-RUL-004", "rule_condition_node_list_cannot_be_empty", "Rule condition node list cannot be empty."),
    RULE_NOT_FOUND("RUL-RUL-005", "rule_not_found", "Rule not found."),
    RULE_TYPE_INCOMPATIBLE_WITH_ACTION_TYPE(
            "RUL-RUL-006", "rule_type_incompatible_with_action_type", "Rule type incompatible with action type."),

    RULE_VALIDATION_FAILED(
            "rule_validation_failed", "RUL-009", "Rule validation failed. Please see the following issues: {0}"),
    RULE_VARIANT_ID_LIST_CANNOT_BE_EMPTY(
            "RUL-RUL-007", "rule_variant_id_list_cannot_be_empty", "Rule variant ID list cannot be empty."),

    RULE_FIELD_CANNOT_BE_NULL("RUL-RUL-010", "rule_field_cannot_be_null", "{0} cannot be null."),
    RULE_BUNDLE_TARGET_REQUIRED(
            "RUL-RUL-011", "rule_bundle_target_required", "Bundle rules must target at least one bundle."),
    RULE_BUNDLE_CANNOT_CONTAIN_PRODUCTS(
            "RUL-RUL-012", "rule_bundle_cannot_contain_products", "Bundle rules cannot contain products."),
    RULE_PRODUCT_TARGET_REQUIRED(
            "RUL-RUL-013", "rule_product_target_required", "Product rules must target at least one product."),
    RULE_PRODUCT_CANNOT_CONTAIN_BUNDLES(
            "RUL-RUL-014", "rule_product_cannot_contain_bundles", "Product rules cannot contain bundles."),
    RULE_CONDITION_SET_GROUP_CANNOT_BE_NULL(
            "RUL-RUL-015", "rule_condition_set_group_cannot_be_null", "Rule condition set group cannot be null."),
    RULE_TYPE_VALIDATOR_NOT_FOUND(
            "RUL-RUL-016", "rule_type_validator_not_found", "No validator found for rule type: {0}"),
    PRODUCT_MAPPING_STRATEGY_NOT_FOUND(
            "RUL-RUL-017",
            "product_mapping_strategy_not_found",
            "No product mapping strategy found for rule type: {0}"),
    EFFECTIVE_DATES_CANNOT_BE_NULL("RUL-RUL-018", "effective_dates_cannot_be_null", "Effective dates cannot be null."),
    INVALID_RULE_STATUS_FOR_ACTIVATION(
            "RUL-RUL-019",
            "invalid_rule_status_for_activation",
            "Rule status must be 'Expired' or 'Passive' to activate."),
    RULE_BUNDLE_LIMIT_EXCEEDED(
            "RUL-RUL-020",
            "rule_bundle_limit_exceeded",
            "Rule bundle limit exceeded: A rule can contain a maximum of {0} bundles.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    RuleExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
