package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum CriteriaGroupExceptionMessage implements ExceptionMessageCode {
    CRITERIA_GROUP_NOT_FOUND("RUL-CGR-002", "criteria_group_not_found", "Criteria group not found"),
    CRITERIA_GROUP_ID_CANNOT_BE_NULL(
            "RUL-CGR-003", "criteria_group_id_cannot_be_null", "Criteria group ID cannot be null"),
    CRITERIA_GROUP_NAME_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CGR-004",
            "criteria_group_name_cannot_be_null_or_empty",
            "Criteria group name cannot be null or empty"),
    CRITERIA_GROUP_DISPLAY_ORDER_CANNOT_BE_NULL_OR_EMPTY(
            "RUL-CGR-005",
            "criteria_group_display_order_cannot_be_null_or_empty",
            "Criteria group display number cannot be null or empty"),
    CRITERIA_GROUP_HAS_ASSOCIATED_CRITERIA(
            "RUL-CGR-006",
            "criteria_group_has_associated_criteria",
            "Criteria group cannot be deleted as it has associated criteria");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    CriteriaGroupExceptionMessage(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
