package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum MarketExceptionMessageCode implements ExceptionMessageCode {
    MARKET_NOT_FOUND("RUL-MKT-003", "market_not_found", "Market not found.");

    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    MarketExceptionMessageCode(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
