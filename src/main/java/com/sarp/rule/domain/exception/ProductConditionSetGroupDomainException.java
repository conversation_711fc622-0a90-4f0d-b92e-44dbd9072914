package com.sarp.rule.domain.exception;

import com.sarp.core.domain.exception.BaseDomainException;
import com.sarp.core.domain.exception.ExceptionMessageCode;

public class ProductConditionSetGroupDomainException extends BaseDomainException {
    public static final String EXCEPTION_TYPE_CODE = "RUL-PRD-001";

    public ProductConditionSetGroupDomainException(ExceptionMessageCode messageCode, Object... args) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage(), args);
    }

    public ProductConditionSetGroupDomainException(ExceptionMessageCode messageCode) {
        super(messageCode.getMessageKey(), messageCode.getErrorCode(), messageCode.getDefaultMessage());
    }

    @Override
    public String getExceptionTypeCode() {
        return EXCEPTION_TYPE_CODE;
    }
}
