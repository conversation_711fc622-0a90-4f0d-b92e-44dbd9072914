package com.sarp.rule.domain.exception.messagecode;

import com.sarp.core.domain.exception.ExceptionMessageCode;

public enum ProductWithQuantityExceptionMessage implements ExceptionMessageCode {
    PRODUCT_CANNOT_BE_NULL("RUL-PWQ-002", "product_cannot_be_null", "Product cannot be null."),
    VARIANT_ID_CANNOT_BE_NULL("RUL-PWQ-003", "variant_id_cannot_be_null", "Variant ID cannot be null."),
    ;
    private final String errorCode;
    private final String messageKey;
    private final String defaultMessage;

    ProductWithQuantityExceptionMessage(String errorCode, String messageKey, String defaultMessage) {
        this.errorCode = errorCode;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }
}
