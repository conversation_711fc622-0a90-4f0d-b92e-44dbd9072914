package com.sarp.rule.config;

import jakarta.persistence.EntityManagerFactory;
import java.util.Optional;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(basePackages = "com.sarp.rule.adapter.persistence.repository")
@EnableJpaAuditing
@EntityScan(basePackages = "com.sarp.rule.adapter.persistence.model")
public class JpaConfig {

    @Primary
    @Bean(name = "transactionManager")
    public PlatformTransactionManager jpaTransactionManager(EntityManagerFactory emf) {
        return new JpaTransactionManager(emf);
    }

    @Bean
    public AuditorAware<String> auditorProvider() {
        // TODO: Implement a way to determine the current auditor.
        return () -> Optional.of("system"); // Example: "system" as the default auditor
    }
}
