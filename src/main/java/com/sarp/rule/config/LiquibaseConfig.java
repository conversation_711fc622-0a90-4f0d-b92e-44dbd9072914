package com.sarp.rule.config;

import com.sarp.rule.config.properties.LiquibaseProperties;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import javax.sql.DataSource;
import liquibase.integration.spring.SpringLiquibase;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
@EnableConfigurationProperties(LiquibaseProperties.class)
public class LiquibaseConfig {

    private final LiquibaseProperties liquibaseProperties;

    public LiquibaseConfig(LiquibaseProperties liquibaseProperties) {
        this.liquibaseProperties = liquibaseProperties;
    }

    @Bean("liquibaseCommons")
    @ConditionalOnProperty(name = "liquibase.commons-properties.enabled", havingValue = "true", matchIfMissing = false)
    public SpringLiquibase liquibaseCommons(DataSource dataSource) {
        LiquibaseProperties.Commons commons = liquibaseProperties.getCommonsProperties();

        createSchemaIfNotExists(dataSource, commons.getDefaultSchema(), commons.getSchemaLimit());

        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setDataSource(dataSource);
        liquibase.setChangeLog(commons.getChangeLog());
        liquibase.setDefaultSchema(commons.getDefaultSchema());
        liquibase.setContexts(commons.getContexts());
        liquibase.setShouldRun(commons.isShouldRun());

        return liquibase;
    }

    @Bean
    @ConditionalOnProperty(name = "liquibase.public-properties.enabled", havingValue = "true", matchIfMissing = false)
    @DependsOn("liquibaseCommons")
    public SpringLiquibase liquibasePublic(DataSource dataSource) {
        LiquibaseProperties.Public publicConfig = liquibaseProperties.getPublicProperties();

        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setDataSource(dataSource);
        liquibase.setChangeLog(publicConfig.getChangeLog());
        liquibase.setDefaultSchema(publicConfig.getDefaultSchema());
        liquibase.setContexts(publicConfig.getContexts());
        liquibase.setShouldRun(publicConfig.isShouldRun());

        return liquibase;
    }

    private void createSchemaIfNotExists(DataSource dataSource, String schemaName, int schemaLimit) {
        try (Connection connection = dataSource.getConnection();
                Statement statement = connection.createStatement()) {

            if (!isValidSchemaName(schemaName, schemaLimit)) {
                throw new IllegalArgumentException("Invalid schema name: " + schemaName);
            }

            String sql = String.format("CREATE SCHEMA IF NOT EXISTS \"%s\"", schemaName.replace("\"", "\"\""));
            statement.execute(sql);

        } catch (SQLException e) {
            throw new RuntimeException("Failed to create schema '" + schemaName + "'", e);
        }
    }

    private boolean isValidSchemaName(String schemaName, int schemaLimit) {
        return schemaName != null
                && schemaName.matches("^[a-zA-Z][a-zA-Z0-9_]*$")
                && schemaName.length() <= schemaLimit;
    }
}
