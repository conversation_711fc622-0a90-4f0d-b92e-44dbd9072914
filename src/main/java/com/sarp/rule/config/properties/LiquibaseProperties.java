package com.sarp.rule.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "liquibase")
@Data
@Component
public class LiquibaseProperties {

    private Commons commonsProperties = new Commons();
    private Public publicProperties = new Public();

    @Data
    public static class Commons {
        private String changeLog;
        private String defaultSchema;
        private String contexts;
        private boolean shouldRun = true;
        private boolean enabled = false;
        private int schemaLimit = 63;
    }

    @Data
    public static class Public {
        private String changeLog;
        private String defaultSchema = "public";
        private String contexts = "public";
        private boolean shouldRun = true;
        private boolean enabled = false;
    }
}
