package com.sarp.rule.application.validator;

import static com.sarp.rule.domain.exception.messagecode.ProductExceptionMessage.PRODUCT_NOT_FOUND;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.exception.ProductNotFoundException;
import com.sarp.rule.domain.repository.ProductRepository;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ProductValidator {

    private final ProductRepository productRepository;

    public List<UUID> validateAllProductsExist(List<UUID> variantIds) {
        List<Product> products = productRepository.findAllByVariantIdIn(variantIds);
        if (products.size() == variantIds.size()) {
            return products.stream().map(Product::getId).toList();
        }

        Set<UUID> foundProductIds = products.stream().map(Product::getProductId).collect(Collectors.toSet());

        List<UUID> missingProductIds = variantIds.stream()
                .filter(productId -> !foundProductIds.contains(productId))
                .toList();

        throw new ProductNotFoundException(PRODUCT_NOT_FOUND, missingProductIds);
    }
}
