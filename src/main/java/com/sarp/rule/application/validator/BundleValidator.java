package com.sarp.rule.application.validator;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_NOT_FOUND;

import com.sarp.core.domain.base.DomainService;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.repository.BundleRepository;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@DomainService
@RequiredArgsConstructor
public class BundleValidator {

    private final BundleRepository bundleRepository;

    public void validateAllBundlesExist(List<UUID> bundleIds) {
        List<Bundle> bundles = bundleRepository.findAllById(bundleIds);

        if (bundles.size() == bundleIds.size()) {
            return;
        }

        Set<UUID> foundBundleIds = bundles.stream().map(Bundle::getId).collect(Collectors.toSet());

        List<UUID> missingBundleIds = bundleIds.stream()
                .filter(bundleId -> !foundBundleIds.contains(bundleId))
                .toList();

        throw new BundleNotFoundException(BUNDLE_NOT_FOUND, missingBundleIds);
    }
}
