package com.sarp.rule.application.validator;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_VARIANT_ID_LIST_CANNOT_BE_EMPTY;
import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_VARIANT_ID_NOT_UNIQUE;
import static com.sarp.rule.domain.exception.messagecode.ProductExceptionMessage.PRODUCT_NOT_FOUND;

import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.exception.BundleDomainException;
import com.sarp.rule.domain.exception.ProductNotFoundException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class BundleProductValidator {

    /** Validates that the bundle contains at least one variant ID */
    public void ensureBundleHasVariants(List<UUID> variantIds, String bundleName) {
        if (CollectionUtils.isEmpty(variantIds)) {
            throw new BundleDomainException(BUNDLE_VARIANT_ID_LIST_CANNOT_BE_EMPTY, bundleName);
        }
    }

    /**
     * Validates that all variant IDs in the bundle are unique (no duplicates) Assumes the list is
     * already validated to be non-empty
     */
    public void validateUniqueVariantIds(List<UUID> variantIds, String bundleName) {
        long distinctCount =
                variantIds.stream().filter(Objects::nonNull).distinct().count();

        if (distinctCount != variantIds.size()) {
            throw new BundleDomainException(BUNDLE_VARIANT_ID_NOT_UNIQUE, bundleName);
        }
    }

    /** Comprehensive validation of variant IDs Ensures bundle has variants and all are unique */
    public void validateBundleVariantIds(List<UUID> variantIds, String bundleName) {
        ensureBundleHasVariants(variantIds, bundleName);
        validateUniqueVariantIds(variantIds, bundleName);
    }

    public void validateAllProductsFound(List<Product> foundProducts, List<UUID> requestedIds) {
        if (foundProducts.size() == requestedIds.size()) {
            return;
        }

        Set<UUID> foundProductIds =
                foundProducts.stream().map(Product::getProductId).collect(Collectors.toSet());

        List<UUID> missingIds = requestedIds.stream()
                .filter(id -> !foundProductIds.contains(id))
                .toList();

        if (!missingIds.isEmpty()) {
            throw new ProductNotFoundException(PRODUCT_NOT_FOUND, missingIds);
        }
    }
}
