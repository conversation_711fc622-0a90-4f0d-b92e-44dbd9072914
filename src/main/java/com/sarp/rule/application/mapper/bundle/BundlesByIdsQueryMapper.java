package com.sarp.rule.application.mapper.bundle;

import com.sarp.generated.openapi.api.dto.BundlesByIdsRequestDTO;
import com.sarp.generated.openapi.api.dto.BundlesByIdsResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.query.bundle.BundlesByIdsQuery;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = BundleMapper.class)
public abstract class BundlesByIdsQueryMapper {

    @Autowired
    protected BundleMapper bundleMapper;

    public abstract BundlesByIdsQuery toQuery(BundlesByIdsRequestDTO requestDTO);

    public BundlesByIdsResponseDTO toResponse(List<Bundle> bundles) {
        BundlesByIdsResponseDTO response = new BundlesByIdsResponseDTO();

        if (bundles == null || bundles.isEmpty()) {
            response.setBundles(Collections.emptyList());
            return response;
        }

        response.setBundles(
                bundles.stream().map(bundleMapper::bundleToBundleResponseDTO).toList());
        return response;
    }
}
