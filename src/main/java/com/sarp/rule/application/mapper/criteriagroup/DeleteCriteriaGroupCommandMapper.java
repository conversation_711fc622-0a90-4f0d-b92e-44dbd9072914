package com.sarp.rule.application.mapper.criteriagroup;

import com.sarp.rule.domain.command.criteriagroup.DeleteCriteriaGroupCommand;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DeleteCriteriaGroupCommandMapper {
    default DeleteCriteriaGroupCommand toCommand(UUID criteriaGroupId) {
        return new DeleteCriteriaGroupCommand(criteriaGroupId);
    }
}
