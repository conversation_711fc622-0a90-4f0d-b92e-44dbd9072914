package com.sarp.rule.application.mapper.market;

import com.sarp.generated.openapi.api.dto.UpdateMarketRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.PortMapper;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import com.sarp.rule.domain.entity.Port;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {PortMapper.class})
public interface UpdateMarketCommandMapper {
    @Mapping(target = "id", source = "id")
    @Mapping(target = "market.name", source = "requestDTO.name")
    @Mapping(target = "market.ports", source = "requestDTO.portCodes", qualifiedByName = "portCodesToPorts")
    UpdateMarketCommand toCommand(UUID id, UpdateMarketRequestDTO requestDTO);

    @Named("portCodesToPorts")
    default Set<Port> portCodesToPorts(Set<String> portCodes) {
        if (portCodes == null || portCodes.isEmpty()) {
            return Set.of();
        }

        return portCodes.stream()
                .map(portCode -> Port.builder().portCode(portCode).build())
                .collect(Collectors.toSet());
    }
}
