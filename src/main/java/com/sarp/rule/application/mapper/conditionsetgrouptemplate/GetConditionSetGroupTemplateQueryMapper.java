package com.sarp.rule.application.mapper.conditionsetgrouptemplate;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupTemplateResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupTemplateMapper;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.query.conditionsetgrouptemplate.ConditionSetGroupTemplateQuery;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = ConditionSetGroupTemplateMapper.class)
public interface GetConditionSetGroupTemplateQueryMapper {

    ConditionSetGroupTemplateQuery toQuery(UUID conditionSetGroupTemplateId);

    ConditionSetGroupTemplateResponseDTO toResponse(ConditionSetGroupTemplate conditionSetGroupTemplate);

    default Page<ConditionSetGroupTemplateResponseDTO> toResponsePage(
            PaginatedResult<ConditionSetGroupTemplate> paginatedResult) {
        if (paginatedResult == null) {
            return Page.empty();
        }

        List<ConditionSetGroupTemplateResponseDTO> dtoList =
                paginatedResult.getItems().stream().map(this::toResponse).toList();

        return new PageImpl<>(
                dtoList,
                PageRequest.of(paginatedResult.getCurrentPage(), paginatedResult.getPageSize()),
                paginatedResult.getTotalElements());
    }
}
