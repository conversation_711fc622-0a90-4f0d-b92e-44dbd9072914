package com.sarp.rule.application.mapper.bundle;

import com.sarp.generated.openapi.api.dto.BundleStatusDTO;
import com.sarp.rule.domain.query.bundle.GetBundlesQuery;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface GetBundlesQueryMapper {

    default GetBundlesQuery toQuery(Integer page, Integer size, BundleStatusDTO statusDto) {
        BundleStatus domainStatus = null;
        if (statusDto != null) {
            try {
                domainStatus = BundleStatus.valueOf(statusDto.name());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid status value: " + statusDto.name());
            }
        }
        return GetBundlesQuery.of(page, size, domainStatus);
    }
}
