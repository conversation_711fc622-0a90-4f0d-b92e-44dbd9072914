package com.sarp.rule.application.mapper.conditionsetgroup;

import com.sarp.rule.domain.command.conditionsetgroup.DeleteConditionSetGroupCommand;
import java.util.UUID;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface DeleteConditionSetGroupCommandMapper {
    default DeleteConditionSetGroupCommand toCommand(UUID conditionSetGroupId) {
        return new DeleteConditionSetGroupCommand(conditionSetGroupId);
    }
}
