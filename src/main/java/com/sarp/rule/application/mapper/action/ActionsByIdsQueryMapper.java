package com.sarp.rule.application.mapper.action;

import com.sarp.generated.openapi.api.dto.ActionResponseDTO;
import com.sarp.generated.openapi.api.dto.GetActionsByIdsRequestDTO;
import com.sarp.generated.openapi.api.dto.GetActionsByIdsResponseDTO;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.query.action.ActionsByIdsQuery;
import com.sarp.rule.domain.valueobject.action.Parameters;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ActionsByIdsQueryMapper {
    ActionsByIdsQuery toQuery(GetActionsByIdsRequestDTO requestDTO);

    default GetActionsByIdsResponseDTO toResponse(List<Action> actions) {
        GetActionsByIdsResponseDTO response = new GetActionsByIdsResponseDTO();

        if (actions == null || actions.isEmpty()) {
            response.setActions(Collections.emptyList());
            return response;
        }

        response.setActions(actions.stream().map(this::toActionResponseDTO).toList());
        return response;
    }

    ActionResponseDTO toActionResponseDTO(Action action);

    default Map<String, Object> map(Parameters parameters) {
        if (parameters == null || parameters.parameters() == null) {
            return Map.of();
        }
        return parameters.parameters();
    }
}
