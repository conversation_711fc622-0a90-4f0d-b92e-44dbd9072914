package com.sarp.rule.application.mapper.criteriagroup;

import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupWithDetailsResponseDTO;
import com.sarp.generated.openapi.api.dto.RuleTypeDTO;
import com.sarp.rule.adapter.persistence.mapper.CriteriaGroupMapper;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsQuery;
import com.sarp.rule.domain.query.criteriagroup.CriteriaGroupWithDetailQuery;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CriteriaGroupMapper.class})
public interface CriteriaGroupsQueryMapper {

    default CriteriaGroupsQuery toQuery(Integer page, Integer size) {
        return new CriteriaGroupsQuery(PagingParams.of(page, size));
    }

    default CriteriaGroupWithDetailQuery toCriteriaGroupWithDetailQuery(
            Integer page, Integer size, RuleTypeDTO allowedRule) {
        return CriteriaGroupWithDetailQuery.of(RuleType.valueOf(allowedRule.getValue()), page, size);
    }

    // MapStruct will handle the mapping automatically
    List<CriteriaGroupWithCriteriaResponseDTO> toDtoList(List<CriteriaGroupApplicationDTO> applicationDTOs);

    /** Convert PaginatedResultApplicationDTO to Spring Page */
    default Page<CriteriaGroupWithCriteriaResponseDTO> toResponsePage(
            PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO> applicationResult) {

        if (applicationResult == null || applicationResult.getItems() == null) {
            return Page.empty();
        }

        List<CriteriaGroupWithCriteriaResponseDTO> dtoList = toDtoList(applicationResult.getItems());

        PageRequest pageRequest = PageRequest.of(applicationResult.getCurrentPage(), applicationResult.getPageSize());

        return new PageImpl<>(dtoList, pageRequest, applicationResult.getTotalElements());
    }

    /** Convert PaginatedResultApplicationDTO to Spring Page of response DTOs */
    default Page<CriteriaGroupWithDetailsResponseDTO> toDetailsResponsePage(
            PaginatedResultApplicationDTO<CriteriaGroupWithDetailsApplicationDTO> applicationResult) {

        if (applicationResult == null || applicationResult.getItems() == null) {
            return Page.empty();
        }

        List<CriteriaGroupWithDetailsResponseDTO> dtoList = applicationResult.getItems().stream()
                .map(this::toDetailsResponseDTO)
                .toList();

        PageRequest pageRequest = PageRequest.of(applicationResult.getCurrentPage(), applicationResult.getPageSize());

        return new PageImpl<>(dtoList, pageRequest, applicationResult.getTotalElements());
    }

    /** Convert a single CriteriaGroupWithDetailsApplicationDTO to response DTO */
    CriteriaGroupWithDetailsResponseDTO toDetailsResponseDTO(CriteriaGroupWithDetailsApplicationDTO detailsDTO);
}
