package com.sarp.rule.application.mapper.conditionsetgroup;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateConditionSetGroupRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetMapper;
import com.sarp.rule.domain.command.conditionsetgroup.UpdateConditionSetGroupCommand;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupUpdatedEvent;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
        componentModel = "spring",
        uses = {ConditionSetGroupMapper.class, ConditionSetMapper.class})
public interface UpdateConditionSetGroupCommandMapper {

    @Mapping(target = "conditionSetGroupId", source = "conditionSetGroupIdPath")
    @Mapping(target = "conditionSetGroup.name", source = "requestDTO.name")
    @Mapping(target = "conditionSetGroup.description", source = "requestDTO.description")
    @Mapping(target = "conditionSetGroup.conditionSets", source = "requestDTO.conditionSets")
    UpdateConditionSetGroupCommand toCommand(
            UUID conditionSetGroupIdPath, UpdateConditionSetGroupRequestDTO requestDTO);

    @Mapping(target = "id", source = "conditionSetGroup.id")
    @Mapping(target = "name", source = "conditionSetGroup.name")
    @Mapping(target = "description", source = "conditionSetGroup.description")
    @Mapping(target = "conditionSets", source = "conditionSetGroup.conditionSets")
    ConditionSetGroupResponseDTO toResponse(ConditionSetGroupUpdatedEvent event);
}
