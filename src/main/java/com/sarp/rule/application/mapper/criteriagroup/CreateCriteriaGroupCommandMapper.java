package com.sarp.rule.application.mapper.criteriagroup;

import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupRequestDTO;
import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupResponseDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CreateCriteriaGroupCommand;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CreateCriteriaGroupCommandMapper {

    default CreateCriteriaGroupCommand toCommand(CreateCriteriaGroupRequestDTO requestDTO) {
        if (requestDTO == null) {
            return null;
        }

        return new CreateCriteriaGroupCommand(requestDTO.getName());
    }

    CreateCriteriaGroupResponseDTO toResponse(CriteriaGroupApplicationDTO criteriaGroupApplicationDTO);
}
