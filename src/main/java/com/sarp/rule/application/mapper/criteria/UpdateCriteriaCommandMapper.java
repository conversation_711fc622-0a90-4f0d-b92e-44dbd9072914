package com.sarp.rule.application.mapper.criteria;

import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.CriteriaMapper;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.command.criteria.UpdateCriteriaCommand;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = CriteriaMapper.class)
public interface UpdateCriteriaCommandMapper {

    UpdateCriteriaCommand toCommand(UUID id, UpdateCriteriaRequestDTO updateCriteriaRequestDTO);

    CriteriaResponseDTO toResponse(CriteriaApplicationDTO applicationDTO);

    default Instant map(LocalDateTime value) {
        return value != null ? value.atZone(ZoneId.systemDefault()).toInstant() : null;
    }

    default LocalDateTime map(Instant value) {
        return value != null ? LocalDateTime.ofInstant(value, ZoneId.systemDefault()) : null;
    }
}
