package com.sarp.rule.application.mapper.conditionsetgrouptemplate;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupTemplateResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateConditionSetGroupTemplateRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupTemplateMapper;
import com.sarp.rule.domain.command.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommand;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateCreatedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = ConditionSetGroupTemplateMapper.class)
public interface CreateConditionSetGroupTemplateCommandMapper {

    @Mapping(target = "conditionSetGroupTemplate.name", source = "requestDTO.name")
    @Mapping(target = "conditionSetGroupTemplate.author", source = "requestDTO.author")
    @Mapping(target = "conditionSetGroupTemplate.conditionSetGroupIds", source = "requestDTO.conditionSetGroupIds")
    CreateConditionSetGroupTemplateCommand toCommand(CreateConditionSetGroupTemplateRequestDTO requestDTO);

    @Mapping(target = "id", source = "event.conditionSetGroupTemplate.id")
    @Mapping(target = "name", source = "event.conditionSetGroupTemplate.name")
    @Mapping(target = "author", source = "event.conditionSetGroupTemplate.author")
    @Mapping(target = "conditionSetGroupIds", source = "event.conditionSetGroupTemplate.conditionSetGroupIds")
    ConditionSetGroupTemplateResponseDTO toResponse(ConditionSetGroupTemplateCreatedEvent event);
}
