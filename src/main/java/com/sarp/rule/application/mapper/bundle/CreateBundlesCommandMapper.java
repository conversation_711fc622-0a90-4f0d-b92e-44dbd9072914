package com.sarp.rule.application.mapper.bundle;

import com.sarp.generated.openapi.api.dto.BundleDTO;
import com.sarp.generated.openapi.api.dto.CreateBundleResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateBundlesRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.domain.command.bundle.CreateBundleCommand;
import com.sarp.rule.domain.command.bundle.CreateBundlesCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.event.bundle.BundlesCreatedEvent;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = BundleMapper.class)
public interface CreateBundlesCommandMapper {

    default CreateBundlesCommand toCommand(CreateBundlesRequestDTO requestDTO) {
        if (requestDTO == null || requestDTO.getBundles() == null) {
            return new CreateBundlesCommand(Collections.emptyList());
        }
        List<CreateBundleCommand> bundleCommands = requestDTO.getBundles().stream()
                .map(this::bundleDTOToCreateBundleCommand)
                .toList();
        return new CreateBundlesCommand(bundleCommands);
    }

    default CreateBundleCommand bundleDTOToCreateBundleCommand(BundleDTO bundleDTO) {
        if (bundleDTO == null) {
            return null;
        }

        BundleMapper bundleMapper = Mappers.getMapper(BundleMapper.class);
        Bundle bundle = bundleMapper.bundleDTOToBundle(bundleDTO);

        return new CreateBundleCommand(bundle);
    }

    CreateBundleResponseDTO toResponse(BundlesCreatedEvent bundlesCreatedEvent);
}
