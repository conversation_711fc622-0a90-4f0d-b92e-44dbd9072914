package com.sarp.rule.application.mapper.rule;

import com.sarp.generated.openapi.api.dto.ActivateRuleRequestDTO;
import com.sarp.rule.domain.command.rule.ActivateRuleCommand;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ActivateRuleCommandMapper {

    @Mapping(target = "effectiveDates", source = "request", qualifiedByName = "mapEffectiveDates")
    ActivateRuleCommand toCommand(UUID id, ActivateRuleRequestDTO request);

    @Named("mapEffectiveDates")
    default EffectiveDates mapEffectiveDates(ActivateRuleRequestDTO request) {
        if (request == null || request.getEffectiveDateFrom() == null || request.getEffectiveDateTo() == null) {
            return null;
        }
        return new EffectiveDates(request.getEffectiveDateFrom(), request.getEffectiveDateTo());
    }
}
