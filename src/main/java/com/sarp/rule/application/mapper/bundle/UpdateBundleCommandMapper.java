package com.sarp.rule.application.mapper.bundle;

import com.sarp.generated.openapi.api.dto.BundleResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateBundleRequestDTO;
import com.sarp.generated.openapi.api.dto.VariantDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.domain.command.bundle.UpdateBundleCommand;
import com.sarp.rule.domain.event.bundle.BundleUpdatedEvent;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {BundleMapper.class})
public interface UpdateBundleCommandMapper {
    @Mapping(target = "bundleId", source = "bundleIdPath")
    @Mapping(
            target = "productWithQuantities",
            source = "dto.variants",
            qualifiedByName = "variantsToProductWithQuantitiesList")
    UpdateBundleCommand toCommand(UUID bundleIdPath, UpdateBundleRequestDTO dto);

    default BundleResponseDTO toResponse(BundleUpdatedEvent event) {
        return Mappers.getMapper(BundleMapper.class).bundleToBundleResponseDTO(event.getBundle());
    }

    @Named("variantsToProductWithQuantitiesList")
    default List<ProductWithQuantity> variantsToProductWithQuantitiesList(List<VariantDTO> variants) {
        Set<ProductWithQuantity> set = Mappers.getMapper(BundleMapper.class).variantsToProductWithQuantities(variants);
        return new ArrayList<>(set);
    }
}
