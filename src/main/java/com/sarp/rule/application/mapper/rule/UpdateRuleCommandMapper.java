package com.sarp.rule.application.mapper.rule;

import com.sarp.generated.openapi.api.dto.UpdateRuleRequestDTO;
import com.sarp.generated.openapi.api.dto.UpdateRuleResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.adapter.persistence.mapper.ProductMapper;
import com.sarp.rule.domain.command.rule.UpdateRuleCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.event.rule.RuleUpdatedEvent;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ConditionSetGroupMapper.class, BundleMapper.class, ProductMapper.class})
public interface UpdateRuleCommandMapper {

    @Mapping(target = "ruleType", source = "request.type")
    @Mapping(target = "effectiveDates", source = "request", qualifiedByName = "mapEffectiveDates")
    UpdateRuleCommand toCommand(UUID id, UpdateRuleRequestDTO request);

    @Named("mapEffectiveDates")
    default EffectiveDates mapEffectiveDates(UpdateRuleRequestDTO request) {
        if (request == null) {
            return null;
        }
        return new EffectiveDates(request.getEffectiveDateFrom(), request.getEffectiveDateTo());
    }

    @Mapping(target = "ruleType", source = "event.rule.ruleType")
    @Mapping(target = "conditionSetGroups", source = "event.rule.conditionSetGroups")
    @Mapping(target = "bundleIds", source = "event.rule.bundles", qualifiedByName = "mapBundleIds")
    @Mapping(target = "variantIds", source = "event.rule.products", qualifiedByName = "mapProductIds")
    @Mapping(target = "createdAt", source = "event.rule.createdAt")
    @Mapping(target = "updatedAt", source = "event.rule.updatedAt")
    @Mapping(target = "createdBy", source = "event.rule.createdBy")
    @Mapping(target = "updatedBy", source = "event.rule.updatedBy")
    UpdateRuleResponseDTO toResponseDTO(RuleUpdatedEvent event);

    @Named("mapBundleIds")
    default List<UUID> mapBundleIds(Set<Bundle> bundles) {
        if (bundles == null) {
            return Collections.emptyList();
        }
        return bundles.stream().map(Bundle::getId).toList();
    }

    @Named("mapProductIds")
    default List<UUID> mapProductIds(Set<Product> products) {
        if (products == null) {
            return Collections.emptyList();
        }
        return products.stream().map(Product::getProductId).toList();
    }
}
