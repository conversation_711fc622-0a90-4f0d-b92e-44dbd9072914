package com.sarp.rule.application.mapper.criteria;

import com.sarp.generated.openapi.api.dto.CreateCriteriaRequestDTO;
import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.command.criteria.CreateCriteriaCommand;
import com.sarp.rule.domain.entity.Criteria;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CreateCriteriaCommandMapper {

    @Mapping(target = "criteria", source = "createCriteriaRequestDTO")
    CreateCriteriaCommand toCommand(CreateCriteriaRequestDTO createCriteriaRequestDTO);

    // Add this mapping method to handle DTO to Domain conversion
    @Mapping(target = "minValue", expression = "java(mapMinValue(dto.getMinValue()))")
    @Mapping(target = "maxValue", expression = "java(mapMaxValue(dto.getMaxValue()))")
    Criteria toCriteria(CreateCriteriaRequestDTO dto);

    CriteriaResponseDTO toResponse(CriteriaApplicationDTO applicationDTO);

    // Helper methods for type conversion
    default BigDecimal mapMinValue(Double value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    default BigDecimal mapMaxValue(Double value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    default Instant map(LocalDateTime value) {
        return value != null ? value.atZone(ZoneId.systemDefault()).toInstant() : null;
    }

    default LocalDateTime map(Instant value) {
        return value != null ? LocalDateTime.ofInstant(value, ZoneId.systemDefault()) : null;
    }
}
