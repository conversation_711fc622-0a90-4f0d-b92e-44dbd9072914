package com.sarp.rule.application.mapper.conditionsetgrouptemplate;

import com.sarp.rule.domain.command.conditionsetgrouptemplate.DeleteConditionSetGroupTemplateCommand;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DeleteConditionSetGroupTemplateCommandMapper {

    default DeleteConditionSetGroupTemplateCommand toCommand(UUID conditionSetGroupTemplateId) {
        return new DeleteConditionSetGroupTemplateCommand(conditionSetGroupTemplateId);
    }
}
