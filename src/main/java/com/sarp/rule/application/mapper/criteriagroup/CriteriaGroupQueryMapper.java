package com.sarp.rule.application.mapper.criteriagroup;

import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.CriteriaGroupMapper;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupByIdQuery;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CriteriaGroupMapper.class})
public interface CriteriaGroupQueryMapper {

    CriteriaGroupByIdQuery toQuery(UUID criteriaGroupId);

    CriteriaGroupWithCriteriaResponseDTO toResponse(CriteriaGroupApplicationDTO criteriaGroupApplicationDTO);
}
