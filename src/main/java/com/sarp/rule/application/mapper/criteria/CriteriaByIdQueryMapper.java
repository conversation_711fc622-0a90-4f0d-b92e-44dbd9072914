package com.sarp.rule.application.mapper.criteria;

import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.CriteriaMapper;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.command.criteria.CriteriaByIdQuery;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CriteriaMapper.class})
public interface CriteriaByIdQueryMapper {
    CriteriaByIdQuery toQuery(UUID criteriaId);

    CriteriaResponseDTO toResponse(CriteriaApplicationDTO applicationDTO);

    default Instant map(LocalDateTime localDateTime) {
        return localDateTime == null
                ? null
                : localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }

    default LocalDateTime map(Instant instant) {
        return instant == null ? null : LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
