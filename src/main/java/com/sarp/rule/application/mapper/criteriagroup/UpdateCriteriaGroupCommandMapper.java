package com.sarp.rule.application.mapper.criteriagroup;

import com.sarp.generated.openapi.api.dto.CriteriaGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaGroupRequestDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.UpdateCriteriaGroupCommand;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UpdateCriteriaGroupCommandMapper {
    @Mapping(target = "criteriaGroupId", source = "id")
    @Mapping(target = "name", source = "dto.name")
    UpdateCriteriaGroupCommand toCommand(UUID id, UpdateCriteriaGroupRequestDTO dto);

    CriteriaGroupResponseDTO toResponse(CriteriaGroupApplicationDTO criteriaGroupApplicationDTO);
}
