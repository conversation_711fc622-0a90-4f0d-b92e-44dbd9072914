package com.sarp.rule.application.mapper.conditionsetgroup;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateConditionSetGroupRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetMapper;
import com.sarp.rule.domain.command.conditionsetgroup.CreateConditionSetGroupCommand;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupCreatedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
        componentModel = "spring",
        uses = {ConditionSetGroupMapper.class, ConditionSetMapper.class})
public interface CreateConditionSetGroupCommandMapper {

    @Mapping(target = "conditionSetGroup.name", source = "requestDTO.name")
    @Mapping(target = "conditionSetGroup.description", source = "requestDTO.description")
    @Mapping(target = "conditionSetGroup.conditionSets", source = "requestDTO.conditionSets")
    CreateConditionSetGroupCommand toCommand(CreateConditionSetGroupRequestDTO requestDTO);

    @Mapping(target = "name", source = "conditionSetGroup.name")
    @Mapping(target = "description", source = "conditionSetGroup.description")
    @Mapping(target = "conditionSets", source = "conditionSetGroup.conditionSets")
    @Mapping(target = "id", source = "conditionSetGroup.id")
    ConditionSetGroupResponseDTO toResponse(ConditionSetGroupCreatedEvent event);
}
