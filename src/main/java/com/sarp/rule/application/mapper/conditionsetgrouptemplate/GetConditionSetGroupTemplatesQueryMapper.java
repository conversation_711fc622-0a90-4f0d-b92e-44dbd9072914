package com.sarp.rule.application.mapper.conditionsetgrouptemplate;

import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupTemplateMapper;
import com.sarp.rule.domain.query.conditionsetgrouptemplate.ConditionSetGroupTemplatesQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = ConditionSetGroupTemplateMapper.class)
public interface GetConditionSetGroupTemplatesQueryMapper {

    default ConditionSetGroupTemplatesQuery toQuery(Integer page, Integer size) {
        return ConditionSetGroupTemplatesQuery.of(page, size);
    }
}
