package com.sarp.rule.application.mapper.bundle;

import com.sarp.generated.openapi.api.dto.BundleResponseDTO;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.query.bundle.GetBundleQuery;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

@Mapper(componentModel = "spring")
public interface GetBundleQueryMapper {
    GetBundleQuery toQuery(UUID bundleId);

    @Mapping(source = "productWithQuantities", target = "productsWithQuantities")
    BundleResponseDTO toResponse(Bundle bundle);

    default Page<BundleResponseDTO> toResponsePage(PaginatedResult<Bundle> paginatedResult) {
        if (paginatedResult == null) {
            return Page.empty();
        }
        List<BundleResponseDTO> dtoList =
                paginatedResult.getItems().stream().map(this::toResponse).toList();
        return new PageImpl<>(
                dtoList,
                PageRequest.of(paginatedResult.getCurrentPage(), paginatedResult.getPageSize()),
                paginatedResult.getTotalElements());
    }
}
