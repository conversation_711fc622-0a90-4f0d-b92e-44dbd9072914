package com.sarp.rule.application.mapper.rule;

import com.sarp.generated.openapi.api.dto.CreateRuleRequestDTO;
import com.sarp.generated.openapi.api.dto.CreateRuleResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupTemplateMapper;
import com.sarp.rule.adapter.persistence.mapper.ProductMapper;
import com.sarp.rule.domain.command.rule.CreateRuleCommand;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleCreatedEvent;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {
            ConditionSetGroupTemplateMapper.class,
            ConditionSetGroupMapper.class,
            BundleMapper.class,
            ProductMapper.class
        })
public interface CreateRuleCommandMapper {

    @Mapping(target = "rule", source = "request")
    @Mapping(target = "conditionSetGroupTemplate", source = "request.conditionSetGroupTemplate")
    CreateRuleCommand toCommand(CreateRuleRequestDTO request);

    @Mapping(target = "products", source = "variantIds", qualifiedByName = "variantIdsToProducts")
    @Mapping(target = "bundles", source = "bundles")
    @Mapping(target = "conditionSetGroups", source = "conditionSetGroups")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "ruleType", source = "type")
    @Mapping(target = "priority", source = "priority")
    @Mapping(target = "saveAsDraft", source = "saveAsDraft")
    @Mapping(target = "effectiveDates", source = "request", qualifiedByName = "mapEffectiveDates")
    Rule toRule(CreateRuleRequestDTO request);

    /**
     * Maps effective date fields from request to EffectiveDates value object. This approach
     * provides better testability and debugging compared to expressions.
     */
    @Named("mapEffectiveDates")
    default EffectiveDates mapEffectiveDates(CreateRuleRequestDTO request) {
        if (request == null) {
            return null;
        }
        return new EffectiveDates(request.getEffectiveDateFrom(), request.getEffectiveDateTo());
    }

    @Mapping(target = "id", source = "rule.id")
    @Mapping(target = "name", source = "rule.name")
    @Mapping(target = "effectiveDateFrom", source = "rule.effectiveDates.from")
    @Mapping(target = "effectiveDateTo", source = "rule.effectiveDates.to")
    @Mapping(target = "priority", source = "rule.priority")
    @Mapping(target = "type", source = "rule.ruleType")
    @Mapping(target = "status", source = "rule.ruleStatus")
    @Mapping(target = "conditionSetGroups", source = "rule.conditionSetGroups")
    @Mapping(target = "bundles", source = "rule.bundles")
    @Mapping(target = "products", source = "rule.products")
    @Mapping(target = "createdAt", source = "rule.createdAt")
    @Mapping(target = "updatedAt", source = "rule.updatedAt")
    @Mapping(target = "createdBy", source = "rule.createdBy")
    @Mapping(target = "updatedBy", source = "rule.updatedBy")
    CreateRuleResponseDTO toResponse(RuleCreatedEvent ruleCreatedEvent);

    @Named("variantIdsToProducts")
    default Set<Product> variantIdsToProducts(List<UUID> variantIds) {
        if (variantIds == null) {
            return java.util.Collections.emptySet();
        }
        return variantIds.stream()
                .map(variantId -> Product.builder().variantId(variantId).build())
                .collect(Collectors.toSet());
    }
}
