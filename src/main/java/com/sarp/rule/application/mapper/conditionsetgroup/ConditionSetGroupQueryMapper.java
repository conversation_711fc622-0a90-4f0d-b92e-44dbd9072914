package com.sarp.rule.application.mapper.conditionsetgroup;

import com.sarp.generated.openapi.api.dto.ConditionSetGroupResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.query.conditionSetGroup.ConditionSetGroupQuery;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

@Mapper(
        componentModel = "spring",
        uses = {ConditionSetGroupMapper.class})
public interface ConditionSetGroupQueryMapper {

    ConditionSetGroupQuery toQuery(UUID conditionSetGroupId);

    ConditionSetGroupResponseDTO toResponse(ConditionSetGroup conditionSetGroup);

    default Page<ConditionSetGroupResponseDTO> toResponsePage(PaginatedResult<ConditionSetGroup> paginatedResult) {
        if (paginatedResult == null) {
            return Page.empty();
        }
        List<ConditionSetGroupResponseDTO> dtoList =
                paginatedResult.getItems().stream().map(this::toResponse).toList();
        return new PageImpl<>(
                dtoList,
                PageRequest.of(paginatedResult.getCurrentPage(), paginatedResult.getPageSize()),
                paginatedResult.getTotalElements());
    }
}
