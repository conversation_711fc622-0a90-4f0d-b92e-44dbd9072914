package com.sarp.rule.application.mapper.conditionsetgroup;

import com.sarp.rule.adapter.persistence.mapper.ConditionSetGroupMapper;
import com.sarp.rule.domain.query.conditionSetGroup.ConditionSetGroupsQuery;
import org.mapstruct.Mapper;

@Mapper(
        componentModel = "spring",
        uses = {ConditionSetGroupMapper.class})
public interface ConditionSetGroupsQueryMapper {

    default ConditionSetGroupsQuery toQuery(Integer page, Integer size) {
        return ConditionSetGroupsQuery.of(page, size);
    }
}
