package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.conditionsetgroup.ConditionNodeApplicationDTO;
import com.sarp.rule.domain.entity.ConditionNode;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ConditionApplicationDTOMapper.class})
public interface ConditionNodeApplicationDTOMapper {

    /** Converts ConditionNode domain object to ConditionNodeApplicationDTO */
    ConditionNodeApplicationDTO toDTO(ConditionNode conditionNode);

    /** Converts ConditionNodeApplicationDTO to ConditionNode domain object */
    ConditionNode toDomain(ConditionNodeApplicationDTO dto);
}
