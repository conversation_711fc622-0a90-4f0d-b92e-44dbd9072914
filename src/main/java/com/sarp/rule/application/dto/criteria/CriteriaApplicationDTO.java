package com.sarp.rule.application.dto.criteria;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class CriteriaApplicationDTO {
    UUID id;
    String name;
    String description;
    String type;
    String requestType;
    String mappingField;
    String fieldType;
    String selectionType;
    List<String> allowedValues;
    BigDecimal minValue;
    BigDecimal maxValue;
    LocalDateTime startDateTime;
    LocalDateTime endDateTime;
    LocalTime startTime;
    LocalTime endTime;
    UUID criteriaGroupId;
    Instant createdAt;
    String createdBy;
    Instant updatedAt;
    String updatedBy;
    List<ComparisonOperator> allowedOperators;
}
