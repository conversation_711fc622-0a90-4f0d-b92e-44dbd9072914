package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.condition.ConditionApplicationDTO;
import com.sarp.rule.domain.entity.Condition;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ConditionApplicationDTOMapper {

    /** Converts Condition domain object to ConditionApplicationDTO */
    ConditionApplicationDTO toDTO(Condition condition);

    /** Converts ConditionApplicationDTO to Condition domain object */
    Condition toDomain(ConditionApplicationDTO dto);
}
