package com.sarp.rule.application.dto.mapper.criteriagroup;

import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.entity.CriteriaGroup;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CriteriaApplicationDTOMapper.class})
public interface CriteriaGroupApplicationDTOMapper {

    /**
     * Converts CriteriaGroup domain object to CriteriaGroupApplicationDTO MapStruct automatically
     * maps the criteriaList using CriteriaApplicationMapper
     */
    CriteriaGroupApplicationDTO toDTO(CriteriaGroup criteriaGroup);

    /**
     * Converts CriteriaGroupApplicationDTO to CriteriaGroup domain object MapStruct automatically
     * maps the criteriaList using CriteriaApplicationMapper
     */
    CriteriaGroup toDomain(CriteriaGroupApplicationDTO dto);
}
