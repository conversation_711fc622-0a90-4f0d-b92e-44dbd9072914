package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.conditionsetgroup.ConditionSetGroupApplicationDTO;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ConditionSetApplicationDTOMapper.class})
public interface ConditionSetGroupApplicationDTOMapper {

    /** Converts ConditionSetGroup domain object to ConditionSetGroupApplicationDTO */
    ConditionSetGroupApplicationDTO toDTO(ConditionSetGroup conditionSetGroup);

    /** Converts ConditionSetGroupApplicationDTO to ConditionSetGroup domain object */
    ConditionSetGroup toDomain(ConditionSetGroupApplicationDTO dto);
}
