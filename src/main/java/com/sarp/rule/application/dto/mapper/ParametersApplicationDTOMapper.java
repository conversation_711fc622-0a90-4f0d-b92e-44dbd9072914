package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.common.ParametersApplicationDTO;
import com.sarp.rule.domain.valueobject.action.Parameters;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParametersApplicationDTOMapper {

    /** Converts Parameters domain value object to ParametersApplicationDTO */
    ParametersApplicationDTO toDTO(Parameters parameters);

    /** Converts ParametersApplicationDTO to Parameters domain value object */
    @Mapping(target = ".", source = "parameters")
    Parameters toDomain(ParametersApplicationDTO dto);
}
