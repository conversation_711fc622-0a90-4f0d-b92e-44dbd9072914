package com.sarp.rule.application.dto.bundle;

import com.sarp.generated.openapi.api.dto.BundleStatusDTO;
import com.sarp.generated.openapi.api.dto.BundleTypeDTO;
import com.sarp.rule.application.dto.product.ProductWithQuantityApplicationDTO;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class BundleApplicationDTO {
    UUID id;
    String name;
    String description;
    BundleTypeDTO type;
    BundleStatusDTO status;
    Set<ProductWithQuantityApplicationDTO> productWithQuantities;
    byte[] image;
    Instant createdAt;
    String createdBy;
    Instant updatedAt;
    String updatedBy;
}
