package com.sarp.rule.application.dto.rule;

import com.sarp.rule.application.dto.bundle.BundleApplicationDTO;
import com.sarp.rule.application.dto.conditionsetgroup.ConditionSetGroupApplicationDTO;
import com.sarp.rule.application.dto.product.ProductApplicationDTO;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class RuleApplicationDTO {
    UUID id;
    String name;
    Instant effectiveDateFrom;
    Instant effectiveDateTo;
    Short priority;
    RuleTypeApplicationDTO ruleType;
    RuleStatusApplicationDTO ruleStatus;
    List<ConditionSetGroupApplicationDTO> conditionSetGroups;
    List<BundleApplicationDTO> bundles;
    List<ProductApplicationDTO> products;
    Instant createdAt;
    String createdBy;
    Instant updatedAt;
    String updatedBy;
}
