package com.sarp.rule.application.dto.mapper.criteria;

import com.sarp.rule.application.dto.criteria.CriteriaWithConfigDetailsApplicationDTO;
import com.sarp.rule.domain.valueobject.criteria.CriteriaWithConfigDetails;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CriteriaWithConfigDetailsApplicationDTOMapper {

    /**
     * Converts CriteriaWithConfigDetails domain object to CriteriaWithConfigDetailsApplicationDTO
     * MapStruct automatically handles enum conversions since both use domain enums
     */
    CriteriaWithConfigDetailsApplicationDTO toDTO(CriteriaWithConfigDetails criteriaWithConfigDetails);

    /**
     * Converts CriteriaWithConfigDetailsApplicationDTO to CriteriaWithConfigDetails domain object
     * MapStruct automatically handles enum conversions since both use domain enums
     */
    CriteriaWithConfigDetails toDomain(CriteriaWithConfigDetailsApplicationDTO dto);
}
