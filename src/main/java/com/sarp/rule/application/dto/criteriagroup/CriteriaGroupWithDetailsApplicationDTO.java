package com.sarp.rule.application.dto.criteriagroup;

import com.sarp.rule.application.dto.criteria.CriteriaWithConfigDetailsApplicationDTO;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class CriteriaGroupWithDetailsApplicationDTO {
    UUID id;
    String name;
    Integer displayOrder;
    List<CriteriaWithConfigDetailsApplicationDTO> criteriaDetails;
}
