package com.sarp.rule.application.dto.mapper.criteriagroup;

import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaWithConfigDetailsApplicationDTOMapper;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {CriteriaWithConfigDetailsApplicationDTOMapper.class})
public interface CriteriaGroupWithDetailsApplicationMapper {

    /**
     * Converts CriteriaGroupWithDetails domain object to CriteriaGroupWithDetailsApplicationDTO
     * MapStruct automatically maps the criteriaDetails using
     * CriteriaWithConfigDetailsApplicationMapper
     */
    CriteriaGroupWithDetailsApplicationDTO toDTO(CriteriaGroupWithDetails criteriaGroupWithDetails);

    /**
     * Converts CriteriaGroupWithDetailsApplicationDTO to CriteriaGroupWithDetails domain object
     * MapStruct automatically maps the criteriaDetails using
     * CriteriaWithConfigDetailsApplicationMapper
     */
    CriteriaGroupWithDetails toDomain(CriteriaGroupWithDetailsApplicationDTO dto);
}
