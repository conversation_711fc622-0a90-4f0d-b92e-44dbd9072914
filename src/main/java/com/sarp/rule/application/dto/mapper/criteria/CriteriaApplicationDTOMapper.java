package com.sarp.rule.application.dto.mapper.criteria;

import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.entity.Criteria;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CriteriaApplicationDTOMapper {

    /**
     * Converts Criteria domain object to CriteriaApplicationDTO String fields like type,
     * requestType will be converted from enum.name() if needed
     */
    CriteriaApplicationDTO toDTO(Criteria criteria);

    /**
     * Converts CriteriaApplicationDTO to Criteria domain object String fields will be converted to
     * enums using valueOf() if needed
     */
    Criteria toDomain(CriteriaApplicationDTO dto);
}
