package com.sarp.rule.application.dto.criteria;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import com.sarp.rule.domain.valueobject.criteria.SelectionType;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class CriteriaWithConfigDetailsApplicationDTO {
    UUID criteriaId;
    String criteriaName;
    String description;
    CriteriaType type;
    RequestType requestType;
    String mappingField;
    FieldType fieldType;
    SelectionType selectionType;
    List<String> allowedValues;
    BigInteger minValue;
    BigInteger maxValue;
    List<ComparisonOperator> allowedOperators;
    LocalDateTime startDateTime;
    LocalDateTime endDateTime;
    LocalTime startTime;
    LocalTime endTime;
    Integer displayOrder;
    List<String> allowedRules;
    List<String> mandatoryRules;
}
