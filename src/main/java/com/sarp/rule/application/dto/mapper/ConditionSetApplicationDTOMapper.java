package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.conditionsetgroup.ConditionSetApplicationDTO;
import com.sarp.rule.domain.entity.ConditionSet;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ConditionNodeApplicationDTOMapper.class, ActionApplicationDTOMapper.class})
public interface ConditionSetApplicationDTOMapper {

    /** Converts ConditionSet domain object to ConditionSetApplicationDTO */
    ConditionSetApplicationDTO toDTO(ConditionSet conditionSet);

    /** Converts ConditionSetApplicationDTO to ConditionSet domain object */
    ConditionSet toDomain(ConditionSetApplicationDTO dto);
}
