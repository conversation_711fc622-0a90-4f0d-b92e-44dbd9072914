package com.sarp.rule.application.dto.conditionsetgroup;

import com.sarp.rule.application.dto.action.ActionApplicationDTO;
import java.util.Set;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ConditionSetApplicationDTO {
    UUID id;
    String displayOrder;
    Set<ConditionNodeApplicationDTO> conditionNodes;
    Set<ActionApplicationDTO> actions;
}
