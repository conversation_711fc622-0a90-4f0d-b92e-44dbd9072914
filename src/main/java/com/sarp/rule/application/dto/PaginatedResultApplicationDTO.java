package com.sarp.rule.application.dto;

import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.function.Function;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public class PaginatedResultApplicationDTO<T> {
    private final List<T> items;
    private final int currentPage;
    private final int pageSize;
    private final long totalElements;
    private final int totalPages;

    /** Static factory method to create from domain PaginatedResult */
    public static <D, A> PaginatedResultApplicationDTO<A> of(PaginatedResult<D> domainResult, Function<D, A> mapper) {

        List<A> applicationDTOs = domainResult.getItems().stream().map(mapper).toList();

        return new PaginatedResultApplicationDTO<>(
                applicationDTOs,
                domainResult.getCurrentPage(),
                domainResult.getPageSize(),
                domainResult.getTotalElements(),
                domainResult.getTotalPages());
    }

    /** Transform items to another type */
    public <R> PaginatedResultApplicationDTO<R> map(Function<T, R> mapper) {
        List<R> mappedItems = items.stream().map(mapper).toList();

        return new PaginatedResultApplicationDTO<>(mappedItems, currentPage, pageSize, totalElements, totalPages);
    }
}
