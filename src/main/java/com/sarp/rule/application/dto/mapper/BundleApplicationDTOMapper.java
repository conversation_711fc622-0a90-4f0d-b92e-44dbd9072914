package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.bundle.BundleApplicationDTO;
import com.sarp.rule.domain.entity.Bundle;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ProductWithQuantityApplicationMapper.class})
public interface BundleApplicationDTOMapper {

    /**
     * Converts Bundle domain object to BundleApplicationDTO MapStruct will automatically handle
     * enum conversions if enum values are the same
     */
    BundleApplicationDTO toDTO(Bundle bundle);

    /**
     * Converts BundleApplicationDTO to Bundle domain object MapStruct will automatically handle
     * enum conversions if enum values are the same
     */
    Bundle toDomain(BundleApplicationDTO dto);

    // Note: If enum values differ between BundleTypeDTO/BundleStatusDTO (OpenAPI generated)
    // and domain enums, then custom mapping methods would be needed here.
    // If they're the same, MapStruct handles them automatically.
}
