package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.product.ProductApplicationDTO;
import com.sarp.rule.domain.entity.Product;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ProductApplicationDTOMapper {
    /** Converts Product domain object to ProductApplicationDTO */
    ProductApplicationDTO toDTO(Product product);

    /** Converts ProductApplicationDTO to Product domain object */
    Product toDomain(ProductApplicationDTO dto);
}
