package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.product.ProductWithQuantityApplicationDTO;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ProductApplicationDTOMapper.class})
public interface ProductWithQuantityApplicationMapper {

    /**
     * Converts ProductWithQuantity domain object to ProductWithQuantityApplicationDTO
     */
    ProductWithQuantityApplicationDTO toDTO(ProductWithQuantity productWithQuantity);

    /**
     * Converts ProductWithQuantityApplicationDTO to ProductWithQuantity domain object
     */
    ProductWithQuantity toDomain(ProductWithQuantityApplicationDTO dto);
}
