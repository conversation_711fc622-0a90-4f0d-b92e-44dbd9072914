package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.rule.RuleApplicationDTO;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.valueobject.common.EffectiveDates;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {
            ConditionSetGroupApplicationDTOMapper.class,
            BundleApplicationDTOMapper.class,
            ProductApplicationDTOMapper.class
        })
public interface RuleApplicationDTOMapper {

    /**
     * Converts a Rule domain object to RuleApplicationDTO
     *
     * @param rule the domain object to convert
     * @return the DTO representation
     */
    @Mapping(target = "effectiveDateFrom", source = "effectiveDates.from")
    @Mapping(target = "effectiveDateTo", source = "effectiveDates.to")
    RuleApplicationDTO toDTO(Rule rule);

    /**
     * Converts a RuleApplicationDTO to Rule domain object
     *
     * @param dto the DTO to convert
     * @return the domain object representation
     */
    @Mapping(target = "effectiveDates", source = "dto", qualifiedByName = "mapEffectiveDates")
    Rule toDomain(RuleApplicationDTO dto);

    @Named("mapEffectiveDates")
    default EffectiveDates mapEffectiveDates(RuleApplicationDTO dto) {
        if (dto == null) {
            return null;
        }
        return new EffectiveDates(dto.getEffectiveDateFrom(), dto.getEffectiveDateTo());
    }
}
