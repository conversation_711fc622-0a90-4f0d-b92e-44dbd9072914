package com.sarp.rule.application.dto.conditionsetgroup;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ConditionSetGroupApplicationDTO {
    UUID id;
    String name;
    String description;
    List<ConditionSetApplicationDTO> conditionSets;
    Instant createdAt;
    String createdBy;
    Instant updatedAt;
    String updatedBy;
}
