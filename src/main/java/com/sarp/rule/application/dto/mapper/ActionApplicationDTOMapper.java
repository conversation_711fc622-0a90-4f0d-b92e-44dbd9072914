package com.sarp.rule.application.dto.mapper;

import com.sarp.rule.application.dto.action.ActionApplicationDTO;
import com.sarp.rule.domain.entity.Action;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {ParametersApplicationDTOMapper.class})
public interface ActionApplicationDTOMapper {

    /** Converts Action domain object to ActionApplicationDTO */
    ActionApplicationDTO toDTO(Action action);

    /** Converts ActionApplicationDTO to Action domain object */
    Action toDomain(ActionApplicationDTO dto);
}
