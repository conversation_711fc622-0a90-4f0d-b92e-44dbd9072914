package com.sarp.rule.application.event.messaging;

import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NonNull;
import lombok.Singular;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RuleUpdatePayload {
    @NonNull
    UUID id;

    @NonNull
    RuleType ruleType;

    @NonNull
    @Singular
    List<ConditionSet> conditionSets;

    @NonNull
    @Singular
    List<UUID> oldConditionSets;
}
