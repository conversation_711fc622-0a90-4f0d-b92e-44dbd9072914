package com.sarp.rule.application.event.messaging.market;

import com.sarp.commons.kafka.domain.event.CloudEvent;
import java.time.OffsetDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class MarketEvent<T> extends CloudEvent<T> {
    public static MarketEvent<MarketCreateEventPayload> createCreatedEvent(
            String subject, MarketCreateEventPayload payload) {
        return MarketEvent.<MarketCreateEventPayload>builder()
                .specVersion("1.0")
                .type("com.sarp.market.create")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("create")
                .data(payload)
                .build();
    }

    public static MarketEvent<MarketUpdateEventPayload> createUpdateEvent(
            String subject, MarketUpdateEventPayload payload) {
        return MarketEvent.<MarketUpdateEventPayload>builder()
                .specVersion("1.0")
                .type("com.sarp.market.update")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("update")
                .data(payload)
                .build();
    }

    public static MarketEvent<MarketDeleteEventPayload> createDeleteEvent(
            String subject, MarketDeleteEventPayload payload) {
        return MarketEvent.<MarketDeleteEventPayload>builder()
                .specVersion("1.0")
                .type("com.sarp.market.delete")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("delete")
                .data(payload)
                .build();
    }
}
