package com.sarp.rule.application.event.messaging;

import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NonNull;
import lombok.Singular;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RuleDeletePayload {
    @NonNull
    UUID id;

    @Singular
    @NonNull
    List<UUID> oldConditionSets;
}
