package com.sarp.rule.application.event.messaging;

import com.sarp.commons.kafka.domain.event.CloudEvent;
import java.time.OffsetDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class RuleEvent<T> extends CloudEvent<T> {

    public static RuleEvent<RuleCreatePayload> createCreateEvent(String subject, RuleCreatePayload payload) {
        return RuleEvent.<RuleCreatePayload>builder()
                .specVersion("1.0")
                .type("com.sarp.rule.create")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("create")
                .data(payload)
                .build();
    }

    public static RuleEvent<RuleUpdatePayload> createUpdateEvent(String subject, RuleUpdatePayload payload) {
        return RuleEvent.<RuleUpdatePayload>builder()
                .specVersion("1.0")
                .type("com.sarp.rule.update")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("update")
                .data(payload)
                .build();
    }

    public static RuleEvent<RuleDeletePayload> createDeleteEvent(String subject, RuleDeletePayload payload) {
        return RuleEvent.<RuleDeletePayload>builder()
                .specVersion("1.0")
                .type("com.sarp.rule.delete")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(subject)
                .action("delete")
                .data(payload)
                .build();
    }
}
