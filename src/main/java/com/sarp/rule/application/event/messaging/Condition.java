package com.sarp.rule.application.event.messaging;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.adapter.persistence.model.enums.CriteriaType;
import com.sarp.rule.adapter.persistence.model.enums.FieldType;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class Condition {
    FieldType type;

    @NonNull
    List<Object> value;

    @NonNull
    ComparisonOperator operator;

    String criteriaMappingField;
    String systemDefinedCriteriaType;

    @NonNull
    CriteriaType criteriaType;
}
