package com.sarp.rule.application.event.messaging;

import java.util.UUID;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ConditionSet {
    @NonNull
    UUID id;

    @NonNull
    Integer priority;

    @NonNull
    When when;

    @NonNull
    Then then;
}
