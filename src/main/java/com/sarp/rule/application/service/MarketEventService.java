package com.sarp.rule.application.service;

import com.sarp.commons.kafka.adapter.producer.CloudEventKafkaProducer;
import com.sarp.rule.application.event.messaging.market.MarketCreateEventPayload;
import com.sarp.rule.application.event.messaging.market.MarketDeleteEventPayload;
import com.sarp.rule.application.event.messaging.market.MarketEvent;
import com.sarp.rule.application.event.messaging.market.MarketUpdateEventPayload;
import com.sarp.rule.config.MarketEventConfig;
import com.sarp.rule.domain.event.market.MarketCreatedEvent;
import com.sarp.rule.domain.event.market.MarketDeletedEvent;
import com.sarp.rule.domain.event.market.MarketUpdatedEvent;
import java.time.OffsetDateTime;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Service
@RequiredArgsConstructor
public class MarketEventService {
    private final CloudEventKafkaProducer kafkaProducer;
    private final MarketEventConfig config;

    public void sendMarketCreatedEventAfterCommit(MarketCreatedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendMarketCreatedEvent(event);
                }
            });
        } else {
            sendMarketCreatedEvent(event);
        }
    }

    private void sendMarketCreatedEvent(MarketCreatedEvent event) {
        MarketCreateEventPayload marketCreatePayload = MarketCreateEventPayload.builder()
                .marketId(event.getMarketId().toString())
                .name(event.getMarketName())
                .portCodes(event.getPortCodes())
                .build();

        MarketEvent<MarketCreateEventPayload> marketEvent =
                MarketEvent.createCreatedEvent(event.getMarketName(), marketCreatePayload);

        kafkaProducer.sendAsync(
                config.getProducerName(), config.getTopic(), event.getMarketId().toString(), marketEvent);
    }

    public void sendMarketUpdatedEventAfterCommit(MarketUpdatedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendMarketUpdatedEvent(event);
                }
            });
        } else {
            sendMarketUpdatedEvent(event);
        }
    }

    private void sendMarketUpdatedEvent(MarketUpdatedEvent event) {
        MarketUpdateEventPayload marketUpdatePayload = MarketUpdateEventPayload.builder()
                .marketId(event.getMarketId().toString())
                .name(event.getMarketName())
                .portCodes(event.getPortCodes())
                .build();

        MarketEvent<MarketUpdateEventPayload> marketEvent = MarketEvent.<MarketUpdateEventPayload>builder()
                .specVersion("1.0")
                .type("com.sarp.rule.update")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(event.getMarketName())
                .action("update")
                .data(marketUpdatePayload)
                .build();

        kafkaProducer.sendAsync(
                config.getProducerName(), config.getTopic(), event.getMarketId().toString(), marketEvent);
    }

    public void sendMarketDeletedEventAfterCommit(MarketDeletedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendMarketDeletedEvent(event);
                }
            });
        } else {
            sendMarketDeletedEvent(event);
        }
    }

    private void sendMarketDeletedEvent(MarketDeletedEvent event) {
        MarketDeleteEventPayload marketDeletePayload = MarketDeleteEventPayload.builder()
                .id(event.getDeleteMarketId().toString())
                .build();

        MarketEvent<MarketDeleteEventPayload> marketEvent = MarketEvent.<MarketDeleteEventPayload>builder()
                .specVersion("1.0")
                .type("com.sarp.rule.delete")
                .source("/rule-admin-service")
                .id(UUID.randomUUID().toString())
                .time(OffsetDateTime.now())
                .dataContentType("application/json")
                .subject(event.getDeleteMarketId().toString())
                .action("delete")
                .data(marketDeletePayload)
                .build();

        kafkaProducer.sendAsync(
                config.getProducerName(),
                config.getTopic(),
                event.getDeleteMarketId().toString(),
                marketEvent);
    }
}
