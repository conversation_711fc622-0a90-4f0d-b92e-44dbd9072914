package com.sarp.rule.application.service;

import static java.util.stream.Collectors.toSet;

import com.sarp.commons.kafka.adapter.producer.CloudEventKafkaProducer;
import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.adapter.persistence.model.enums.CriteriaType;
import com.sarp.rule.adapter.persistence.model.enums.FieldType;
import com.sarp.rule.application.event.messaging.Condition;
import com.sarp.rule.application.event.messaging.ConditionSet;
import com.sarp.rule.application.event.messaging.RuleCreatePayload;
import com.sarp.rule.application.event.messaging.RuleDeletePayload;
import com.sarp.rule.application.event.messaging.RuleEvent;
import com.sarp.rule.application.event.messaging.RuleUpdatePayload;
import com.sarp.rule.application.event.messaging.Then;
import com.sarp.rule.application.event.messaging.When;
import com.sarp.rule.config.RuleEventConfig;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.entity.ConditionNode;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleCreatedEvent;
import com.sarp.rule.domain.event.rule.RuleDeletedEvent;
import com.sarp.rule.domain.event.rule.RuleUpdatedEvent;
import com.sarp.rule.domain.repository.CriteriaRepository;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

@Service
@RequiredArgsConstructor
public class RuleEventService {
    private final CloudEventKafkaProducer kafkaProducer;
    private final CriteriaRepository criteriaRepository;
    private final RuleEventConfig config;

    public void sendRuleCreatedEventAfterCommit(RuleCreatedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendRuleCreatedEvent(event);
                }
            });
        } else {
            sendRuleCreatedEvent(event);
        }
    }

    public void sendRuleUpdatedEventAfterCommit(RuleUpdatedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendRuleUpdatedEvent(event);
                }
            });
        } else {
            sendRuleUpdatedEvent(event);
        }
    }

    public void sendRuleDeletedEventAfterCommit(RuleDeletedEvent event) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendRuleDeletedEvent(event);
                }
            });
        } else {
            sendRuleDeletedEvent(event);
        }
    }

    public void sendRuleCreatedEvent(RuleCreatedEvent event) {
        Rule rule = event.getRule();

        RuleCreatePayload payload = RuleCreatePayload.builder()
                .id(rule.getId())
                .ruleType(rule.getRuleType())
                .conditionSets(convertConditionSetGroups(event.getRule()))
                .build();

        RuleEvent<RuleCreatePayload> ruleEvent =
                RuleEvent.createCreateEvent(event.getRule().getId().toString(), payload);
        kafkaProducer.sendAsync(
                config.getProducerName(),
                config.getTopic(),
                event.getRule().getId().toString(),
                ruleEvent);
    }

    private void sendRuleUpdatedEvent(RuleUpdatedEvent event) {
        Rule rule = event.getRule();

        RuleUpdatePayload payload = RuleUpdatePayload.builder()
                .id(rule.getId())
                .ruleType(rule.getRuleType())
                .conditionSets(convertConditionSetGroups(rule))
                .oldConditionSets(event.getOldConditionSetGroups().stream()
                        .flatMap(g -> g.getConditionSets().stream())
                        .map(com.sarp.rule.domain.entity.ConditionSet::getId)
                        .collect(toSet()))
                .build();

        RuleEvent<RuleUpdatePayload> ruleEvent =
                RuleEvent.createUpdateEvent(rule.getId().toString(), payload);
        kafkaProducer.sendAsync(
                config.getProducerName(), config.getTopic(), rule.getId().toString(), ruleEvent);
    }

    public void sendRuleDeletedEvent(RuleDeletedEvent event) {
        UUID ruleId = event.getDeletedRuleId();
        if (ruleId == null) {
            return;
        }

        RuleDeletePayload payload = RuleDeletePayload.builder()
                .id(ruleId)
                .oldConditionSets(event.getConditionSets())
                .build();

        RuleEvent<RuleDeletePayload> ruleEvent = RuleEvent.createDeleteEvent(ruleId.toString(), payload);

        kafkaProducer.sendAsync(config.getProducerName(), config.getTopic(), ruleId.toString(), ruleEvent);
    }

    List<ConditionSet> convertConditionSetGroups(Rule rule) {
        Set<ConditionSetGroup> conditionSetGroups = rule.getConditionSetGroups();

        return conditionSetGroups.stream()
                .flatMap(group -> group.getConditionSets().stream().map(conditionSet -> ConditionSet.builder()
                        .id(conditionSet.getId())
                        .priority(rule.getPriority().intValue())
                        .when(When.builder()
                                .conditions(convertConditionNodes(conditionSet.getConditionNodes()))
                                .build())
                        .then(Then.builder()
                                .actions(conditionSet.getActions().stream()
                                        .map(Action::getId)
                                        .toList())
                                .build())
                        .build()))
                .toList();
    }

    List<Condition> convertConditionNodes(Set<ConditionNode> conditionNodes) {
        Set<UUID> criteriaIds = conditionNodes.stream()
                .map(node -> node.getCondition().getCriteria().getId())
                .collect(toSet());

        Map<UUID, Criteria> criteriaMap = criteriaRepository.findAllById(List.copyOf(criteriaIds)).stream()
                .collect(Collectors.toMap(Criteria::getId, criteria -> criteria));

        return conditionNodes.stream()
                .map(node -> {
                    Criteria criteria =
                            criteriaMap.get(node.getCondition().getCriteria().getId());
                    if (criteria == null) {
                        throw new IllegalStateException("Criteria not found: "
                                + node.getCondition().getCriteria().getId());
                    }
                    return Condition.builder()
                            .type(FieldType.valueOf(criteria.getFieldType().name()))
                            .value(node.getCondition().getValue())
                            .operator(ComparisonOperator.valueOf(
                                    node.getCondition().getOperator()))
                            .criteriaType(
                                    CriteriaType.valueOf(criteria.getType().name()))
                            .criteriaMappingField(criteria.getMappingField())
                            .systemDefinedCriteriaType(criteria.getSystemDefinedCriteriaType())
                            .build();
                })
                .toList();
    }
}
