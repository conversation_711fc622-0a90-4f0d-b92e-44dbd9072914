package com.sarp.rule.application.handler.product;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.mapper.ProductApplicationDTOMapper;
import com.sarp.rule.application.dto.product.ProductApplicationDTO;
import com.sarp.rule.domain.command.product.ProductsQuery;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.repository.ProductRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProductsQueryHandler implements QueryHandler<ProductsQuery, Page<ProductApplicationDTO>> {

    private final ProductRepository productRepository;
    private final ProductApplicationDTOMapper productApplicationDTOMapper;

    @Override
    public Page<ProductApplicationDTO> handle(ProductsQuery query) {
        PaginatedResult<Product> products = productRepository.findProductsMatchingCriteria(
                query.getRsqlQuery(), query.getPagingParams(), query.getSortingParams());

        return new PageImpl<>(
                convertProductsToDTOs(products.getItems()),
                PageRequest.of(
                        query.getPagingParams().getPageNumber(),
                        query.getPagingParams().getPageSize()),
                products.getTotalElements());
    }

    private List<ProductApplicationDTO> convertProductsToDTOs(List<Product> products) {
        return products.stream().map(productApplicationDTOMapper::toDTO).toList();
    }
}
