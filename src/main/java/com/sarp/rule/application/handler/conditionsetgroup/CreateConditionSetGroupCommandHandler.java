package com.sarp.rule.application.handler.conditionsetgroup;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgroup.CreateConditionSetGroupCommand;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupCreatedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreateConditionSetGroupCommandHandler
        implements CommandHandler<CreateConditionSetGroupCommand, ConditionSetGroupCreatedEvent> {
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    @Override
    public ConditionSetGroupCreatedEvent handle(CreateConditionSetGroupCommand command) {
        ConditionSetGroup saved = conditionSetGroupRepository.save(command.getConditionSetGroup());
        return new ConditionSetGroupCreatedEvent(saved);
    }
}
