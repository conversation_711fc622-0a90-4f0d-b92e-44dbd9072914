package com.sarp.rule.application.handler.criteria;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.command.criteria.CountCriteriaByIdsQuery;
import com.sarp.rule.domain.repository.CriteriaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class CountCriteriaByIdQueryHandler implements QueryHandler<CountCriteriaByIdsQuery, Long> {
    private final CriteriaRepository criteriaRepository;

    @Override
    public Long handle(CountCriteriaByIdsQuery query) {
        if (CollectionUtils.isEmpty(query.getCriteriaIds())) {
            return 0L;
        }
        return criteriaRepository.countByIds(query.getCriteriaIds());
    }
}
