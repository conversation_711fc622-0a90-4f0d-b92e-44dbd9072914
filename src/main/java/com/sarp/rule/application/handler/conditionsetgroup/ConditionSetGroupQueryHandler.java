package com.sarp.rule.application.handler.conditionsetgroup;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.query.conditionSetGroup.ConditionSetGroupQuery;
import com.sarp.rule.domain.query.conditionSetGroup.ConditionSetGroupsQuery;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConditionSetGroupQueryHandler implements QueryHandler<ConditionSetGroupQuery, ConditionSetGroup> {
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    @Override
    public ConditionSetGroup handle(ConditionSetGroupQuery query) {
        return conditionSetGroupRepository.findById(query.getConditionSetGroupId());
    }

    public PaginatedResult<ConditionSetGroup> handle(ConditionSetGroupsQuery query) {
        PagingParams pagingParams = query.getPagingParams();

        return conditionSetGroupRepository.findAll(pagingParams);
    }
}
