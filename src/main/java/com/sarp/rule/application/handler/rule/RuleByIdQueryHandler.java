package com.sarp.rule.application.handler.rule;

import com.sarp.rule.application.dto.mapper.RuleApplicationDTOMapper;
import com.sarp.rule.application.dto.rule.RuleApplicationDTO;
import com.sarp.rule.domain.command.rule.RuleByIdQuery;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode;
import com.sarp.rule.domain.repository.RuleRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class RuleByIdQueryHandler {
    private final RuleRepository ruleRepository;
    private final RuleApplicationDTOMapper ruleApplicationDTOMapper;

    @Transactional(readOnly = true)
    public RuleApplicationDTO handle(RuleByIdQuery query) {
        UUID ruleId = query.getId();
        Rule rule = ruleRepository
                .findById(ruleId)
                .orElseThrow(() -> new RuleNotFoundException(RuleExceptionMessageCode.RULE_NOT_FOUND, ruleId));

        return ruleApplicationDTOMapper.toDTO(rule);
    }
}
