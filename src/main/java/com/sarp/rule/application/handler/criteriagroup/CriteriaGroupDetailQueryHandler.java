package com.sarp.rule.application.handler.criteriagroup;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupWithDetailsApplicationMapper;
import com.sarp.rule.domain.query.criteriagroup.CriteriaGroupWithDetailQuery;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CriteriaGroupDetailQueryHandler
        implements QueryHandler<
                CriteriaGroupWithDetailQuery, PaginatedResultApplicationDTO<CriteriaGroupWithDetailsApplicationDTO>> {
    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupWithDetailsApplicationMapper criteriaGroupWithDetailsApplicationDTOMapper;

    @Override
    @Transactional(readOnly = true)
    public PaginatedResultApplicationDTO<CriteriaGroupWithDetailsApplicationDTO> handle(
            CriteriaGroupWithDetailQuery query) {
        PaginatedResult<CriteriaGroupWithDetails> paginatedResult =
                criteriaGroupRepository.findAllWithDetailsByRuleType(query.getRuleType(), query.getPagingParams());

        return PaginatedResultApplicationDTO.of(paginatedResult, criteriaGroupWithDetailsApplicationDTOMapper::toDTO);
    }
}
