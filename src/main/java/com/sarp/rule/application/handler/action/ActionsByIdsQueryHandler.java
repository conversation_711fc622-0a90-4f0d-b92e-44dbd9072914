package com.sarp.rule.application.handler.action;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.query.action.ActionsByIdsQuery;
import com.sarp.rule.domain.repository.ActionRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ActionsByIdsQueryHandler implements QueryHandler<ActionsByIdsQuery, List<Action>> {

    private final ActionRepository actionRepository;

    public List<Action> handle(ActionsByIdsQuery query) {
        return actionRepository.findByIds(query.getActionIds());
    }
}
