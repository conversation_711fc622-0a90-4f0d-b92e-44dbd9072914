package com.sarp.rule.application.handler;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.handler.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommandHandler;
import com.sarp.rule.application.handler.product.ProductsByVariantIdsQueryHandler;
import com.sarp.rule.application.service.RuleEventService;
import com.sarp.rule.domain.command.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommand;
import com.sarp.rule.domain.command.rule.CreateRuleCommand;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateCreatedEvent;
import com.sarp.rule.domain.event.rule.RuleCreatedEvent;
import com.sarp.rule.domain.query.product.ProductsByVariantIdsQuery;
import com.sarp.rule.domain.repository.RuleRepository;
import com.sarp.rule.domain.service.RuleProductVerifier;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateRuleCommandHandler implements CommandHandler<CreateRuleCommand, RuleCreatedEvent> {
    private final RuleRepository ruleRepository;
    private final RuleEventService ruleEventService;
    private final CreateConditionSetGroupTemplateCommandHandler createConditionSetGroupTemplateCommandHandler;
    private final ProductsByVariantIdsQueryHandler productsByVariantIdsQueryHandler;
    private final RuleProductVerifier ruleProductVerifier;

    @Override
    @Transactional
    public RuleCreatedEvent handle(CreateRuleCommand command) {
        Rule rule = command.getRule();

        List<UUID> variantIds = rule.variantIds();
        List<Product> foundProducts =
                productsByVariantIdsQueryHandler.handle(new ProductsByVariantIdsQuery(variantIds));

        ruleProductVerifier.verifyProductsFor(variantIds, foundProducts);

        Rule updatedRule = rule.enrichWithProducts(foundProducts);
        Rule savedRule = ruleRepository.save(updatedRule);

        ConditionSetGroupTemplate savedTemplate = saveConditionSetGroupTemplateIfPresent(
                command.getConditionSetGroupTemplate(), rule.getConditionSetGroups());

        RuleCreatedEvent ruleCreatedEvent = new RuleCreatedEvent(savedRule, savedTemplate);

        ruleEventService.sendRuleCreatedEventAfterCommit(ruleCreatedEvent);

        return ruleCreatedEvent;
    }

    private ConditionSetGroupTemplate saveConditionSetGroupTemplateIfPresent(
            ConditionSetGroupTemplate template, Set<ConditionSetGroup> conditionSetGroups) {
        if (template == null) return null;
        Set<UUID> groupIds =
                conditionSetGroups.stream().map(ConditionSetGroup::getId).collect(Collectors.toSet());
        ConditionSetGroupTemplate updatedTemplate = template.updated(groupIds);
        ConditionSetGroupTemplateCreatedEvent event = createConditionSetGroupTemplateCommandHandler.handle(
                new CreateConditionSetGroupTemplateCommand(updatedTemplate));
        return event.getConditionSetGroupTemplate();
    }
}
