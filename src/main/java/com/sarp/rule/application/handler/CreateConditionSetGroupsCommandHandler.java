package com.sarp.rule.application.handler;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.CreateConditionSetGroupsCommand;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupsCreatedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreateConditionSetGroupsCommandHandler
        implements CommandHandler<CreateConditionSetGroupsCommand, ConditionSetGroupsCreatedEvent> {
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    public ConditionSetGroupsCreatedEvent handle(CreateConditionSetGroupsCommand command) {
        // TODO: munal: burada dogrudan saveAll yapiyoruz, eger ID ler varsa update yapar.
        //  Eger ID ler varsa kayit yapmamaliyiz ve update ayri bir islem olmali sanki?
        List<ConditionSetGroup> savedConditionSetGroups =
                conditionSetGroupRepository.saveAll(command.getConditionSetGroups());
        return new ConditionSetGroupsCreatedEvent(savedConditionSetGroups, Instant.now());
    }
}
