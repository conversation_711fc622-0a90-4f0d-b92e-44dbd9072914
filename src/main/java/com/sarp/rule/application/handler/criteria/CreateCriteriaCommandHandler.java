package com.sarp.rule.application.handler.criteria;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.command.criteria.CreateCriteriaCommand;
import com.sarp.rule.domain.repository.CriteriaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateCriteriaCommandHandler implements CommandHandler<CreateCriteriaCommand, CriteriaApplicationDTO> {
    private final CriteriaRepository criteriaRepository;
    private final CriteriaApplicationDTOMapper criteriaApplicationDTOMapper;

    @Override
    @Transactional
    public CriteriaApplicationDTO handle(CreateCriteriaCommand command) {
        return criteriaApplicationDTOMapper.toDTO(criteriaRepository.save(command.getCriteria()));
    }
}
