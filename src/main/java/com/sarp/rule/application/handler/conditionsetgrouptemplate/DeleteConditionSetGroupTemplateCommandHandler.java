package com.sarp.rule.application.handler.conditionsetgrouptemplate;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgrouptemplate.DeleteConditionSetGroupTemplateCommand;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateDeletedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupTemplateRepository;
import jakarta.transaction.Transactional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteConditionSetGroupTemplateCommandHandler
        implements CommandHandler<DeleteConditionSetGroupTemplateCommand, ConditionSetGroupTemplateDeletedEvent> {

    private final ConditionSetGroupTemplateRepository conditionSetGroupTemplateRepository;

    @Override
    @Transactional
    public ConditionSetGroupTemplateDeletedEvent handle(DeleteConditionSetGroupTemplateCommand command) {
        UUID conditionSetGroupTemplateId = command.getConditionSetGroupTemplateId();
        conditionSetGroupTemplateRepository
                .findById(conditionSetGroupTemplateId)
                .orElseThrow(() -> new RuntimeException(
                        "ConditionSetGroupTemplate not found with ID: " + conditionSetGroupTemplateId));

        conditionSetGroupTemplateRepository.deleteById(conditionSetGroupTemplateId);
        return new ConditionSetGroupTemplateDeletedEvent(conditionSetGroupTemplateId);
    }
}
