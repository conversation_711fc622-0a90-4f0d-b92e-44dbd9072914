package com.sarp.rule.application.handler;

import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConditionSetGroupsQueryHandler {
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    public List<ConditionSetGroup> handle(List<UUID> ids) {
        return conditionSetGroupRepository.findAllById(ids);
    }
}
