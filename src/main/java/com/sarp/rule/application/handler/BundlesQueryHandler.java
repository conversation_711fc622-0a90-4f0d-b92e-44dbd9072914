package com.sarp.rule.application.handler;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.repository.BundleRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BundlesQueryHandler {
    private final BundleRepository bundleRepository;

    public List<Bundle> handle(List<UUID> bundleIds) {
        return bundleRepository.findAllById(bundleIds);
    }
}
