package com.sarp.rule.application.handler.bundle;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_LIST_CANNOT_BE_EMPTY;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.handler.product.ProductsByVariantIdsQueryHandler;
import com.sarp.rule.application.validator.BundleProductValidator;
import com.sarp.rule.domain.command.bundle.CreateBundleCommand;
import com.sarp.rule.domain.command.bundle.CreateBundlesCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.event.bundle.BundlesCreatedEvent;
import com.sarp.rule.domain.exception.BundleDomainException;
import com.sarp.rule.domain.query.product.ProductsByVariantIdsQuery;
import com.sarp.rule.domain.repository.BundleRepository;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class CreateBundleCommandHandler implements CommandHandler<CreateBundlesCommand, BundlesCreatedEvent> {
    private final BundleRepository bundleRepository;
    private final ProductsByVariantIdsQueryHandler productsByVariantIdsQueryHandler;
    private final BundleProductValidator bundleProductValidator;

    @Transactional
    public BundlesCreatedEvent handle(CreateBundlesCommand command) {
        validateCommand(command);

        List<Bundle> bundlesToProcess = prepareBundles(command);

        List<Bundle> savedBundles = bundleRepository.saveAll(bundlesToProcess);

        return new BundlesCreatedEvent(savedBundles);
    }

    private void validateCommand(CreateBundlesCommand command) {
        if (CollectionUtils.isEmpty(command.getBundleCommands())) {
            throw new BundleDomainException(BUNDLE_LIST_CANNOT_BE_EMPTY);
        }
    }

    private List<Bundle> prepareBundles(CreateBundlesCommand command) {
        List<Bundle> bundlesToProcess = new ArrayList<>();

        for (CreateBundleCommand bundleCommand : command.getBundleCommands()) {
            Bundle preparedBundle = prepareBundle(bundleCommand);
            bundlesToProcess.add(preparedBundle);
        }
        return bundlesToProcess;
    }

    private Bundle prepareBundle(CreateBundleCommand command) {
        Bundle bundle = command.getBundle();
        List<UUID> requestedVariantIds = bundle.getProductWithQuantities().stream()
                .map(productWithQuantity -> productWithQuantity.product().getVariantId())
                .toList();

        bundleProductValidator.validateBundleVariantIds(requestedVariantIds, bundle.getName());

        List<Product> foundProducts = fetchAndValidateProducts(requestedVariantIds);

        Map<UUID, Product> foundProductsByVariantId =
                foundProducts.stream().collect(Collectors.toMap(Product::getVariantId, Function.identity()));

        bundle = bundle.withUpdatedProducts(foundProductsByVariantId);

        return bundle;
    }

    private List<Product> fetchAndValidateProducts(List<UUID> requestedVariantIds) {
        ProductsByVariantIdsQuery query = new ProductsByVariantIdsQuery(requestedVariantIds);
        List<Product> products = productsByVariantIdsQueryHandler.handle(query);

        bundleProductValidator.validateAllProductsFound(products, requestedVariantIds);

        return products;
    }
}
