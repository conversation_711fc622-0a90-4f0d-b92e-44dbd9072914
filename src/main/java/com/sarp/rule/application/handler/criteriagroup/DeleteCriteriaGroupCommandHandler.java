package com.sarp.rule.application.handler.criteriagroup;

import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_NOT_FOUND;

import com.sarp.core.application.VoidCommandHandler;
import com.sarp.rule.application.handler.criteria.CountCriteriaByCriteriaGroupIdQueryHandler;
import com.sarp.rule.domain.command.criteria.CountCriteriaByGroupIdQuery;
import com.sarp.rule.domain.command.criteriagroup.DeleteCriteriaGroupCommand;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.validator.CriteriaGroupDeletionValidator;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class DeleteCriteriaGroupCommand<PERSON>and<PERSON> implements VoidCommandHandler<DeleteCriteriaGroupCommand> {

    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupDeletionValidator criteriaGroupDeletionValidator;
    private final CountCriteriaByCriteriaGroupIdQueryHandler countCriteriaByCriteriaGroupIdQueryHandler;

    @Override
    @Transactional
    public void handle(DeleteCriteriaGroupCommand command) {
        UUID criteriaGroupId = command.getCriteriaGroupId();

        if (!criteriaGroupRepository.existsById(criteriaGroupId)) {
            throw new CriteriaGroupNotFoundException(CRITERIA_GROUP_NOT_FOUND, criteriaGroupId);
        }

        long associatedCriteriaCount =
                countCriteriaByCriteriaGroupIdQueryHandler.handle(new CountCriteriaByGroupIdQuery(criteriaGroupId));

        criteriaGroupDeletionValidator.ensureDeletable(criteriaGroupId, associatedCriteriaCount);

        criteriaGroupRepository.deleteById(criteriaGroupId);
    }
}
