package com.sarp.rule.application.handler.market;

import com.sarp.core.application.CommandHandler;
import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.application.service.MarketEventService;
import com.sarp.rule.domain.command.market.CreateMarketCommand;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.event.market.MarketCreatedEvent;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.service.PortVerifier;
import jakarta.transaction.Transactional;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreateMarketCommandHandler implements CommandHandler<CreateMarketCommand, MarketResponseDTO> {
    private final MarketRepository marketRepository;
    private final PortRepository portRepository;

    private final MarketMapper marketMapper;

    private final MarketEventService marketEventService;

    private final PortVerifier portVerifier;

    @Override
    @Transactional
    public MarketResponseDTO handle(CreateMarketCommand command) {
        List<String> portCodes =
                command.getMarket().getPorts().stream().map(Port::getPortCode).toList();
        Set<Port> foundPorts = new HashSet<>(portRepository.findAllByPortCodeIn(portCodes));

        portVerifier.verifyPortExists(portCodes, foundPorts);
        portVerifier.verifyPortsActive(portCodes, foundPorts);

        Market market = Market.builder()
                .name(command.getMarket().getName())
                .ports(foundPorts)
                .build();

        Market savedMarket = marketRepository.save(market);

        MarketCreatedEvent marketCreatedEvent =
                new MarketCreatedEvent(savedMarket.getId(), savedMarket.getName(), portCodes);
        marketEventService.sendMarketCreatedEventAfterCommit(marketCreatedEvent);

        return marketMapper.toMarketResponseDTO(savedMarket);
    }
}
