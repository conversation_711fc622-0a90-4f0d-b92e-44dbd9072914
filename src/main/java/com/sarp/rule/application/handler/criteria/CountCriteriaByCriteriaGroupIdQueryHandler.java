package com.sarp.rule.application.handler.criteria;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.command.criteria.CountCriteriaByGroupIdQuery;
import com.sarp.rule.domain.repository.CriteriaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CountCriteriaByCriteriaGroupIdQueryHandler implements QueryHandler<CountCriteriaByGroupIdQuery, Long> {
    private final CriteriaRepository criteriaRepository;

    @Override
    @Transactional(readOnly = true)
    public Long handle(CountCriteriaByGroupIdQuery query) {
        return criteriaRepository.countByCriteriaGroupId(query.getCriteriaGroupId());
    }
}
