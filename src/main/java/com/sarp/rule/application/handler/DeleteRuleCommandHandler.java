package com.sarp.rule.application.handler;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.service.RuleEventService;
import com.sarp.rule.domain.command.rule.DeleteRuleCommand;
import com.sarp.rule.domain.entity.ConditionSet;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleDeletedEvent;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.repository.RuleRepository;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class DeleteRuleCommandHandler implements CommandHandler<DeleteRuleCommand, RuleDeletedEvent> {
    private final RuleRepository ruleRepository;
    private final RuleEventService ruleEventService;

    @Override
    public RuleDeletedEvent handle(DeleteRuleCommand command) {
        Rule rule =
                ruleRepository.findById(command.getId()).orElseThrow(() -> new RuleNotFoundException(RULE_NOT_FOUND));

        List<UUID> oldConditionSets = rule.getConditionSetGroups().stream()
                .flatMap(g -> g.getConditionSets().stream())
                .map(ConditionSet::getId)
                .toList();

        ruleRepository.deleteById(command.getId());

        RuleDeletedEvent event =
                new RuleDeletedEvent(command.getId(), Instant.now(), oldConditionSets, rule.getRuleType());

        ruleEventService.sendRuleDeletedEventAfterCommit(event);

        return event;
    }
}
