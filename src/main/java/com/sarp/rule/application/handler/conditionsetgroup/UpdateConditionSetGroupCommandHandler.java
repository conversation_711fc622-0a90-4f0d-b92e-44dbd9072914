package com.sarp.rule.application.handler.conditionsetgroup;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgroup.UpdateConditionSetGroupCommand;
import com.sarp.rule.domain.entity.ConditionSet;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupUpdatedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import jakarta.transaction.Transactional;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UpdateConditionSetGroupCommandHandler
        implements CommandHandler<UpdateConditionSetGroupCommand, ConditionSetGroupUpdatedEvent> {
    private final ConditionSetGroupRepository conditionSetGroupRepository;
    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional
    public ConditionSetGroupUpdatedEvent handle(UpdateConditionSetGroupCommand command) {
        ConditionSetGroup existingGroup = conditionSetGroupRepository.findById(command.getConditionSetGroupId());
        Map<UUID, ConditionSet> existingSetsById = mapExistingSetsById(existingGroup);
        Set<ConditionSet> updatedSets = buildUpdatedSets(command, existingSetsById);
        ConditionSetGroup updatedGroup = buildUpdatedGroup(command, existingGroup, updatedSets);
        ConditionSetGroup saved = conditionSetGroupRepository.save(updatedGroup);
        ConditionSetGroupUpdatedEvent conditionSetGroupUpdatedEvent = new ConditionSetGroupUpdatedEvent(saved);
        publishEventIfConditionSetAdded(updatedSets, existingSetsById, conditionSetGroupUpdatedEvent);
        return conditionSetGroupUpdatedEvent;
    }

    private void publishEventIfConditionSetAdded(
            Set<ConditionSet> updatedSets,
            Map<UUID, ConditionSet> existingSetsById,
            ConditionSetGroupUpdatedEvent conditionSetGroupUpdatedEvent) {
        // Only publish event if new condition sets were added
        boolean hasNewConditionSets =
                updatedSets.stream().anyMatch(updatedSet -> !existingSetsById.containsKey(updatedSet.getId()));

        if (hasNewConditionSets) {
            applicationEventPublisher.publishEvent(conditionSetGroupUpdatedEvent);
        }
    }

    private Map<UUID, ConditionSet> mapExistingSetsById(ConditionSetGroup group) {
        return Optional.ofNullable(group.getConditionSets()).orElse(Collections.emptySet()).stream()
                .collect(Collectors.toMap(ConditionSet::getId, cs -> cs));
    }

    private Set<ConditionSet> buildUpdatedSets(
            UpdateConditionSetGroupCommand command, Map<UUID, ConditionSet> existingSetsById) {
        return command.getConditionSetGroup().getConditionSets().stream()
                .map(incomingSet -> updateOrCreateSet(incomingSet, existingSetsById))
                .collect(Collectors.toSet());
    }

    private ConditionSet updateOrCreateSet(ConditionSet incomingSet, Map<UUID, ConditionSet> existingSetsById) {
        // Update existing set
        if (incomingSet.getId() != null) {
            if (!existingSetsById.containsKey(incomingSet.getId())) {
                throw new IllegalArgumentException(
                        "ConditionSet with id " + incomingSet.getId() + " does not exist in the group.");
            }

            ConditionSet existingSet = existingSetsById.get(incomingSet.getId());
            return existingSet.updated(
                    incomingSet.getDisplayOrder(), incomingSet.getConditionNodes(), incomingSet.getActions());

        } else {
            // New set
            return ConditionSet.builder()
                    .id(UUID.randomUUID())
                    .displayOrder(incomingSet.getDisplayOrder())
                    .conditionNodes(incomingSet.getConditionNodes())
                    .actions(incomingSet.getActions())
                    .build();
        }
    }

    private ConditionSetGroup buildUpdatedGroup(
            UpdateConditionSetGroupCommand command, ConditionSetGroup existingGroup, Set<ConditionSet> updatedSets) {
        return existingGroup.updated(
                command.getConditionSetGroup().getName(),
                command.getConditionSetGroup().getDescription(),
                updatedSets);
    }
}
