package com.sarp.rule.application.handler.market;

import static com.sarp.rule.domain.exception.messagecode.MarketExceptionMessageCode.MARKET_NOT_FOUND;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.query.market.MarketByIdQuery;
import com.sarp.rule.domain.repository.MarketRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class MarketByIdQueryHandler {
    private final MarketRepository marketRepository;
    private final MarketMapper marketMapper;

    @Transactional(readOnly = true)
    public MarketResponseDTO handle(MarketByIdQuery query) {
        UUID marketId = query.getId();
        Market market =
                marketRepository.findById(marketId).orElseThrow(() -> new MarketNotFoundException(MARKET_NOT_FOUND));

        return marketMapper.toMarketResponseDTO(market);
    }
}
