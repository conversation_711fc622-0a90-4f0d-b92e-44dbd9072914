package com.sarp.rule.application.handler.rule;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.handler.bundle.BundlesByIdsQueryHandler;
import com.sarp.rule.application.handler.product.ProductsByVariantIdsQueryHandler;
import com.sarp.rule.application.service.RuleEventService;
import com.sarp.rule.domain.command.rule.UpdateRuleCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.ConditionSetGroup;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleUpdatedEvent;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode;
import com.sarp.rule.domain.query.bundle.BundlesByIdsQuery;
import com.sarp.rule.domain.query.product.ProductsByVariantIdsQuery;
import com.sarp.rule.domain.repository.RuleRepository;
import com.sarp.rule.domain.service.RuleBundleVerifier;
import com.sarp.rule.domain.service.RuleProductVerifier;
import com.sarp.rule.domain.service.conditionsetgroup.ConditionSetGroupResolver;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateRuleCommandHandler implements CommandHandler<UpdateRuleCommand, RuleUpdatedEvent> {
    private final RuleRepository ruleRepository;
    private final RuleEventService ruleEventService;
    private final BundlesByIdsQueryHandler bundlesByIdsQueryHandler;
    private final ProductsByVariantIdsQueryHandler productsByVariantIdsQueryHandler;
    private final RuleProductVerifier ruleProductVerifier;
    private final RuleBundleVerifier ruleBundleVerifier;
    private final ConditionSetGroupResolver conditionSetGroupResolver;

    @Override
    public RuleUpdatedEvent handle(UpdateRuleCommand command) {
        UUID ruleId = command.getId();

        Rule oldRule = ruleRepository
                .findById(ruleId)
                .orElseThrow(() -> new RuleNotFoundException(RuleExceptionMessageCode.RULE_NOT_FOUND, ruleId));

        Map<UUID, ConditionSetGroup> oldConditionSetGroupsMap = oldRule.getConditionSetGroups().stream()
                .collect(Collectors.toMap(ConditionSetGroup::getId, Function.identity()));

        Set<Bundle> bundles = getBundlesForCommand(command);

        Set<Product> products = getProductsForCommand(command);

        Set<ConditionSetGroup> arrangedConditionSetGroups =
                conditionSetGroupResolver.resolve(command.getConditionSetGroups(), oldConditionSetGroupsMap);

        Rule updatedRule = oldRule.updated(command, bundles, products, arrangedConditionSetGroups);

        ruleRepository.save(updatedRule);

        RuleUpdatedEvent ruleUpdatedEvent = RuleUpdatedEvent.builder()
                .rule(updatedRule)
                .oldConditionSetGroups(
                        oldConditionSetGroupsMap.values().stream().toList())
                .build();

        ruleEventService.sendRuleUpdatedEventAfterCommit(ruleUpdatedEvent);

        return ruleUpdatedEvent;
    }

    private Set<Bundle> getBundlesForCommand(UpdateRuleCommand command) {
        if (command.getRuleType().equals(RuleType.BUNDLE)) {
            List<UUID> bundleIds = command.getBundleIds().stream().toList();
            Set<Bundle> bundles = new HashSet<>(bundlesByIdsQueryHandler.handle(new BundlesByIdsQuery(bundleIds)));
            ruleBundleVerifier.verifyBundlesFor(bundleIds, bundles);
            return bundles;
        }
        return Collections.emptySet();
    }

    private Set<Product> getProductsForCommand(UpdateRuleCommand command) {
        if (command.getRuleType().equals(RuleType.ALA_CARTE)
                || command.getRuleType().equals(RuleType.PRICING)) {
            List<UUID> variantIds = command.getVariantIds().stream().toList();
            List<Product> foundProducts =
                    productsByVariantIdsQueryHandler.handle(new ProductsByVariantIdsQuery(variantIds));
            ruleProductVerifier.verifyProductsFor(variantIds, foundProducts);
            return new HashSet<>(foundProducts);
        }
        return Collections.emptySet();
    }
}
