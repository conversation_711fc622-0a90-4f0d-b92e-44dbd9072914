package com.sarp.rule.application.handler.bundle;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.query.bundle.BundlesByIdsQuery;
import com.sarp.rule.domain.repository.BundleRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BundlesByIdsQueryHandler implements QueryHandler<BundlesByIdsQuery, List<Bundle>> {
    private final BundleRepository bundleRepository;

    public List<Bundle> handle(BundlesByIdsQuery query) {
        return bundleRepository.findAllActiveByIdIn(query.getBundleIds());
    }
}
