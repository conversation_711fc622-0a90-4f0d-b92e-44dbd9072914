package com.sarp.rule.application.handler.market;

import static com.sarp.rule.domain.exception.messagecode.MarketExceptionMessageCode.MARKET_NOT_FOUND;

import com.sarp.rule.application.service.MarketEventService;
import com.sarp.rule.domain.command.market.DeleteMarketCommand;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.event.market.MarketDeletedEvent;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.repository.MarketRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteMarketCommandHandler {

    private final MarketRepository marketRepository;
    private final MarketEventService marketEventService;

    @Transactional
    public void handle(DeleteMarketCommand command) {
        Market market = marketRepository
                .findById(command.getId())
                .orElseThrow(() -> new MarketNotFoundException(MARKET_NOT_FOUND));

        MarketDeletedEvent marketDeletedEvent = new MarketDeletedEvent(market.getId());

        marketRepository.deleteById(market.getId());

        marketEventService.sendMarketDeletedEventAfterCommit(marketDeletedEvent);
    }
}
