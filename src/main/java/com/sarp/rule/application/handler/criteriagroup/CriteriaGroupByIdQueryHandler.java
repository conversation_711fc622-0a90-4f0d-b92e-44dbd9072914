package com.sarp.rule.application.handler.criteriagroup;

import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_NOT_FOUND;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupByIdQuery;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CriteriaGroupByIdQueryHandler
        implements QueryHandler<CriteriaGroupByIdQuery, CriteriaGroupApplicationDTO> {

    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @Override
    @Transactional(readOnly = true)
    public CriteriaGroupApplicationDTO handle(CriteriaGroupByIdQuery query) {
        CriteriaGroup criteriaGroup = criteriaGroupRepository
                .findByIdWithCriteria(query.getCriteriaGroupId())
                .orElseThrow(
                        () -> new CriteriaGroupNotFoundException(CRITERIA_GROUP_NOT_FOUND, query.getCriteriaGroupId()));

        return criteriaGroupApplicationDTOMapper.toDTO(criteriaGroup);
    }
}
