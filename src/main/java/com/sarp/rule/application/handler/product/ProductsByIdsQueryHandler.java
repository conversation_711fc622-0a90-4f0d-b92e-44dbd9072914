package com.sarp.rule.application.handler.product;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.query.product.ProductsByIdsQuery;
import com.sarp.rule.domain.repository.ProductRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class ProductsByIdsQueryHandler implements QueryHandler<ProductsByIdsQuery, List<Product>> {
    private final ProductRepository productRepository;

    @Transactional(readOnly = true)
    public List<Product> handle(ProductsByIdsQuery query) {
        if (query == null || CollectionUtils.isEmpty(query.getProductEntityIds())) {
            return List.of();
        }
        return productRepository.findAllByIdIn(query.getProductEntityIds());
    }
}
