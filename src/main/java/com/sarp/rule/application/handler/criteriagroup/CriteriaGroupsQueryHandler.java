package com.sarp.rule.application.handler.criteriagroup;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsByIdsQuery;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsQuery;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CriteriaGroupsQueryHand<PERSON>
        implements QueryHandler<CriteriaGroupsQuery, PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO>> {

    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @Override
    @Transactional(readOnly = true)
    public PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO> handle(CriteriaGroupsQuery query) {
        PaginatedResult<CriteriaGroup> paginatedCriteriaGroups =
                criteriaGroupRepository.findAllWithCriteriaDetails(query.getPagingParams());

        return PaginatedResultApplicationDTO.of(paginatedCriteriaGroups, criteriaGroupApplicationDTOMapper::toDTO);
    }

    public List<CriteriaGroup> handle(CriteriaGroupsByIdsQuery query) {
        List<UUID> criteriaGroupIds = query.getCriteriaGroupIds();
        if (criteriaGroupIds == null || criteriaGroupIds.isEmpty()) {
            return List.of();
        }
        return criteriaGroupRepository.findAllById(criteriaGroupIds);
    }
}
