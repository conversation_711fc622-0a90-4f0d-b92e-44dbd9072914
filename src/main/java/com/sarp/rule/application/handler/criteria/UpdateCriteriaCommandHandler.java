package com.sarp.rule.application.handler.criteria;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.command.criteria.UpdateCriteriaCommand;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.repository.CriteriaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateCriteriaCommandHandler implements CommandHandler<UpdateCriteriaCommand, CriteriaApplicationDTO> {
    private final CriteriaRepository criteriaRepository;
    private final CriteriaApplicationDTOMapper criteriaApplicationDTOMapper;

    @Override
    @Transactional
    public CriteriaApplicationDTO handle(UpdateCriteriaCommand command) {
        Criteria existing = criteriaRepository
                .findById(command.getId())
                .orElseThrow(() -> new CriteriaNotFoundException(CRITERIA_NOT_FOUND, command.getId()));

        Criteria updated = existing.updated(command.getName(), command.getDescription());

        return criteriaApplicationDTOMapper.toDTO(criteriaRepository.update(updated));
    }
}
