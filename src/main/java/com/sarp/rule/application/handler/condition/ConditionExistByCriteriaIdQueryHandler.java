package com.sarp.rule.application.handler.condition;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.query.condition.ConditionExistsByCriteriaIdQuery;
import com.sarp.rule.domain.repository.ConditionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConditionExistByCriteriaIdQueryHandler implements QueryHandler<ConditionExistsByCriteriaIdQuery, Boolean> {

    private final ConditionRepository conditionRepository;

    @Override
    public Boolean handle(ConditionExistsByCriteriaIdQuery query) {
        return conditionRepository.existsByCriteriaId(query.getCriteriaId());
    }
}
