package com.sarp.rule.application.handler.market;

import com.sarp.core.application.QueryHandler;
import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.query.market.MarketsQuery;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MarketsQueryHandler implements QueryHandler<MarketsQuery, Page<MarketResponseDTO>> {
    private final MarketRepository marketRepository;
    private final MarketMapper marketMapper;

    @Override
    public Page<MarketResponseDTO> handle(MarketsQuery marketsQuery) {
        PaginatedResult<Market> markets = marketRepository.findAllMarkets(marketsQuery.getPagingParams());

        return new PageImpl<>(
                convertMarketsToDTOs(markets.getItems()),
                PageRequest.of(
                        marketsQuery.getPagingParams().getPageNumber(),
                        marketsQuery.getPagingParams().getPageSize()),
                markets.getTotalElements());
    }

    private List<MarketResponseDTO> convertMarketsToDTOs(List<Market> markets) {
        return markets.stream().map(marketMapper::toMarketResponseDTO).toList();
    }
}
