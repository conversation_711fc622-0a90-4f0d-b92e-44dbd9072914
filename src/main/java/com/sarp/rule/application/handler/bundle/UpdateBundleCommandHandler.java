package com.sarp.rule.application.handler.bundle;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.handler.product.ProductsByVariantIdsQueryHandler;
import com.sarp.rule.application.validator.BundleProductValidator;
import com.sarp.rule.domain.command.bundle.UpdateBundleCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.Product;
import com.sarp.rule.domain.event.bundle.BundleUpdatedEvent;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.query.product.ProductsByVariantIdsQuery;
import com.sarp.rule.domain.repository.BundleRepository;
import com.sarp.rule.domain.valueobject.product.ProductWithQuantity;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class UpdateBundleCommandHandler implements CommandHandler<UpdateBundleCommand, BundleUpdatedEvent> {

    private final BundleProductValidator bundleProductValidator;
    private final BundleRepository bundleRepository;

    private final ProductsByVariantIdsQueryHandler productsByVariantIdsQueryHandler;

    @Override
    @Transactional
    public BundleUpdatedEvent handle(UpdateBundleCommand command) {
        UUID bundleId = command.getBundleId();

        Bundle existingBundle = findBundleById(bundleId);

        Set<Product> validatedProducts = resolveProductsForUpdate(command, existingBundle);

        Bundle savedBundle = updateAndSaveBundle(existingBundle, command, validatedProducts);

        return new BundleUpdatedEvent(savedBundle);
    }

    private Bundle findBundleById(UUID bundleId) {
        return bundleRepository
                .findById(bundleId)
                .orElseThrow(() -> new BundleNotFoundException(BUNDLE_NOT_FOUND, bundleId));
    }

    private Set<Product> resolveProductsForUpdate(UpdateBundleCommand command, Bundle existingBundle) {
        if (command.getProductWithQuantities() == null) {
            return existingBundle.getProductWithQuantities().stream()
                    .map(ProductWithQuantity::product)
                    .collect(Collectors.toSet());
        }

        List<UUID> requestedVariantIds = command.getProductWithQuantities().stream()
                .map(productWithQuantity -> productWithQuantity.product().getVariantId())
                .toList();

        if (CollectionUtils.isEmpty(requestedVariantIds)) {
            return Collections.emptySet();
        }
        return validateAndFetchProducts(requestedVariantIds, existingBundle.getName());
    }

    private Set<Product> validateAndFetchProducts(List<UUID> requestedVariantIds, String bundleName) {
        bundleProductValidator.validateBundleVariantIds(requestedVariantIds, bundleName);

        List<Product> products =
                productsByVariantIdsQueryHandler.handle(new ProductsByVariantIdsQuery(requestedVariantIds));

        bundleProductValidator.validateAllProductsFound(products, requestedVariantIds);

        return new HashSet<>(products);
    }

    private Bundle updateAndSaveBundle(Bundle bundle, UpdateBundleCommand command, Set<Product> products) {

        Map<UUID, Product> productsByVariantId =
                products.stream().collect(Collectors.toMap(Product::getVariantId, Function.identity()));

        Set<ProductWithQuantity> productWithQuantities = command.getProductWithQuantities().stream()
                .map(productWithQuantity -> ProductWithQuantity.of(
                        productsByVariantId.get(productWithQuantity.product().getVariantId()),
                        productWithQuantity.quantity()))
                .collect(Collectors.toSet());

        Bundle updatedBundle = bundle.updated(
                command.getName(),
                command.getDescription(),
                command.getType(),
                command.getStatus(),
                productWithQuantities);

        return bundleRepository.save(updatedBundle);
    }
}
