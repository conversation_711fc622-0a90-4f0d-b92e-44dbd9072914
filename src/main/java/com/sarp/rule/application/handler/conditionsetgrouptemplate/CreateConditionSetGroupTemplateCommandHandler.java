package com.sarp.rule.application.handler.conditionsetgrouptemplate;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgrouptemplate.CreateConditionSetGroupTemplateCommand;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateCreatedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import com.sarp.rule.domain.repository.ConditionSetGroupTemplateRepository;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateConditionSetGroupTemplateCommandHandler
        implements CommandHandler<CreateConditionSetGroupTemplateCommand, ConditionSetGroupTemplateCreatedEvent> {

    private final ConditionSetGroupTemplateRepository conditionSetGroupTemplateRepository;
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    @Override
    @Transactional
    public ConditionSetGroupTemplateCreatedEvent handle(CreateConditionSetGroupTemplateCommand command) {

        Set<UUID> requstedConditionSetGroupIds =
                command.getConditionSetGroupTemplate().getConditionSetGroupIds();
        if (requstedConditionSetGroupIds != null) {
            requstedConditionSetGroupIds.forEach(conditionSetGroupId -> {
                if (!conditionSetGroupRepository.existsById(conditionSetGroupId)) {
                    throw new IllegalArgumentException(
                            "ConditionSetGroup with ID " + conditionSetGroupId + " does not exist.");
                }
            });
        }

        ConditionSetGroupTemplate conditionSetGroupTemplate =
                conditionSetGroupTemplateRepository.save(command.getConditionSetGroupTemplate());
        return new ConditionSetGroupTemplateCreatedEvent(conditionSetGroupTemplate);
    }
}
