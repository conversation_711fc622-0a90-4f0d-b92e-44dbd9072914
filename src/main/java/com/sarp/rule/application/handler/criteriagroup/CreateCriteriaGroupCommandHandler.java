package com.sarp.rule.application.handler.criteriagroup;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CreateCriteriaGroupCommand;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.service.criteriagroup.CriteriaGroupDisplayOrderCalculator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateCriteriaGroupCommandHandler
        implements CommandHandler<CreateCriteriaGroupCommand, CriteriaGroupApplicationDTO> {
    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupDisplayOrderCalculator criteriaGroupDisplayOrderCalculator;
    private final CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @Transactional
    public CriteriaGroupApplicationDTO handle(CreateCriteriaGroupCommand command) {
        Integer nextDisplayOrder = criteriaGroupDisplayOrderCalculator.calculateNextDisplayOrder(
                criteriaGroupRepository.getHighestDisplayOrder());

        CriteriaGroup criteriaGroup = createCriteriaGroup(command, nextDisplayOrder);

        CriteriaGroup saved = criteriaGroupRepository.save(criteriaGroup);
        return criteriaGroupApplicationDTOMapper.toDTO(saved);
    }

    private CriteriaGroup createCriteriaGroup(CreateCriteriaGroupCommand command, Integer nextDisplayOrder) {
        return CriteriaGroup.builder()
                .name(command.getCriteriaGroupName())
                .displayOrder(nextDisplayOrder)
                .build();
    }
}
