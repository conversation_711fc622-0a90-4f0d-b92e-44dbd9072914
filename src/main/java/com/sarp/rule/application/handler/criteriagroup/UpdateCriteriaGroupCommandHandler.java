package com.sarp.rule.application.handler.criteriagroup;

import static com.sarp.rule.domain.exception.messagecode.CriteriaGroupExceptionMessage.CRITERIA_GROUP_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.UpdateCriteriaGroupCommand;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateCriteriaGroupCommandHandler
        implements CommandHandler<UpdateCriteriaGroupCommand, CriteriaGroupApplicationDTO> {
    private final CriteriaGroupRepository criteriaGroupRepository;
    private final CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @Override
    @Transactional
    public CriteriaGroupApplicationDTO handle(UpdateCriteriaGroupCommand command) {
        CriteriaGroup existing = criteriaGroupRepository
                .findByIdWithoutCriteriaList(command.getCriteriaGroupId())
                .orElseThrow(() ->
                        new CriteriaGroupNotFoundException(CRITERIA_GROUP_NOT_FOUND, command.getCriteriaGroupId()));

        CriteriaGroup updated = existing.updated(command.getName());

        CriteriaGroup saved = criteriaGroupRepository.save(updated);
        return criteriaGroupApplicationDTOMapper.toDTO(saved);
    }

    public List<CriteriaGroup> handle(List<UpdateCriteriaGroupCommand> commands) {
        if (commands == null || commands.isEmpty()) {
            throw new CriteriaGroupNotFoundException(CRITERIA_GROUP_NOT_FOUND);
        }

        List<CriteriaGroup> groupsToUpdate = commands.stream()
                .map(cmd -> CriteriaGroup.builder()
                        .id(cmd.getCriteriaGroupId())
                        .name(cmd.getName())
                        .displayOrder(cmd.getDisplayOrder())
                        .build())
                .toList();

        return criteriaGroupRepository.updateAll(groupsToUpdate);
    }
}
