package com.sarp.rule.application.handler.conditionsetgrouptemplate;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgrouptemplate.UpdateConditionSetGroupTemplateCommand;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.event.conditionsetgrouptemplate.ConditionSetGroupTemplateUpdatedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupTemplateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateConditionSetGroupTemplateCommandHandler
        implements CommandHandler<UpdateConditionSetGroupTemplateCommand, ConditionSetGroupTemplateUpdatedEvent> {

    private final ConditionSetGroupTemplateRepository conditionSetGroupTemplateRepository;

    @Override
    @Transactional
    public ConditionSetGroupTemplateUpdatedEvent handle(UpdateConditionSetGroupTemplateCommand command) {
        ConditionSetGroupTemplate existing = conditionSetGroupTemplateRepository
                .findById(command.getConditionSetGroupTemplateId())
                .orElseThrow(() -> new RuntimeException(
                        "ConditionSetGroupTemplate not found with ID: " + command.getConditionSetGroupTemplateId()));

        if (command.getConditionSetGroupTemplate().getName() != null) {
            existing = ConditionSetGroupTemplate.builder()
                    .id(existing.getId())
                    .name(command.getConditionSetGroupTemplate().getName())
                    .author(
                            command.getConditionSetGroupTemplate().getAuthor() != null
                                    ? command.getConditionSetGroupTemplate().getAuthor()
                                    : existing.getAuthor())
                    .conditionSetGroupIds(
                            command.getConditionSetGroupTemplate().getConditionSetGroupIds() != null
                                    ? command.getConditionSetGroupTemplate().getConditionSetGroupIds()
                                    : existing.getConditionSetGroupIds())
                    .build();
        }
        ConditionSetGroupTemplate updatedTemplate = conditionSetGroupTemplateRepository.save(existing);
        return new ConditionSetGroupTemplateUpdatedEvent(updatedTemplate);
    }
}
