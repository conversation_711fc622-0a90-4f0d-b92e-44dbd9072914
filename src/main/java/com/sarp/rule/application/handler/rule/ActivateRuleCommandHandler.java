package com.sarp.rule.application.handler.rule;

import static com.sarp.rule.domain.exception.messagecode.RuleExceptionMessageCode.RULE_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.rule.ActivateRuleCommand;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleActivatedEvent;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.repository.RuleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ActivateRuleCommandHandler implements CommandHandler<ActivateRuleCommand, RuleActivatedEvent> {

    private final RuleRepository ruleRepository;

    @Override
    public RuleActivatedEvent handle(ActivateRuleCommand command) {

        Rule rule =
                ruleRepository.findById(command.getId()).orElseThrow(() -> new RuleNotFoundException(RULE_NOT_FOUND));

        Rule updatedRule = rule.updateRuleStatusToInReview(command);
        ruleRepository.save(updatedRule);

        return RuleActivatedEvent.builder()
                .id(command.getId())
                .oldRuleStatus(rule.getRuleStatus())
                .oldEffectiveDates(rule.getEffectiveDates())
                .build();
    }
}
