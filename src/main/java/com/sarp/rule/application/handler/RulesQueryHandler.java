package com.sarp.rule.application.handler;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.mapper.RuleApplicationDTOMapper;
import com.sarp.rule.application.dto.rule.RuleApplicationDTO;
import com.sarp.rule.domain.command.rule.RulesQuery;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.repository.RuleRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RulesQueryHandler implements QueryHandler<RulesQuery, Page<RuleApplicationDTO>> {

    private final RuleRepository ruleRepository;
    private final RuleApplicationDTOMapper ruleApplicationDTOMapper;

    @Override
    public Page<RuleApplicationDTO> handle(RulesQuery query) {
        PaginatedResult<Rule> rules = ruleRepository.findRulesMatchingCriteria(
                query.getRsqlQuery(), query.getPagingParams(), query.getSortingParams());

        return new PageImpl<>(
                convertRulesToDTOs(rules.getItems()),
                PageRequest.of(
                        query.getPagingParams().getPageNumber(),
                        query.getPagingParams().getPageSize()),
                rules.getTotalElements());
    }

    private List<RuleApplicationDTO> convertRulesToDTOs(List<Rule> rules) {
        return rules.stream().map(ruleApplicationDTOMapper::toDTO).toList();
    }
}
