package com.sarp.rule.application.handler.rule;

import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupUpdatedEvent;
import com.sarp.rule.domain.repository.RuleRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class ConditionSetGroupUpdatedEventHandler {
    private final RuleRepository ruleRepository;

    @EventListener
    @Transactional
    public void process(ConditionSetGroupUpdatedEvent event) {
        UUID conditionSetGroupId = event.getConditionSetGroup().getId();

        ruleRepository.updateStatusesToWaitingForActionByConditionSetGroupId(conditionSetGroupId);
    }
}
