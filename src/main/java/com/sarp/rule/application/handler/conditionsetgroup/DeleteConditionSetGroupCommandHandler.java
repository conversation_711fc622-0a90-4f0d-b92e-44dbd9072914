package com.sarp.rule.application.handler.conditionsetgroup;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.conditionsetgroup.DeleteConditionSetGroupCommand;
import com.sarp.rule.domain.event.conditionsetgroup.ConditionSetGroupDeletedEvent;
import com.sarp.rule.domain.repository.ConditionSetGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteConditionSetGroupCommandHandler
        implements CommandHandler<DeleteConditionSetGroupCommand, ConditionSetGroupDeletedEvent> {
    private final ConditionSetGroupRepository conditionSetGroupRepository;

    @Override
    public ConditionSetGroupDeletedEvent handle(DeleteConditionSetGroupCommand command) {
        var conditionSetGroup = conditionSetGroupRepository.findById(command.getConditionSetGroupId());
        conditionSetGroupRepository.delete(conditionSetGroup);
        return new ConditionSetGroupDeletedEvent(conditionSetGroup.getId());
    }
}
