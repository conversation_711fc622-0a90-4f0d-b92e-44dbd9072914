package com.sarp.rule.application.handler.bundle;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_NOT_FOUND;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.query.bundle.GetBundleQuery;
import com.sarp.rule.domain.query.bundle.GetBundlesQuery;
import com.sarp.rule.domain.repository.BundleRepository;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BundleQueryHandler implements QueryHandler<GetBundleQuery, Bundle> {
    private final BundleRepository bundleRepository;

    public Bundle handle(GetBundleQuery query) {
        UUID bundleId = query.getBundleId();
        return bundleRepository
                .findById(bundleId)
                .orElseThrow(() -> new BundleNotFoundException(BUNDLE_NOT_FOUND, bundleId));
    }

    public PaginatedResult<Bundle> handle(GetBundlesQuery query) {
        PagingParams pagingParams = query.getPagingParams();
        if (query.getStatus() != null) {
            return bundleRepository.findAllByStatus(query.getStatus(), pagingParams);
        } else {
            return bundleRepository.findAllByStatusNot(BundleStatus.PASSIVE, pagingParams);
        }
    }
}
