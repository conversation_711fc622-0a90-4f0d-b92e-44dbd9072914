package com.sarp.rule.application.handler;

import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_FAILED_TO_SAVE;
import static com.sarp.rule.domain.exception.messagecode.ConditionExceptionMessageCode.CONDITION_LIST_CANNOT_BE_EMPTY;

import com.sarp.rule.domain.command.SaveConditionsCommand;
import com.sarp.rule.domain.entity.Condition;
import com.sarp.rule.domain.event.ConditionsSavedEvent;
import com.sarp.rule.domain.exception.ConditionDomainException;
import com.sarp.rule.domain.repository.ConditionRepository;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class SaveConditionsCommandHandler {
    private final ConditionRepository conditionRepository;

    public ConditionsSavedEvent handle(SaveConditionsCommand command) {
        List<Condition> conditionsToSave = command.getConditions();

        if (CollectionUtils.isEmpty(conditionsToSave)) {
            throw new ConditionDomainException(CONDITION_LIST_CANNOT_BE_EMPTY);
        }

        List<Condition> savedConditions = conditionRepository.saveAll(conditionsToSave);
        if (CollectionUtils.isEmpty(savedConditions) || savedConditions.size() != conditionsToSave.size()) {
            throw new ConditionDomainException(CONDITION_FAILED_TO_SAVE);
        }
        return new ConditionsSavedEvent(savedConditions, LocalDateTime.now());
    }
}
