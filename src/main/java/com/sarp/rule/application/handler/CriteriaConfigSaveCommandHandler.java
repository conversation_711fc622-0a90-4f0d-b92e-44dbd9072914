package com.sarp.rule.application.handler;

import com.sarp.core.application.VoidCommandHandler;
import com.sarp.rule.application.handler.criteria.CountCriteriaByIdQueryHandler;
import com.sarp.rule.application.handler.criteriagroup.CriteriaGroupsQueryHandler;
import com.sarp.rule.application.handler.criteriagroup.UpdateCriteriaGroupCommandHandler;
import com.sarp.rule.domain.command.criteria.CountCriteriaByIdsQuery;
import com.sarp.rule.domain.command.criteriaconfig.CriteriaConfigSaveCommand;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsByIdsQuery;
import com.sarp.rule.domain.command.criteriagroup.UpdateCriteriaGroupCommand;
import com.sarp.rule.domain.entity.CriteriaConfig;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaConfigRepository;
import com.sarp.rule.domain.validator.CriteriaConfigValidator;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CriteriaConfigSaveCommandHandler implements VoidCommandHandler<CriteriaConfigSaveCommand> {
    private final CriteriaConfigRepository criteriaConfigRepository;
    private final UpdateCriteriaGroupCommandHandler updateCriteriaGroupCommandHandler;
    private final CriteriaGroupsQueryHandler criteriaGroupsQueryHandler;
    private final CountCriteriaByIdQueryHandler countCriteriaByIdQueryHandler;
    private final CriteriaConfigValidator criteriaConfigValidator;

    @Transactional
    public void handle(CriteriaConfigSaveCommand command) {
        ValidationContext validationContext = validateCommand(command);

        SaveContext saveContext = prepareSaveContext(command, validationContext);

        persistChanges(saveContext);
    }

    /**
     * Validates that all criteria and groups referenced in the command exist
     *
     * @return ValidationContext containing existing entities mapped by their IDs
     */
    private ValidationContext validateCommand(CriteriaConfigSaveCommand command) {
        List<UUID> criteriaIds = extractCriteriaIds(command);
        List<UUID> groupIds = extractCriteriaGroupIds(command);

        long matchingCriteriaCount = countCriteriaByIdQueryHandler.handle(CountCriteriaByIdsQuery.of(criteriaIds));
        criteriaConfigValidator.validateAllCriteriasExist(criteriaIds.size(), matchingCriteriaCount);

        List<CriteriaGroup> existingGroups = criteriaGroupsQueryHandler.handle(CriteriaGroupsByIdsQuery.of(groupIds));
        criteriaConfigValidator.validateAllCriteriaGroupsExist(groupIds.size(), existingGroups.size());

        List<CriteriaConfig> existingConfigs = criteriaConfigRepository.findByCriteriaIdIn(criteriaIds);

        return new ValidationContext(existingConfigs, existingGroups);
    }

    /** Prepares all entities to be saved by merging command data with existing entities */
    private SaveContext prepareSaveContext(CriteriaConfigSaveCommand command, ValidationContext validationContext) {

        Map<UUID, CriteriaConfig> existingConfigsMap = validationContext.getConfigsMap();
        Map<UUID, CriteriaGroup> existingGroupsMap = validationContext.getGroupsMap();

        List<CriteriaConfig> criteriaConfigsToSave = new ArrayList<>();
        List<UpdateCriteriaGroupCommand> criteriaGroupCommands = new ArrayList<>();

        for (CriteriaConfigSaveCommand.GroupConfigSave groupConfig : command.getGroups()) {
            List<CriteriaConfig> groupCriteriaConfigs =
                    processCriteriaConfigs(groupConfig.getCriterias(), existingConfigsMap);
            criteriaConfigsToSave.addAll(groupCriteriaConfigs);

            processGroupUpdate(groupConfig, existingGroupsMap).ifPresent(criteriaGroupCommands::add);
        }

        return new SaveContext(criteriaConfigsToSave, criteriaGroupCommands);
    }

    /** Processes criteria configurations, creating new ones or updating existing ones */
    private List<CriteriaConfig> processCriteriaConfigs(
            List<CriteriaConfigSaveCommand.CriteriaConfigSave> criteriaConfigs,
            Map<UUID, CriteriaConfig> existingConfigsMap) {

        return criteriaConfigs.stream()
                .map(criteriaConfig -> {
                    CriteriaConfig existingConfig = existingConfigsMap.get(criteriaConfig.getCriteriaId());
                    return buildConfigToSave(existingConfig, criteriaConfig);
                })
                .toList();
    }

    /** Creates an update command for the group if it exists */
    private Optional<UpdateCriteriaGroupCommand> processGroupUpdate(
            CriteriaConfigSaveCommand.GroupConfigSave groupConfig, Map<UUID, CriteriaGroup> existingGroupsMap) {

        CriteriaGroup existingGroup = existingGroupsMap.get(groupConfig.getGroupId());

        if (existingGroup == null) {
            return Optional.empty();
        }

        return Optional.of(new UpdateCriteriaGroupCommand(
                existingGroup.getId(), existingGroup.getName(), groupConfig.getDisplayOrder()));
    }

    /** Persists all prepared changes to the database */
    private void persistChanges(SaveContext saveContext) {
        criteriaConfigRepository.saveAll(saveContext.getCriteriaConfigs());
        updateCriteriaGroupCommandHandler.handle(saveContext.getGroupCommands());
    }

    private List<UUID> extractCriteriaIds(CriteriaConfigSaveCommand command) {
        return command.getGroups().stream()
                .flatMap(group -> group.getCriterias().stream())
                .map(CriteriaConfigSaveCommand.CriteriaConfigSave::getCriteriaId)
                .toList();
    }

    private List<UUID> extractCriteriaGroupIds(CriteriaConfigSaveCommand command) {
        return command.getGroups().stream()
                .map(CriteriaConfigSaveCommand.GroupConfigSave::getGroupId)
                .toList();
    }

    /** Creates or updates a CriteriaConfig based on whether it already exists */
    private CriteriaConfig buildConfigToSave(
            CriteriaConfig existingConfig, CriteriaConfigSaveCommand.CriteriaConfigSave criteriaConfig) {

        if (existingConfig == null) {
            return CriteriaConfig.of(
                    criteriaConfig.getCriteriaId(),
                    criteriaConfig.getDisplayOrder(),
                    criteriaConfig.getAllowedRules(),
                    criteriaConfig.getMandatoryRules());
        }

        return existingConfig.update(
                criteriaConfig.getDisplayOrder(), criteriaConfig.getAllowedRules(), criteriaConfig.getMandatoryRules());
    }

    /** Contains validated entities needed for processing */
    @Value
    private static class ValidationContext {
        List<CriteriaConfig> existingConfigs;
        List<CriteriaGroup> existingGroups;

        Map<UUID, CriteriaConfig> getConfigsMap() {
            return existingConfigs.stream()
                    .collect(Collectors.toMap(CriteriaConfig::getCriteriaId, Function.identity()));
        }

        Map<UUID, CriteriaGroup> getGroupsMap() {
            return existingGroups.stream().collect(Collectors.toMap(CriteriaGroup::getId, Function.identity()));
        }
    }

    /** Contains entities prepared for saving */
    @Value
    private static class SaveContext {
        List<CriteriaConfig> criteriaConfigs;
        List<UpdateCriteriaGroupCommand> groupCommands;
    }
}
