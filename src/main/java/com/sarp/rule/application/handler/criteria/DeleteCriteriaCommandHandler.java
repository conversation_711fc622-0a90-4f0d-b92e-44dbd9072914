package com.sarp.rule.application.handler.criteria;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_NOT_FOUND;

import com.sarp.core.application.VoidCommandHandler;
import com.sarp.rule.application.handler.condition.ConditionExistByCriteriaIdQueryHandler;
import com.sarp.rule.domain.command.criteria.DeleteCriteriaCommand;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.query.condition.ConditionExistsByCriteriaIdQuery;
import com.sarp.rule.domain.repository.CriteriaRepository;
import com.sarp.rule.domain.validator.CriteriaDeletionValidator;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteCriteriaCommandHandler implements VoidCommandHandler<DeleteCriteriaCommand> {
    private final CriteriaRepository criteriaRepository;
    private final CriteriaDeletionValidator criteriaDeletionValidator;
    private final ConditionExistByCriteriaIdQueryHandler conditionExistByCriteriaIdQueryHandler;

    public void handle(DeleteCriteriaCommand command) {
        UUID criteriaId = command.getCriteriaId();

        if (!criteriaRepository.existsById(criteriaId)) {
            throw new CriteriaNotFoundException(CRITERIA_NOT_FOUND, criteriaId);
        }

        boolean criteriaIsAssociatedWithCondition =
                conditionExistByCriteriaIdQueryHandler.handle(ConditionExistsByCriteriaIdQuery.of(criteriaId));

        criteriaDeletionValidator.ensureDeletable(criteriaId, criteriaIsAssociatedWithCondition);

        criteriaRepository.deleteById(command.getCriteriaId());
    }
}
