package com.sarp.rule.application.handler.market;

import static com.sarp.rule.domain.exception.messagecode.MarketExceptionMessageCode.MARKET_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.application.service.MarketEventService;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.event.market.MarketUpdatedEvent;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.service.PortVerifier;
import jakarta.transaction.Transactional;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateMarketCommandHandler implements CommandHandler<UpdateMarketCommand, MarketResponseDTO> {
    private final MarketRepository marketRepository;
    private final PortRepository portRepository;

    private final MarketMapper marketMapper;

    private final MarketEventService marketEventService;

    private final PortVerifier portVerifier;

    @Override
    @Transactional
    public MarketResponseDTO handle(UpdateMarketCommand command) {
        Market existingMarket = marketRepository
                .findById(command.getId())
                .orElseThrow(() -> new MarketNotFoundException(MARKET_NOT_FOUND));

        List<String> newPortCodes =
                command.getMarket().getPorts().stream().map(Port::getPortCode).toList();
        Set<Port> updatePorts = new HashSet<>(portRepository.findAllByPortCodeIn(newPortCodes));
        portVerifier.verifyPortExists(newPortCodes, updatePorts);
        portVerifier.verifyPortsActive(newPortCodes, updatePorts);

        Market updateMarket = existingMarket.updated(command, updatePorts);

        Market updatedMarket = marketRepository.save(updateMarket);

        MarketUpdatedEvent marketUpdatedEvent =
                new MarketUpdatedEvent(updatedMarket.getId(), updatedMarket.getName(), newPortCodes);
        marketEventService.sendMarketUpdatedEventAfterCommit(marketUpdatedEvent);

        return marketMapper.toMarketResponseDTO(updatedMarket);
    }
}
