package com.sarp.rule.application.handler.conditionsetgrouptemplate;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.entity.ConditionSetGroupTemplate;
import com.sarp.rule.domain.query.conditionsetgrouptemplate.ConditionSetGroupTemplateQuery;
import com.sarp.rule.domain.query.conditionsetgrouptemplate.ConditionSetGroupTemplatesQuery;
import com.sarp.rule.domain.repository.ConditionSetGroupTemplateRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ConditionSetGroupTemplateQueryHandler
        implements QueryHandler<ConditionSetGroupTemplateQuery, ConditionSetGroupTemplate> {

    private final ConditionSetGroupTemplateRepository conditionSetGroupTemplateRepository;

    @Override
    public ConditionSetGroupTemplate handle(ConditionSetGroupTemplateQuery query) {
        UUID conditionSetGroupTemplateId = query.getConditionSetGroupTemplateId();
        return conditionSetGroupTemplateRepository
                .findById(conditionSetGroupTemplateId)
                .orElseThrow(() -> new RuntimeException(
                        "ConditionSetGroupTemplate not found with ID: " + conditionSetGroupTemplateId));
    }

    public PaginatedResult<ConditionSetGroupTemplate> handle(ConditionSetGroupTemplatesQuery query) {
        PagingParams pagingParams = query.getPagingParams();
        return conditionSetGroupTemplateRepository.findAll(pagingParams);
    }
}
