package com.sarp.rule.application.handler.bundle;

import static com.sarp.rule.domain.exception.messagecode.BundleExceptionMessageCode.BUNDLE_NOT_FOUND;

import com.sarp.core.application.CommandHandler;
import com.sarp.rule.domain.command.bundle.DeleteBundleCommand;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.event.bundle.BundleDeletedEvent;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.repository.BundleRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteBundleCommandHandler implements CommandHandler<DeleteBundleCommand, BundleDeletedEvent> {
    private final BundleRepository bundleRepository;

    public BundleDeletedEvent handle(DeleteBundleCommand command) {
        UUID bundleId = command.getBundleId();
        Bundle bundle = bundleRepository
                .findById(bundleId)
                .orElseThrow(() -> new BundleNotFoundException(BUNDLE_NOT_FOUND, bundleId));
        bundleRepository.delete(bundle);
        return new BundleDeletedEvent(bundleId);
    }
}
