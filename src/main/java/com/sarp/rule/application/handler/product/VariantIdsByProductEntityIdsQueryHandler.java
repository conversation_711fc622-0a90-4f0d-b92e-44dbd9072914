package com.sarp.rule.application.handler.product;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.domain.query.product.VariantIdsByProductEntityIdsQuery;
import com.sarp.rule.domain.repository.ProductRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class VariantIdsByProductEntityIdsQueryHandler
        implements QueryHandler<VariantIdsByProductEntityIdsQuery, List<UUID>> {
    private final ProductRepository productRepository;

    @Transactional(readOnly = true)
    public List<UUID> handle(VariantIdsByProductEntityIdsQuery query) {
        if (query == null || CollectionUtils.isEmpty(query.getProductEntityIds())) {
            return List.of();
        }
        return productRepository.findAllVariantIdsByProductEntityIds(query.getProductEntityIds());
    }
}
