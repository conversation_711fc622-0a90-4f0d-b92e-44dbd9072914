package com.sarp.rule.application.handler.criteria;

import static com.sarp.rule.domain.exception.messagecode.CriteriaExceptionMessageCode.CRITERIA_NOT_FOUND;

import com.sarp.core.application.QueryHandler;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.command.criteria.CriteriaByIdQuery;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.repository.CriteriaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CriteriaByIdQueryHandler implements QueryHandler<CriteriaByIdQuery, CriteriaApplicationDTO> {
    private final CriteriaRepository criteriaRepository;
    private final CriteriaApplicationDTOMapper criteriaApplicationDTOMapper;

    @Override
    @Transactional(readOnly = true)
    public CriteriaApplicationDTO handle(CriteriaByIdQuery query) {
        Criteria criteria = criteriaRepository
                .findById(query.getCriteriaId())
                .orElseThrow(() -> new CriteriaNotFoundException(CRITERIA_NOT_FOUND, query.getCriteriaId()));

        return criteriaApplicationDTOMapper.toDTO(criteria);
    }
}
