databaseChangeLog:
  - changeSet:
      id: 000-create-sarp_commons-schema
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - sql:
            sql: CREATE SCHEMA IF NOT EXISTS "commons"

  - changeSet:
      id: 001-create-currency-code-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: currency
            columns:
              - column:
                  name: currency_code
                  type: CHAR(3)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: decimal_count
                  type: INTEGER
                  defaultValue: 2
                  constraints:
                    nullable: false

  - changeSet:
      id: 002-create-country-currency-mapping-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: country_currency_mapping
            columns:
              - column:
                  name: appendix_name
                  type: VARCHAR(50)
                  defaultValue: 'Country Currency'
                  constraints:
                    nullable: false
              - column:
                  name: country_code
                  type: CHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: country_description
                  type: VARCHAR(100)
                  constraints:
                    nullable: false
              - column:
                  name: currency_code
                  type: VARCHAR(25)
                  constraints:
                    nullable: false
              - column:
                  name: currency_qualifier
                  type: VARCHAR(50)
              - column:
                  name: status
                  type: VARCHAR(20)
                  defaultValue: 'Current'
              - column:
                  name: date_effective
                  type: VARCHAR(25)

  - changeSet:
      id: 003-create-port-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: port
            columns:
              - column:
                  name: port_code
                  type: VARCHAR(4)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: port_name
                  type: VARCHAR(100)
                  constraints:
                    nullable: false
              - column:
                  name: city_code
                  type: VARCHAR(3)
              - column:
                  name: longitude
                  type: DECIMAL(15,8)
              - column:
                  name: latitude
                  type: DECIMAL(15,8)
              - column:
                  name: port_type
                  type: VARCHAR(20)
                  defaultValue: 'AIRPORT'
                  constraints:
                    nullable: false
              - column:
                  name: has_comfort
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: has_convertible_currency
                  type: BOOLEAN
                  defaultValueBoolean: true
              - column:
                  name: is_round_trip_mandatory
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_domestic
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_active
                  type: BOOLEAN
                  defaultValueBoolean: true
              - column:
                  name: is_refundable
                  type: BOOLEAN
                  defaultValueBoolean: true
              - column:
                  name: is_award
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_star_award
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: ajet_active
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_spa
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_spa_arrival
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_active_for_web_agent
                  type: BOOLEAN
                  defaultValueBoolean: true
              - column:
                  name: is_deleted
                  type: BOOLEAN
                  defaultValueBoolean: false

  - changeSet:
      id: 004-create-pax-type-atpco-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: pax_type_atpco
            columns:
              - column:
                  name: appendix_name
                  type: VARCHAR(50)
                  defaultValue: 'Passenger Type Codes (PTC)'
                  constraints:
                    nullable: false
              - column:
                  name: passenger_type_code
                  type: CHAR(3)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: qualifier
                  type: VARCHAR(50)
              - column:
                  name: status
                  type: VARCHAR(20)
                  defaultValue: 'Current'
              - column:
                  name: date_effective
                  type: DATE

  - changeSet:
      id: 005-create-pax-type-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: pax_type
            columns:
              - column:
                  name: pax_code
                  type: VARCHAR(20)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: ndc_type
                  type: VARCHAR(10)
              - column:
                  name: amadeus_code
                  type: VARCHAR(10)
              - column:
                  name: amadeus_applied_code
                  type: VARCHAR(10)
              - column:
                  name: troya_code
                  type: VARCHAR(10)
              - column:
                  name: rank
                  type: INTEGER
              - column:
                  name: order_priority
                  type: INTEGER
              - column:
                  name: is_domestic
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_international
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_infant
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: accompany_infant
                  type: BOOLEAN
                  defaultValueBoolean: false
              - column:
                  name: is_child
                  type: BOOLEAN
                  defaultValueBoolean: false

  - changeSet:
      id: 006-create-state-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: state
            columns:
              - column:
                  name: appendix_name
                  type: VARCHAR(50)
                  defaultValue: 'State Province'
                  constraints:
                    nullable: false
              - column:
                  name: state_code
                  type: CHAR(2)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: state_name
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: country_code
                  type: CHAR(2)
                  constraints:
                    nullable: false
              - column:
                  name: associated_code
                  type: INTEGER
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: VARCHAR(20)
                  defaultValue: 'Current'
              - column:
                  name: date_effective
                  type: DATE

  - changeSet:
      id: 007-create-web-currency-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: web_currency
            columns:
              - column:
                  name: country_code
                  type: CHAR(2)
                  constraints:
                    nullable: false
              - column:
                  name: country_name
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: currency_code
                  type: CHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: digit_grouping_symbol
                  type: VARCHAR(10)
                  constraints:
                    nullable: false
              - column:
                  name: decimal_placer_symbol
                  type: VARCHAR(10)
                  constraints:
                    nullable: false
        - addPrimaryKey:
            tableName: web_currency
            schemaName: commons
            columnNames: currency_code, country_code
            constraintName: pk_web_currency

  - changeSet:
      id: 008-create-cabin-type-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: cabin_type
            columns:
              - column:
                  name: cabin_type_code
                  type: CHAR(1)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: cabin_type_name
                  type: VARCHAR(20)
                  constraints:
                    nullable: false
              - column:
                  name: cabin_padis_code
                  type: INTEGER
                  constraints:
                    nullable: false
              - column:
                  name: res_book_design_code
                  type: CHAR(1)
                  constraints:
                    nullable: false

  - changeSet:
      id: 009-create-region-continent-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: region_continent
            columns:
              - column:
                  name: region_code
                  type: VARCHAR(2)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: region_name
                  type: VARCHAR(50)
                  constraints:
                    nullable: false

  - changeSet:
      id: 010-create-country-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: country
            columns:
              - column:
                  name: country_code
                  type: CHAR(2)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: country_name
                  type: VARCHAR(100)
                  constraints:
                    nullable: false
              - column:
                  name: c3_code
                  type: CHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: numeric_code
                  type: CHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: phone_code
                  type: INTEGER
                  constraints:
                    nullable: false
              - column:
                  name: currency_code
                  type: CHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: secondary_currency_code
                  type: CHAR(3)
              - column:
                  name: flight_map_region_code
                  type: CHAR(2)
                  constraints:
                    nullable: false

  - changeSet:
      id: 011-create-city-table
      author: ahmet yilmaz - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            schemaName: commons
            tableName: city
            columns:
              - column:
                  name: country
                  type: VARCHAR(100)
              - column:
                  name: city_code
                  type: VARCHAR(10)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: city_name
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
