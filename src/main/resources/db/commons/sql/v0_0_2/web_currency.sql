truncate table commons.web_currency;
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TR', 'Türkiye', 'TRY', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AF', 'Afganistan', 'USD', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DE', 'Almanya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('US', 'AmerikaBirlesikDevletleri', 'USD', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AS', 'AmerikanSamoasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AD', 'Andorra', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AO', 'Angola', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AI', 'Anguilla', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AQ', 'Antarktika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AG', 'AntiguaveBarbuda', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AR', 'Arjantin', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AL', 'Arnavutluk', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AW', 'Aruba', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AU', 'Avustralya', 'AUD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AT', 'Avusturya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AZ', 'Azerbaycan', 'AZN', 'SPACE', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BS', 'Bahamalar', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BH', 'Bahreyn', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BD', 'Banglades', 'BDT', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BB', 'Barbados', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('EH', 'BatiSahra', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BY', 'Belarus', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BE', 'Belçika', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BZ', 'Belize', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BJ', 'Benin', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BM', 'Bermuda', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BT', 'Bhutan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AE', 'BirlesikArapEmirlikleri', 'AED', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GB', 'BirlesikKrallik', 'GBP', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BO', 'Bolivya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BA', 'Bosna-Hersek', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BW', 'Botsvana', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BV', 'BouvetAdasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BR', 'Brezilya', 'BRL', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IO', 'BritanyaHintOkyanusuTopraklari', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BN', 'BruneiDarusselam', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BG', 'Bulgaristan', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BF', 'BurkinaFaso', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('BI', 'Burundi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KY', 'CaymanAdasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GI', 'Cebelitarik', 'GBP', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DZ', 'Cezayir', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CX', 'ChristmasAdasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DJ', 'Cibuti', 'DJF', 'SPACE', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CC', 'Cocos(Keyling)Adalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CK', 'CookAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CW', 'Curaçao', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TD', 'Çad', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CZ', 'ÇekCumhuriyeti', 'CZK', 'SPACE', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CN', 'Çin', 'CNY', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DK', 'Danimarka', 'DKK', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CD', 'DemokratikKongo', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TP', 'DoguTimor', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DM', 'Dominika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('DO', 'DominikCumhuriyeti', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('EC', 'Ekvador', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GQ', 'EkvatorGinesi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SV', 'ElSalvador', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ID', 'Endonezya', 'IDR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ER', 'Eritre', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AM', 'Ermenistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('EE', 'Estonya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ET', 'Etiyopya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FK', 'FalklandAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FO', 'FaroeAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MA', 'Fas', 'MAD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FJ', 'Fiji', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CI', 'FildisiSahili', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PH', 'Filipinler', 'PHP', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PS', 'Filistin', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FI', 'Finlandiya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FR', 'Fransa', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GF', 'FransizGuyanasi', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TF', 'FransizGüneyveAntarktikaTopraklari', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PF', 'FransizPolinezyasi', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GA', 'Gabon', 'XAF', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GM', 'Gambiya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GH', 'Gana', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GN', 'Gine', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GW', 'Gine-Bissau', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GD', 'Grenada', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GL', 'Grönland', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GP', 'Guadölup', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GU', 'Guam', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GT', 'Guatemala', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GY', 'Guyana', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ZA', 'GüneyAfrika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GS', 'GüneyGeorgiaVeGüneySandwichAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KR', 'GüneyKore', 'KRW', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GE', 'Gürcistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HT', 'Haiti', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HM', 'HeardAdasiVeMcdonaldAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HR', 'Hirvatistan', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IN', 'Hindistan', 'INR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NL', 'Hollanda', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('AN', 'HollandaAntilleri', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HN', 'Honduras', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HK', 'HongKong', 'HKD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IQ', 'Irak', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IR', 'Iran', 'IRR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IE', 'Irlanda', 'GBP', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ES', 'Ispanya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IL', 'Israil', 'ILS', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SE', 'Isveç', 'SEK', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CH', 'Isviçre', 'CHF', 'SPACE', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IT', 'Italya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('IS', 'Izlanda', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('JM', 'Jamaika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('JP', 'Japonya', 'JPY', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KH', 'Kamboçya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CM', 'Kamerun', 'XAF', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CA', 'Kanada', 'CAD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ME', 'Karadag', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('QA', 'Katar', 'QAR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KZ', 'Kazakistan', 'KZT', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KE', 'Kenya', 'KES', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KG', 'Kirgizistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KI', 'Kiribati', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CO', 'Kolombiya', 'COP', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KM', 'Komor', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CG', 'Kongo', 'XAF', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('XK', 'Kosova', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CR', 'KostaRika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KW', 'Kuveyt', 'KWD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CY', 'KuzeyKibrisTürkCumhuriyeti', 'TRY', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KT', 'KuzeyKibrisTürkCumhuriyeti', 'TRY', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KP', 'KuzeyKore', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MP', 'KuzeyMarianaAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CU', 'Küba', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LA', 'Laos', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LS', 'Lesoto', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LV', 'Letonya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LR', 'Liberya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LY', 'Libya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LI', 'Lihtenstayn', 'CHF', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LT', 'Litvanya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LB', 'Lübnan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LU', 'Lüksemburg', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('HU', 'Macaristan', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MG', 'Madagaskar', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MO', 'Makao', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MK', 'Makedonya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MW', 'Malavi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MV', 'Maldivler', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MY', 'Malezya', 'MYR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ML', 'Mali', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MT', 'Malta', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MH', 'MarshallAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MQ', 'Martinik', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MU', 'Mauritius', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('YT', 'Mayotte', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MX', 'Meksika', 'MXN', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('EG', 'Misir', 'EGP', 'COMMA', 'DOT');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('FM', 'Mikronezya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MN', 'Mogolistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MD', 'Moldova', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MC', 'Monako', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MS', 'Montserrat', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MR', 'Moritanya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MZ', 'Mozambik', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('MM', 'Myanmar', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NA', 'Namibya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NR', 'Nauru', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NP', 'Nepal', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NE', 'Nijer', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NG', 'Nijerya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NI', 'Nikaragua', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NU', 'Niue', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NF', 'NorfolkAdasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NO', 'Norveç', 'NOK', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CF', 'OrtaAfrika', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('UZ', 'Özbekistan', 'UZS', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PK', 'Pakistan', 'PKR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PW', 'Palau', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PA', 'Panama', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PG', 'PapuaYeniGine', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PY', 'Paraguay', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PE', 'Peru', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PN', 'PitcairnAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PL', 'Polonya', 'PLN', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PT', 'Portekiz', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PR', 'PortoRiko', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('RE', 'Reunion', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('RO', 'Romanya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('RW', 'Ruanda', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('RU', 'Rusya', 'RUP', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SH', 'SaintHelena', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('KN', 'SaintKittsVeNevis', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LC', 'SaintLucia', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SX', 'SaintMarteen', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('PM', 'SaintPierreVeMiquelon', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VC', 'SaintVincentveGrenadinler', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('WS', 'Samoa', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SM', 'SanMarino', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ST', 'SaoTomeVePrincipe', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SN', 'Senegal', 'XOF', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SC', 'Seyseller', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('RS', 'Sirbistan', 'RSD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SL', 'SierraLeone', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SG', 'Singapur', 'SGD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SK', 'Slovakya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SI', 'Slovenya', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SB', 'SolomonAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SO', 'Somali', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('LK', 'SriLanka', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SD', 'Sudan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SR', 'Surinam', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SY', 'Suriye', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SA', 'SuudiArabistan', 'SAR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SJ', 'SvalbardVeJanMayenAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SZ', 'Svaziland', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CL', 'Sili', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TJ', 'Tacikistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TZ', 'Tanzanya', 'TZS', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TH', 'Tayland', 'THB', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TW', 'Tayvan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TG', 'Togo', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TK', 'Tokelau', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TO', 'Tonga', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TT', 'TrinidadveTobago', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TN', 'Tunus', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TC', 'TurksveCaicosAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TV', 'Tuvalu', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('TM', 'Türkmenistan', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('UG', 'Uganda', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('UA', 'Ukrayna', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('OM', 'Umman', 'OMR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('UM', 'UnitedStatesMinorOutlyingIslands', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('UY', 'Uruguay', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('JO', 'Ürdün', 'JOD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VU', 'Vanuatu', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VA', 'Vatikan', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VE', 'Venezuela', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VN', 'Vietnam', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VG', 'VirginAdalari(Ingiliz)', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('VI', 'Virginadasi', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('WF', 'WallisVeFutunaAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('YE', 'Yemen', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NC', 'YeniKaledonya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('NZ', 'YeniZelanda', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('CV', 'YesilBurunAdalari', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('YU', 'Yugoslavya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('GR', 'Yunanistan', 'EUR', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ZM', 'Zambiya', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('ZW', 'Zimbabve', 'USD', 'DOT', 'COMMA');
insert into commons.web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) values ('SS', 'GüneySudan', 'USD', 'COMMA', 'DOT');
