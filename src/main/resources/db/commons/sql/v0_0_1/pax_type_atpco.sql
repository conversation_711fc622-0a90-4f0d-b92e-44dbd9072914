INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ACC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AEB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AEC', 'Child/Infant', 'Current', '2021-06-28');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AEF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AEV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AGT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ANN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'APA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'APC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'API', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'APS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ASB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ASF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AST', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BAG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BLD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BRV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BUD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CCA', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CCH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CCM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CCR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CDT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CEV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CFM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CHR', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CLG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CME', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNI', 'Child/Infant', 'Current', '2021-02-15');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CPN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CSB', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CTZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CVN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DAB', 'No Restriction', 'Current', '2021-06-07');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DAJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DAT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DCD', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DCP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DHC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DIS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DNS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DOD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EAQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EAT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EAV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ECH', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EDT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EVP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EXP', 'No Restriction', 'Current', '2020-09-14');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBD', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBJ', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FBV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FCP', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FCU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGD', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGJ', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIP', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FNP', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FSP', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FXP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FXT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GCF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GCT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GDP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GEX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GRI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GRP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GRS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GSP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GST', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GVA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GVT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GYT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HAJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HOF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ICF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ICN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ICP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IDK', 'No Restriction', 'Current', '2022-01-10');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INR', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INX', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INY', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ISB', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ISR', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ITF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ITS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ITU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ITX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ITY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IWB', 'Age Restricted', 'Current', '2024-12-09');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JCB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JMP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JNS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JOU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JRC', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LBR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LIF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LNS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LTC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LUN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MBT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MCR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MDP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MDR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MEA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MEC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MED', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MEI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MIS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MNS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MRE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MSB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MSG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MSP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MSS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MUS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MXS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NAT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NBA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NBC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NBI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NBS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NBU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NMA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NME', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NMQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NSB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NTL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OFS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OFW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ONN', 'Age Restricted', 'Current', '2020-02-03');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OTS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PEA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PEC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PEI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PES', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PCR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PGO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIC', 'Child/Infant', 'Current', '2024-12-02');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PII', 'Child/Infant', 'Current', '2024-12-02');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIS', 'Child/Infant', 'Current', '2024-12-02');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIU', 'Child/Infant', 'Current', '2024-12-02');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PLM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PTT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'REC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'REF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SAC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SAT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SCC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SCF', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SCM', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SDB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SDR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SEA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SEC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SEF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SES', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SKY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SNS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SRC', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SRR', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SUC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TEA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TIA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TIF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TIM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TIN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TIS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TNF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TUR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TUX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UDA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UDN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UNR', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UNV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VAC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VAG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VFF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VFN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VFR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VFS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WBC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WBI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WBS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WBU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WEB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WOM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YCB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YSB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YTH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YTR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZBC', 'Child/Infant', 'Current', '2024-11-18');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCO', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZED', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZES', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZEU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZMA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZWA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ACI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ACP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ADG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AJI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ARP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ART', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ASC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ASI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ATB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ATC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ATD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ATL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ATP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'AWA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BEB', 'No Restriction', 'Current', '2020-11-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BNA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BNB', 'No Restriction', 'Current', '2023-04-03');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BNC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BND', 'Child/Infant', 'Current', '2023-04-03');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'BPK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CBC', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CBI', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CBO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CDE', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CEH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CEJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CET', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CHD', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CJD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CJE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CJH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CJI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CJV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CMX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNA', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CNE', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'CRR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DEF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'DIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EAC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMF', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EMU', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ENO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EPC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EPI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EPU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ESC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'EYC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FAE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FCC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FEE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FFE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FLM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FLY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'FNC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GBE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GCH', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GGZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GLY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GMZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GVB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GVM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'GVZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HDA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HDH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HDL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HEC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HEH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HEM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HNA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'HNN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IEP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IFN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'IJI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INE', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ING', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'INP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JIC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JIV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JOB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JOM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JWA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JWB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JWC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'JWZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'KGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'KGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'KIF', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'KJA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'KNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LMG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LPP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'LUV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MAM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MBA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MCU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MLI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MLO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MML', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MMU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MNC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MTG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'MTI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NEG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NEH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NET', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NHA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NHP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NHT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NNN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'NRF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'OGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PAC', 'No Restriction', 'Current', '2022-03-07');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PAP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PCB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PCF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PCP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PFZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PHH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PIP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PMC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PMG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PNC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'POS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PPZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PRZ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PSB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PSP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PSR', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PST', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PTA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PTC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'PTQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'QNN', 'Child/Infant', 'Current', '2020-11-30');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'RGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'RGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'RIF', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'RNN', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'RUB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SBB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SBC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SBD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SBE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SBK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SDC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SDG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SEL', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SGV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SHL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SIL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SKI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SPL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SRS', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'STE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SUA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SUB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'SUU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TDC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'TDD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'UNA', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'VDB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WBT', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WEX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WFA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WHV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WOA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WOD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WTA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'WTS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'XES', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'XMA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'XNN', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'XPA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'YTE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZAP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZBE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZBI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZBP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZCP', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDA', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDC', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDK', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZDT', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFK', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZFY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGK', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZGY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHK', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZHY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIA', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIC', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIK', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIP', 'No Restriction', 'Current', '2022-02-23');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIS', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZIT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZJT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZKR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZLC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZLI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZLR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZLU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZLX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZMP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZMY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZNL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZOR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPF', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZPR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZRC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZRD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZRG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZRS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSC', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSF', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSM', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZSY', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZTN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZTP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZTR', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZTW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZUI', 'Child/Infant', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZUS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZVN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZWC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZWV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZXT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZYA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZYC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZYP', 'Age Restricted', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZA', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZB', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZC', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZD', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZE', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZG', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZH', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZI', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZJ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZK', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZL', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZM', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZN', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZO', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZP', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZQ', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZS', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZT', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZU', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZV', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZW', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZX', 'No Restriction', 'Current', '2019-01-01');
INSERT INTO "commons".pax_type_atpco (appendix_name, passenger_type_code, qualifier, status, date_effective) VALUES ('Passenger Type Codes (PTC)', 'ZZY', 'No Restriction', 'Current', '2019-01-01');
