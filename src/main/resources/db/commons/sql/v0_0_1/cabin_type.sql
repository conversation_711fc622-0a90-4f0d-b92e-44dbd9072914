INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('N', 'ECONOMY', 3, 'Y');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('M', 'ECONOMY', 3, 'Y');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('C', 'BUSINESS', 2, 'C');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('B', 'BUSINESS', 2, 'C');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('E', 'ECONOMY', 3, 'Y');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('F', 'BUSINESS', 2, 'C');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('R', 'ECONOMY', 3, 'Y');
INSERT INTO "commons".cabin_type (cabin_type_code, cabin_type_name, cabin_padis_code, res_book_design_code) VALUES ('Y', 'ECONOMY', 3, 'Y');
