INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('ARS', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('AUD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('AZN', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('BDT', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('BGN', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('BRL', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('CAD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('CHF', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('CNY', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('COP', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('CZK', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('DJF', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('DKK', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('EGP', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('EUR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('GBP', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('HKD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('HRK', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('HUF', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('IDR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('ILS', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('INR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('IRR', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('JOD', 3);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('JPY', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('KES', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('KRW', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('KWD', 3);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('KZT', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('MAD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('MRU', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('MXN', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('MYR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('NGN', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('NOK', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('OMR', 3);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('PHP', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('PKR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('PLN', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('QAR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('RON', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('RSD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('RUB', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('SAR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('SEK', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('SGD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('THB', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('TRY', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('TZS', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('UAH', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('USD', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('UZS', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('XAF', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('XOF', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('AED', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('TWD', 0);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('BHD', 3);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('ZAR', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('TND', 3);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('GEL', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('CLP', 2);
INSERT INTO "commons".currency (currency_code, decimal_count) VALUES ('MUR', 2);
