INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'AL', 'Alabama', 'US', 1, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'AK', 'Alaska', 'US', 53, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'AB', 'Alberta', 'CA', 60, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'AZ', 'Arizona', 'US', 2, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'AR', 'Arkansas', 'US', 3, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'BC', 'British Columbia', 'CA', 61, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'CA', 'California', 'US', 4, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'CO', 'Colorado', 'US', 5, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'CT', 'Connecticut', 'US', 6, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'DE', 'Delaware', 'US', 7, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'DC', 'District of Columbia', 'US', 49, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'FL', 'Florida', 'US', 8, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'GA', 'Georgia', 'US', 9, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'HI', 'Hawaii', 'US', 54, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'ID', 'Idaho', 'US', 10, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'IL', 'Illinois', 'US', 11, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'IN', 'Indiana', 'US', 12, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'IA', 'Iowa ', 'US', 13, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'KS', 'Kansas', 'US', 14, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'KY', 'Kentucky ', 'US', 15, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'LA', 'Louisiana', 'US', 16, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'ME', 'Maine', 'US', 17, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MB', 'Manitoba', 'CA', 62, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MD', 'Maryland', 'US', 18, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MA', 'Massachusetts', 'US', 19, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MI', 'Michigan', 'US', 20, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MN', 'Minnesota', 'US', 21, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MS', 'Mississippi', 'US', 22, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MO', 'Missouri ', 'US', 23, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'MT', 'Montana', 'US', 24, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NE', 'Nebraska', 'US', 25, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NV', 'Nevada', 'US', 26, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NB', 'New Brunswick', 'CA', 63, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NH', 'New Hampshire', 'US', 27, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NJ', 'New Jersey', 'US', 28, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NM', 'New Mexico', 'US', 29, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NY', 'New York', 'US', 30, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NF', 'Newfoundland', 'CA', 64, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NC', 'North Carolina', 'US', 31, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'ND', 'North Dakota', 'US', 32, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NT', 'Northwest Territories', 'CA', 65, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NS', 'Nova Scotia', 'CA', 66, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'NU', 'Nunavut', 'CA', 72, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'OH', 'Ohio', 'US', 33, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'OK', 'Oklahoma', 'US', 34, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'ON', 'Ontario', 'CA', 67, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'OR', 'Oregon', 'US', 35, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'PA', 'Pennsylvania', 'US', 36, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'PE', 'Prince Edward Is.', 'CA', 68, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'PQ', 'Quebec', 'CA', 69, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'RI', 'Rhode Island', 'US', 37, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'SK', 'Saskatchewan', 'CA', 70, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'SC', 'South Carolina', 'US', 38, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'SD', 'South Dakota', 'US', 39, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'TN', 'Tennessee', 'US', 40, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'TX', 'Texas', 'US', 41, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'UT', 'Utah', 'US', 42, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'VT', 'Vermont ', 'US', 43, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'VA', 'Virginia', 'US', 44, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'WA', 'Washington', 'US', 45, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'WV', 'West Virginia', 'US', 46, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'WI', 'Wisconsin', 'US', 47, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'WY', 'Wyoming', 'US', 48, 'Current', '2019-01-01');
INSERT INTO "commons".state (appendix_name, state_code, state_name, country_code, associated_code, status, date_effective) VALUES ('State Province', 'YT', 'Yukon Territory', 'CA', 71, 'Current', '2019-01-01');
