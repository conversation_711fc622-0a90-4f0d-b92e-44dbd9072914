INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TR', 'Türkiye', 'TRY', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AF', 'Afganistan', 'USD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('DE', 'Almanya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('US', 'AmerikaBirlesikDevletleri', 'USD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AS', 'AmerikanSamoasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AD', 'Andorra', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AO', 'Angola', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AI', 'Anguilla', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AQ', 'Antarktika', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AG', 'AntiguaveBarbuda', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AR', 'Arjantin', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AL', 'Arnavutluk', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AW', 'Aruba', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AU', 'Avustralya', 'AUD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AT', 'Avusturya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AZ', 'Azerbaycan', 'AZN', 'SPACE', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BS', 'Bahamalar', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BH', 'Bahreyn', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BD', 'Banglades', 'BDT', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BB', 'Barbados', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('EH', 'BatiSahra', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BY', 'Belarus', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BE', 'Belçika', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BZ', 'Belize', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BJ', 'Benin', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BM', 'Bermuda', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BT', 'Bhutan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AE', 'BirlesikArapEmirlikleri', 'AED', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GB', 'BirlesikKrallik', 'GBP', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BO', 'Bolivya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BA', 'BosnaHersek', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BW', 'Botsvana', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BV', 'BouvetAdasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BR', 'Brezilya', 'BRL', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IO', 'BritanyaHindOkyanusuTopraklari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VG', 'BritanyaVirginAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BN', 'Brunei', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BG', 'Bulgaristan', 'BGN', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BF', 'BurkinaFaso', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BI', 'Burundi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TD', 'Çad', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CZ', 'ÇekCumhuriyeti', 'CZK', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CN', 'Çin', 'CNY', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('DK', 'Danimarka', 'DKK', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('DJ', 'Cibuti', 'DJF', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('DM', 'Dominika', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('DO', 'DominikCumhuriyeti', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TL', 'DogुTimor', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('EC', 'Ekvador', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GQ', 'EkvatorGuinesi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SV', 'ElSalvador', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ID', 'Endonezya', 'IDR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ER', 'Eritre', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AM', 'Ermenistan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('EE', 'Estonya', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ET', 'Etiyopya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FK', 'FalklandAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FO', 'FaroeAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MA', 'Fas', 'MAD', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FJ', 'Fiji', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PH', 'Filipinler', 'PHP', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PS', 'Filistin', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FI', 'Finlandiya', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FR', 'Fransa', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GF', 'FransizGuyanasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PF', 'FransizPolinezyasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TF', 'FransizGuneyTopraklari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GA', 'Gabon', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GM', 'Gambiya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GH', 'Gana', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GN', 'Gine', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GW', 'Gine-Bissau', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GY', 'Guyana', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ZA', 'GüneyAfrika', 'ZAR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GS', 'GüneyGeorgiyaveGüneyandwichAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KR', 'GüneyKore', 'KRW', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GE', 'Gürcistan', 'GEL', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GT', 'Guatemala', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GG', 'Guernsey', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GD', 'Grenada', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GL', 'Grönland', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GP', 'Guadeloupe', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GU', 'Guam', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('HT', 'Haiti', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IN', 'Hindistan', 'INR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('HN', 'Honduras', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('HK', 'HongKong', 'HKD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('HR', 'Hirvatis', 'HRK', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NL', 'Hollanda', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('AN', 'HollandaAntilleri', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IQ', 'Irak', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IR', 'İran', 'IRR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IE', 'İrlanda', 'EUR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ES', 'İspanya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IL', 'İsrail', 'ILS', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SE', 'İsveç', 'SEK', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CH', 'İsviçre', 'CHF', 'SPACE', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IT', 'İtalya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IS', 'İzlanda', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('JM', 'Jamaika', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('JP', 'Japonya', 'JPY', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('JE', 'Jersey', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KH', 'Kamboçya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CM', 'Kamerun', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CA', 'Kanada', 'CAD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ME', 'Karadağ', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('QA', 'Katar', 'QAR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KZ', 'Kazakistan', 'KZT', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KE', 'Kenya', 'KES', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CY', 'Kibris', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KG', 'Kirgizistan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KI', 'Kiribati', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CO', 'Kolombiya', 'COP', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KM', 'Komorlar', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CG', 'Kongo', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CD', 'KongoDemokratikCumhuriyeti', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KP', 'KuzeyKore', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MP', 'KuzeyMarıanaAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CR', 'KostarikaVeAltTatiKodu', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CU', 'Küba', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KW', 'Kuveyt', 'KWD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LA', 'Laos', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LS', 'Lesotho', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LV', 'Letonya', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LR', 'Liberya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LY', 'Libya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LI', 'Lihtenstayn', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LT', 'Litvanya', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LB', 'Lübnan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LU', 'Luksemburg', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('HU', 'Macaristan', 'HUF', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MG', 'Madagaskar', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MO', 'Makao', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MK', 'Makedonya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MW', 'Malawi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MV', 'Maldivler', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MY', 'Malezya', 'MYR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ML', 'Mali', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MT', 'Malta', 'EUR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('IM', 'ManAdasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MH', 'MarşalAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MQ', 'Martinique', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MU', 'Mauritius', 'MUR', 'SPACE', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MR', 'Moritanya', 'MRU', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('YT', 'Mayotte', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('EG', 'Misir', 'EGP', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('FM', 'Mikronezya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MN', 'Moğolistan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MD', 'Moldova', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MC', 'Monako', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MS', 'Montserrat', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MZ', 'Mozambik', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MM', 'Myanmar', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NA', 'Namibya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NR', 'Nauru', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NP', 'Nepal', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NE', 'Nijer', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NG', 'Nijerya', 'NGN', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NI', 'Nikaragua', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NU', 'Niue', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NF', 'NorfolkAdasi', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NO', 'Norveç', 'NOK', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CF', 'OrtaAfrikaCumhuriyeti', 'XAF', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('UZ', 'Özbekistan', 'UZS', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PK', 'Pakistan', 'PKR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PW', 'Palau', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PA', 'Panama', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PG', 'PapuaYeniGine', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PY', 'Paraguay', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PE', 'Peru', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PN', 'PitcairnAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PL', 'Polonya', 'PLN', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PT', 'Portekiz', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PR', 'PortorikoVeNATOKodu', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('RE', 'Reunion', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('RO', 'Romanya', 'RON', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('RW', 'Ruanda', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('RU', 'Rusya', 'RUB', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('BL', 'SaintBarthelemy', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SH', 'SaintHelena', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('KN', 'SaintKittsveNevis', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LC', 'SaintLucia', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('MF', 'SaintMartin', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('PM', 'SaintPierreveMiquelon', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VC', 'SaintVincentveGrenadines', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('WS', 'Samoa', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SM', 'SanMarino', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ST', 'SaoTomevePrincipe', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SA', 'SuudiArabistan', 'SAR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SN', 'Senegal', 'XOF', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SC', 'Seyşeller', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SL', 'SierraLeone', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SG', 'Singapur', 'SGD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SK', 'Slovakya', 'EUR', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SI', 'Slovenya', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SB', 'SolomonAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SO', 'Somali', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SJ', 'SvalbardveJanMayen', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('LK', 'SriLanka', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SD', 'Sudan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SR', 'Surinam', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SY', 'Suriye', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CL', 'Şili', 'CLP', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('SZ', 'Svaziland', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('RS', 'Sırbistan', 'RSD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TJ', 'Tacikistan', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TZ', 'Tanzanya', 'TZS', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TH', 'Tayland', 'THB', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TW', 'Tayvan', 'TWD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TG', 'Togo', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TK', 'Tokelau', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TO', 'Tonga', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TT', 'TrinidadveTobago', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TN', 'Tunus', 'TND', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TC', 'TurksveKaikosAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('TV', 'Tuvalu', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('UG', 'Uganda', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('UA', 'Ukrayna', 'UAH', 'SPACE', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('OM', 'Umman', 'OMR', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('UY', 'Uruguay', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('JO', 'Ürdün', 'JOD', 'COMMA', 'DOT');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VU', 'Vanuatu', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VA', 'Vatikan', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VE', 'Venezuela', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VN', 'Vietnam', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('WF', 'WallisVeFutuna', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('YE', 'Yemen', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NC', 'YeniKaledonya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('NZ', 'YeniZelanda', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('CV', 'YeşilBurnanAdaları', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('GR', 'Yunanistan', 'EUR', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('VI', 'AmerikVirginAdalari', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ZM', 'Zambiya', 'USD', 'DOT', 'COMMA');
INSERT INTO "commons".web_currency (country_code, country_name, currency_code, digit_grouping_symbol, decimal_placer_symbol) VALUES ('ZW', 'Zimbabve', 'USD', 'DOT', 'COMMA');
