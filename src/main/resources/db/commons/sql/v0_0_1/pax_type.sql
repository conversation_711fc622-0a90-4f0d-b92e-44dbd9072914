INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('STUDENT', 'STU', 'STU', 'SD', 'ZS', 5, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('SENIOR', 'SNN', 'SNN', 'SNN', 'CD', 5, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('INFANT', 'INF', 'INF', 'IN', 'INF', 20, 0, true, true, true, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ADULT', 'ADT', 'ADT', 'ADT', 'ADT', 0, 1, true, true, null, true, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('CHILD', 'CHD', 'CHD', 'CH', 'CNN', 10, 0, true, true, null, null, true);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('DISABLED', 'DIS', 'DIS', 'IC', 'SB', 5, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('TEACHER', 'TEA', 'TEA', null, 'DT', 5, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('UNACCOMPANIED', 'UNN', 'UNN', 'UNN', 'UNN', 5, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('YOUNGADULT', 'B15', 'B15', null, 'ADT13', 5, 1, false, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('YOUTH', 'YTH', 'YTH', 'ZZ', 'YTH', 5, 1, false, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('LABOR', 'LBR', 'LBR', 'DL', 'LBR', 5, 1, false, true, null, true, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('LABOR_CHILD', 'LNN', 'LNN', 'LNN', 'LNN', 15, 0, false, true, null, null, true);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('LABOR_INFANT', 'LIF', 'LIF', 'LIF', 'LIF', 25, 0, false, true, true, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ETHNIC', 'VFR', 'VFR', 'VFR', 'VFR', 5, 1, false, true, null, true, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ETHNIC_CHILD', 'VFN', 'VFN', 'VFN', 'VFN', 15, 0, false, true, null, null, true);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ETHNIC_INFANT', 'VFF', 'VFF', 'VFF', 'VFF', 25, 0, false, true, true, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('SEAMAN', 'SEA', 'SEA', 'SC', 'SEA', 5, 1, false, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('VETERAN', 'MRE', 'MRE', 'MRE', 'MRE', 5, 1, true, true, null, true, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('MEDICAL', 'MED', 'MED', 'MED', 'MD', 5, 1, false, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('VETERAN_CHILD', 'MNN', 'MNN', 'MNN', 'MNN', 15, 0, true, true, null, null, true);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('VETERAN_INF', 'MNF', 'INF', 'MNF', 'MNF', 25, 0, true, true, true, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ATTENDANT', 'AT', null, null, 'AT', 10, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('JETYOUTHPROMO', 'AJZS00N1', null, null, 'AJZS00N1', 25, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('SOLDIER', 'MM', null, null, 'MM', 15, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('LADIES_DISCOUNT', 'KG', null, null, 'KG', 25, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('DOMESTIC_UNACCOMPANI', 'UCNN', null, null, 'UCNN', 5, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('JETYOUTH', 'JG', null, null, 'JG', 15, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('CASUALTYFAMILY', 'SA', null, null, 'SA', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('EPMCIP', 'EPMCIP', 'EPMCIP', null, 'EPMCIP', 25, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('TEKNIK', 'TEK', null, null, 'TEK', 25, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('DIPLOMAT', 'NOKDV', null, null, 'NOKDV', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('PROMOTION_CYPRUS', 'RT20', null, null, 'RT20', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('WIFE_CYPRUS_PEACE', 'SH', null, null, 'SH', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('ESCORTED_PASSANGER', 'CPE', null, null, 'CP', 15, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('SOLDIER_POLICE_OFFIC', 'PL   ', null, null, 'PL   ', 10, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('WAR_VETERAN_COMPANIO', 'MR', null, null, 'MR', 10, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('PEACEKEEPINGFORCE', 'MU', null, null, 'MU', 10, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('COMPANION', 'CMA', 'CMA', null, 'CMA', 20, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('S_COMPANION', 'CMP', 'CMP', null, 'CMP', 20, 1, true, true, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('CP', 'CP', null, null, 'CP', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('CP50', 'CP50', null, null, 'CP50', 20, 1, true, false, null, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('IT_ADULT', 'IT', 'IT', 'IT', 'ITX', 5, 1, false, true, null, true, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('IT_CHILD', 'INN', 'INN', 'INN', 'INN', 15, 1, false, true, null, null, true);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('IT_INFANT', 'ITF', 'ITF', 'ITF', 'ITF', 25, 1, false, true, true, null, null);
INSERT INTO "commons".pax_type (pax_code, ndc_type, amadeus_code, amadeus_applied_code, troya_code, rank, order_priority, is_domestic, is_international, is_infant, accompany_infant, is_child) VALUES ('EXTRASEAT', 'EXST01', 'EXST01', null, 'EXST01', 15, 1, true, true, null, null, null);
