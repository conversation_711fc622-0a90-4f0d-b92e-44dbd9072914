INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AF ', 'Afghanistan', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AL ', 'Albania', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DZ ', 'Algeria', 'DZD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AD ', 'Andorra', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AO ', 'Angola', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AI ', 'Anguilla', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AG ', 'Antigua and Barbuda', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AS ', 'American Samoa', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AR ', 'Argentina', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AM ', 'Armenia', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AW ', 'Aruba', 'AWG', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AU ', 'Australia', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AT ', 'Austria', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AZ ', 'Azerbaijan', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BS ', 'Bahamas', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BH ', 'Bahrain', 'BHD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BD ', 'Bangladesh', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BB ', 'Barbados', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BY ', 'Belarus', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BE ', 'Belgium', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BZ ', 'Belize', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BJ ', 'Benin', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BM ', 'Bermuda', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BT ', 'Bhutan', 'BTN', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BO ', 'Bolivia', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BQ ', 'Bonaire, Saint Eustatius, and Saba', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BA ', 'Bosnia and Herzegovina', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BW ', 'Botswana', 'BWP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BR ', 'Brazil', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BN ', 'Brunei Darussalam', 'BND', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BG ', 'Bulgaria', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BF ', 'Burkina Faso', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BI ', 'Burundi ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KH ', 'Cambodia', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CM ', 'Cameroon', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CA ', 'Canada', 'CAD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Canary Islands (See Spain)', '---', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CV ', 'Cape Verde ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Caroline Islands (See Micronesia)', '--', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KY ', 'Cayman Islands', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CF ', 'Central African Republic', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TD ', 'Chad', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CL ', 'Chile', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CN ', 'China ', 'CNY', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CX ', 'Christmas Island (Indian Ocean)', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CC ', 'Cocos Islands (Keeling) ', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CO ', 'Colombia  ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KM ', 'Comoros   ', 'KMF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CG ', 'Congo (Brazzaville)', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CD ', 'Congo (Kinshasa)', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CK ', 'Cook Islands  ', 'NZD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CR ', 'Costa Rica ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CI ', 'Cote d'' Ivoire (Ivory Coast) ', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HR ', 'Croatia', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CU ', 'Cuba ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CW ', 'Curaçao ', 'ANG', 'Published Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CW ', 'Curaçao ', 'XCG', 'Published Currency', 'Current', '4/1/2025');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CY ', 'Cyprus', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CZ ', 'Czech Republic  ', 'CZK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DK ', 'Denmark', 'DKK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DJ ', 'Djibouti', 'DJF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DM ', 'Dominica', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DO ', 'Dominican Republic ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EC ', 'Ecuador', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EG ', 'Egypt', 'EGP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SV ', 'El Salvador', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GQ ', 'Equatorial Guinea', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ER ', 'Eritrea ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EE ', 'Estonia', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ET ', 'Ethiopia ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FO ', 'Faroe Islands', 'DKK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FK ', 'Falkland Islands', 'GBP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FJ ', 'Fiji ', 'FJD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FI ', 'Finland', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FR ', 'France', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GF ', 'French Guiana', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PF ', 'French Polynesia ', 'XPF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GA ', 'Gabon', 'XAF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GM ', 'Gambia', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GE ', 'Georgia ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DE ', 'Germany ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GH ', 'Ghana ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GI ', 'Gibraltar', 'GIP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GR ', 'Greece', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GL ', 'Greenland ', 'DKK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GD ', 'Grenada ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GP ', 'Guadeloupe (Inc St.Barthelemy & Northern St Martin)', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GU ', 'Guam ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GT ', 'Guatemala ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GN ', 'Guinea ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GW ', 'Guinea Bissau ', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GY ', 'Guyana', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HT ', 'Haiti', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HN ', 'Honduras', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HK ', 'Hong Kong, SAR, China', 'HKD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HU ', 'Hungary', 'HUF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IS ', 'Iceland', 'ISK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IN ', 'India (Includes Andaman Islands)', 'INR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ID ', 'Indonesia', 'IDR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IR ', 'Iran, Islamic Republic of', 'IRR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IQ ', 'Iraq', 'IQD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IE ', 'Ireland ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IL ', 'Israel', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IT ', 'Italy', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JM ', 'Jamaica', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JP ', 'Japan', 'JPY', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Johnston Island (See US Minor Outlying Islands)', '---', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JO ', 'Jordan', 'JOD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KZ ', 'Kazakhstan', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KE ', 'Kenya ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KI ', 'Kiribati (Includes Canton & Enderbury Islands)', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KP ', 'Korea, Democratic People''s Republic of', 'KPW', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KR ', 'Korea, Republic of', 'KRW', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KW ', 'Kuwait', 'KWD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KG ', 'Kyrgyzstan', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LA ', 'Laos, Peoples Democratic Republic of ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LV ', 'Latvia ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LB ', 'Lebanon ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LS ', 'Lesotho ', 'LSL', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LR ', 'Liberia ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LY ', 'Libyan Arab Jamahiriya ', 'LYD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LI ', 'Liechtenstein,', 'CHF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LT ', 'Lithuania  ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LU ', 'Luxembourg', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MO ', 'Macau SAR ', 'MOP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MG ', 'Madagascar', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MW ', 'Malawi', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MY ', 'Malaysia', 'MYR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MV ', 'Maldives', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ML ', 'Mali', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MT ', 'Malta ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MP ', 'Mariana Islands (Including Guam)', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MH ', 'Marshall Islands', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MQ ', 'Martinique', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MR ', 'Mauritania ', 'MRU', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MU ', 'Mauritius ', 'MUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'YT ', 'Mayotte', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MX ', 'Mexico', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FM ', 'Micronesia/Caroline Islands', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Midway Islands (See US Minor Outlying Islands)', '---', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MD ', 'Moldova, Republic of', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MC ', 'Monaco', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MN ', 'Mongolia ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MS ', 'Montserrat ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ME ', 'Montenegro', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MA ', 'Morocco', 'MAD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MZ ', 'Mozambique ', 'MZN', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MM ', 'Myanmar', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NA ', 'Namibia ', 'NAD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NR ', 'Nauru ', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NP ', 'Nepal ', 'NPR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NL ', 'Netherlands', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NC ', 'New Caledonia (Includes Loyalty Islands) ', 'XPF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NZ ', 'New Zealand', 'NZD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NI ', 'Nicaragua', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NE ', 'Niger ', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NG ', 'Nigeria ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NU ', 'Niue ', 'NZD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NF ', 'Norfolk Island ', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MK ', 'North Macedonia, Republic of ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NO ', 'Norway', 'NOK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PS ', 'Occupied Palestinian Territory', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'OM ', 'Oman', 'OMR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PK ', 'Pakistan', 'PKR', 'Published Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PK ', 'Pakistan', 'USD', 'Published Currency', 'Current', '8/12/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PW ', 'Palau ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PA ', 'Panama   ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PG ', 'Papua New Guinea (Includes Niugini)', 'PGK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PY ', 'Paraguay ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PE ', 'Peru', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PH ', 'Philippines', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PL ', 'Poland', 'PLN', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PT ', 'Portugal (Includes Azores and Madeira)', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PR ', 'Puerto Rico (ISO code for tax applications)', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'QA ', 'Qatar', 'QAR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RE ', 'Reunion ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RO ', 'Romania', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RU ', 'Russia ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'XU ', 'Russia (East of the Urals)', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RW ', 'Rwanda', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'WS ', 'Samoa  ', 'WST', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SM ', 'San Marino   ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ST ', 'Sao Tome & Principe   ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SA ', 'Saudi Arabia ', 'SAR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SN ', 'Senegal ', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RS ', 'Serbia', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SC ', 'Seychelles ', 'SCR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SL ', 'Sierra Leone', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SG ', 'Singapore ', 'SGD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SX ', 'Sint Maarten ', 'ANG', 'Published Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SX ', 'Sint Maarten ', 'XCG', 'Published Currency', 'Current', '4/1/2025');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SK ', 'Slovakia  ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SI ', 'Slovenia  ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SB ', 'Solomon Islands  ', 'SBD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SO ', 'Somalia  ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZA ', 'South Africa ', 'ZAR', 'Published Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZA ', 'South Africa ', 'USD', 'Published Currency', 'Current', '10/7/2024');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SS ', 'South Sudan', 'SSP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ES ', 'Spain & Canary Islands', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LK ', 'Sri Lanka', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SH ', 'St. Helena ', 'SHP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KN ', 'St. Kitts - Nevis ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LC ', 'St. Lucia   ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PM ', 'St. Pierre & Miquelon  ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VC ', 'St. Vincent & The Grenadines', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SD ', 'Sudan', 'SDG', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SR ', 'Surinam', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SZ ', 'Swaziland ', 'SZL', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SE ', 'Sweden ', 'SEK', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CH ', 'Switzerland   ', 'CHF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SY ', 'Syrian Arab Republic  ', 'SYP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TW ', 'Taiwan, Province of ', 'TWD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TJ ', 'Tajikistan ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TZ ', 'Tanzania, United Republic of ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TH ', 'Thailand  ', 'THB', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TL ', 'Timor Leste', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TG ', 'Togo ', 'XOF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TO ', 'Tonga  ', 'TOP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TT ', 'Trinidad & Tobago ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TN ', 'Tunisia ', 'TND', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TR ', 'Turkey ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TM ', 'Turkmenistan  ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TC ', 'Turks & Caicos Islands ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TV ', 'Tuvalu  ', 'AUD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UG ', 'Uganda ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UA ', 'Ukraine ', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AE ', 'United Arab Emirates ', 'AED', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GB ', 'United Kingdom (Great Britain)', 'GBP', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UM ', 'United States Minor Outlying Islands', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'US ', 'The United States', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Upper Volta (see Burkina Faso)', '---', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UY ', 'Uruguay', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VI ', 'US Virgin Islands (ISO code for tax applications)', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UZ ', 'Uzbekistan ', 'EUR', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VU ', 'Vanuatu', 'VUV', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VE ', 'Venezuela', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VN ', 'Vietnam', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VG ', 'Virgin Islands, British', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VI ', 'Virgin Islands, US (ISO code for tax applications)', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Wake Island (See US Minor Outlying Islands)', '---', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'WF ', 'Wallis & Futuna Islands', 'XPF', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'N/A', 'Worldwide (all countries)', 'XXX (signifying miles)', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'YE ', 'Yemen, Republic of', 'YER', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZM ', 'Zambia', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZW ', 'Zimbabwe', 'USD', 'Published Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AF ', 'Afghanistan', 'AFN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AL ', 'Albania', 'ALL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DZ ', 'Algeria', 'DZD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AD ', 'Andorra', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AO ', 'Angola', 'AOA', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AI ', 'Anguilla', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AG ', 'Antigua and Barbuda', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AS ', 'American Samoa', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AR ', 'Argentina', 'ARS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AM ', 'Armenia', 'AMD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AW ', 'Aruba', 'AWG', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AU ', 'Australia', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AT ', 'Austria', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AZ ', 'Azerbaijan', 'AZN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BS ', 'Bahamas', 'BSD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BH ', 'Bahrain', 'BHD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BD ', 'Bangladesh', 'BDT', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BB ', 'Barbados', 'BBD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BY ', 'Belarus', 'BYN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BE ', 'Belgium', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BZ ', 'Belize', 'BZD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BJ ', 'Benin', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BM ', 'Bermuda', 'BMD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BT ', 'Bhutan', 'BTN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BO ', 'Bolivia', 'BOB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BQ ', 'Bonaire, Saint Eustatius, and Saba', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BA ', 'Bosnia and Herzegovina', 'BAM', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BW ', 'Botswana', 'BWP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BR ', 'Brazil', 'BRL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BN ', 'Brunei Darussalam', 'BND', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BG ', 'Bulgaria', 'BGN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BF ', 'Burkina Faso', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'BI ', 'Burundi ', 'BIF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KH ', 'Cambodia', 'KHR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CM ', 'Cameroon', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CA ', 'Canada', 'CAD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Canary Islands (See Spain)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CV ', 'Cape Verde ', 'CVE', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Caroline Islands (See Micronesia)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KY ', 'Cayman Islands', 'KYD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CF ', 'Central African Republic', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TD ', 'Chad', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CL ', 'Chile', 'CLP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CN ', 'China ', 'CNY', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CX ', 'Christmas Island (Indian Ocean)', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CC ', 'Cocos Islands (Keeling) ', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CO ', 'Colombia  ', 'COP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KM ', 'Comoros   ', 'KMF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CG ', 'Congo (Brazzaville)', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CD ', 'Congo (Kinshasa)', 'CDF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CK ', 'Cook Islands  ', 'NZD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CR ', 'Costa Rica ', 'CRC', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CI ', 'Cote d'' Ivoire (Ivory Coast) ', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HR ', 'Croatia', 'HRK', 'Local Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HR ', 'Croatia', 'EUR', 'Local Currency', 'Current', '1/1/2023');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CU ', 'Cuba ', 'CUP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CW ', 'Curaçao ', 'ANG', 'Local Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CW ', 'Curaçao ', 'XCG', 'Local Currency', 'Current', '4/1/2025');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CY ', 'Cyprus', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CZ ', 'Czech Republic  ', 'CZK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DK ', 'Denmark', 'DKK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DJ ', 'Djibouti', 'DJF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DM ', 'Dominica', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DO ', 'Dominican Republic ', 'DOP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EC ', 'Ecuador', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EG ', 'Egypt', 'EGP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SV ', 'El Salvador', 'SVC', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GQ ', 'Equatorial Guinea', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ER ', 'Eritrea ', 'ERN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'EE ', 'Estonia', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ET ', 'Ethiopia ', 'ETB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FO ', 'Faroe Islands', 'DKK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FK ', 'Falkland Islands', 'FKP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FJ ', 'Fiji ', 'FJD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FI ', 'Finland', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FR ', 'France', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GF ', 'French Guiana', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PF ', 'French Polynesia ', 'XPF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GA ', 'Gabon', 'XAF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GM ', 'Gambia', 'GMD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GE ', 'Georgia ', 'GEL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'DE ', 'Germany ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GH ', 'Ghana ', 'GHS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GI ', 'Gibraltar', 'GIP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GR ', 'Greece', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GL ', 'Greenland ', 'DKK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GD ', 'Grenada ', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GP ', 'Guadeloupe (Inc St.Barthelemy & Northern St Martin)', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GU ', 'Guam ', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GT ', 'Guatemala ', 'GTQ', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GN ', 'Guinea ', 'GNF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GW ', 'Guinea Bissau ', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GY ', 'Guyana', 'GYD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HT ', 'Haiti', 'HTG', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HN ', 'Honduras', 'HNL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HK ', 'Hong Kong, SAR, China', 'HKD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'HU ', 'Hungary', 'HUF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IS ', 'Iceland', 'ISK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IN ', 'India (Includes Andaman Islands)', 'INR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ID ', 'Indonesia', 'IDR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IR ', 'Iran, Islamic Republic of', 'IRR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IQ ', 'Iraq', 'IQD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IE ', 'Ireland ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IL ', 'Israel', 'ILS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'IT ', 'Italy', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JM ', 'Jamaica', 'JMD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JP ', 'Japan', 'JPY', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Johnston Island (See US Minor Outlying Islands)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'JO ', 'Jordan', 'JOD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KZ ', 'Kazakhstan', 'KZT', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KE ', 'Kenya ', 'KES', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KI ', 'Kiribati (Includes Canton & Enderbury Islands)', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KP ', 'Korea, Democratic People''s Republic of', 'KPW', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KR ', 'Korea, Republic of', 'KRW', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KW ', 'Kuwait', 'KWD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KG ', 'Kyrgyzstan', 'KGS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LA ', 'Laos, Peoples Democratic Republic of ', 'LAK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LV ', 'Latvia ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LB ', 'Lebanon ', 'LBP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LS ', 'Lesotho ', 'LSL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LR ', 'Liberia ', 'LRD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LY ', 'Libyan Arab Jamahiriya ', 'LYD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LI ', 'Liechtenstein,', 'CHF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LT ', 'Lithuania  ', 'LTL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LU ', 'Luxembourg', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MO ', 'Macau SAR ', 'MOP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MG ', 'Madagascar', 'MGA', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MW ', 'Malawi', 'MWK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MY ', 'Malaysia', 'MYR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MV ', 'Maldives', 'MVR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ML ', 'Mali', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MT ', 'Malta ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MP ', 'Mariana Islands (Including Guam)', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MH ', 'Marshall Islands', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MQ ', 'Martinique', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MR ', 'Mauritania ', 'MRU', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MU ', 'Mauritius ', 'MUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'YT ', 'Mayotte', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MX ', 'Mexico', 'MXN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'FM ', 'Micronesia/Caroline Islands', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Midway Islands (See US Minor Outlying Islands)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MD ', 'Moldova, Republic of', 'MDL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MC ', 'Monaco', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MN ', 'Mongolia ', 'MNT', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MS ', 'Montserrat ', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ME ', 'Montenegro', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MA ', 'Morocco', 'MAD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MZ ', 'Mozambique ', 'MZN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MM ', 'Myanmar', 'MMK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NA ', 'Namibia ', 'NAD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NR ', 'Nauru ', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NP ', 'Nepal ', 'NPR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NL ', 'Netherlands', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NC ', 'New Caledonia (Includes Loyalty Islands) ', 'XPF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NZ ', 'New Zealand', 'NZD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NI ', 'Nicaragua', 'NIO', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NE ', 'Niger ', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NG ', 'Nigeria ', 'NGN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NU ', 'Niue ', 'NZD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NF ', 'Norfolk Island ', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'MK ', 'North Macedonia, Republic of ', 'MKD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'NO ', 'Norway', 'NOK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PS ', 'Occupied Palestinian Territory', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'OM ', 'Oman', 'OMR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PK ', 'Pakistan', 'PKR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PW ', 'Palau ', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PA ', 'Panama   ', 'PAB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PG ', 'Papua New Guinea (Includes Niugini)', 'PGK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PY ', 'Paraguay ', 'PYG', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PE ', 'Peru', 'PEN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PH ', 'Philippines', 'PHP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PL ', 'Poland', 'PLN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PT ', 'Portugal (Includes Azores and Madeira)', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PR ', 'Puerto Rico (ISO code for tax applications)', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'QA ', 'Qatar', 'QAR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RE ', 'Reunion ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RO ', 'Romania', 'RON', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RU ', 'Russia ', 'RUB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'XU ', 'Russia (East of the Urals)', 'RUB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RW ', 'Rwanda', 'RWF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'WS ', 'Samoa  ', 'WST', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SM ', 'San Marino   ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ST ', 'Sao Tome & Principe   ', 'STN', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SA ', 'Saudi Arabia ', 'SAR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SN ', 'Senegal ', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'RS ', 'Serbia', 'RSD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SC ', 'Seychelles ', 'SCR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SL ', 'Sierra Leone', 'SLL', 'Local Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SL ', 'Sierra Leone', 'SLE', 'Local Currency', 'Current', '9/30/2022');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SG ', 'Singapore ', 'SGD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SX ', 'Sint Maarten ', 'ANG', 'Local Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SX ', 'Sint Maarten ', 'XCG', 'Local Currency', 'Current', '4/1/2025');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SK ', 'Slovakia  ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SI ', 'Slovenia  ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SB ', 'Solomon Islands  ', 'SBD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SO ', 'Somalia  ', 'SOS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZA ', 'South Africa ', 'ZAR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SS ', 'South Sudan', 'SSP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ES ', 'Spain & Canary Islands', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LK ', 'Sri Lanka', 'LKR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SH ', 'St. Helena ', 'SHP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'KN ', 'St. Kitts - Nevis ', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'LC ', 'St. Lucia   ', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'PM ', 'St. Pierre & Miquelon  ', 'EUR', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VC ', 'St. Vincent & The Grenadines', 'XCD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SD ', 'Sudan', 'SDG', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SR ', 'Surinam', 'SRD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SZ ', 'Swaziland ', 'SZL', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SE ', 'Sweden ', 'SEK', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'CH ', 'Switzerland   ', 'CHF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'SY ', 'Syrian Arab Republic  ', 'SYP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TW ', 'Taiwan, Province of ', 'TWD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TJ ', 'Tajikistan ', 'TJS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TZ ', 'Tanzania, United Republic of ', 'TZS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TH ', 'Thailand  ', 'THB', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TL ', 'Timor Leste', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TG ', 'Togo ', 'XOF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TO ', 'Tonga  ', 'TOP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TT ', 'Trinidad & Tobago ', 'TTD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TN ', 'Tunisia ', 'TND', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TR ', 'Turkey ', 'TRY', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TM ', 'Turkmenistan  ', 'TMT', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TC ', 'Turks & Caicos Islands ', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'TV ', 'Tuvalu  ', 'AUD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UG ', 'Uganda ', 'UGX', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UA ', 'Ukraine ', 'UAH', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'AE ', 'United Arab Emirates ', 'AED', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'GB ', 'United Kingdom (Great Britain)', 'GBP', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UM ', 'United States Minor Outlying Islands', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'US ', 'The United States', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Upper Volta (see Burkina Faso)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UY ', 'Uruguay', 'UYU', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VI ', 'US Virgin Islands (ISO code for tax applications)', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'UZ ', 'Uzbekistan ', 'UZS', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VU ', 'Vanuatu', 'VUV', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VE ', 'Venezuela', 'VES', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VN ', 'Vietnam', 'VND', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VG ', 'Virgin Islands, British', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'VI ', 'Virgin Islands, US (ISO code for tax applications)', 'USD', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', '-- ', 'Wake Island (See US Minor Outlying Islands)', '--', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'WF ', 'Wallis & Futuna Islands', 'XPF', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'N/A', 'Worldwide (all countries)', 'N/A', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'YE ', 'Yemen, Republic of', 'YER', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZM ', 'Zambia', 'ZMW', 'Local Currency', 'Current', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZW ', 'Zimbabwe', 'ZWR', 'Local Currency', 'Historical', '1/1/2019');
INSERT INTO "commons".country_currency_mapping (appendix_name, country_code, country_description, currency_code, currency_qualifier, status, date_effective) VALUES ('Country Currency', 'ZW ', 'Zimbabwe', 'ZWG', 'Local Currency', 'Current', '11/4/2024');
