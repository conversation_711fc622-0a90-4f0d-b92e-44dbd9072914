INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('DO', 'DOMİNİK CUMHURİYETİ', 'DOM', '214', 1809, 'DOP', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('JM', 'JAMAİKA', 'JAM', '388', 1, 'JMD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GU', 'GUAM', 'GUM', '316', 1671, 'USD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PY', 'PARAGUAY', 'PRY', '600', 595, 'PYG', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BS', 'BAHAMALAR', 'BHS', '44 ', 1242, 'BSD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BB', 'BARBADOS', 'BRB', '52 ', 1246, 'BBD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('VI', 'VIRGIN ADASI', 'VIR', '850', 1, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CW', 'CURACAO', 'CUW', '531', 599, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SX', 'SINT MAARTEN', 'SXM', '534', 1, 'AND', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BM', 'BERMUDA', 'BMU', '60 ', 1, 'BMD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GB', 'İNGİLTERE', 'GBR', '826', 44, 'GBP', 'GBP', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IE', 'İRLANDA CUMHURİYETİ', 'IRL', '372', 353, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ME', 'KARADAĞ', 'MNE', '499', 382, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PT', 'PORTEKİZ', 'PRT', '620', 351, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('RU', 'RUSYA', 'RUS', '643', 7, 'RUB', 'RUB', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('US', 'AMERİKA BİRLEŞİK DEVLETLERİ', 'USA', '840', 1, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IS', 'İZLANDA', 'ISL', '352', 354, 'ISK', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MH', 'MARŞAL ADALARI', 'MHL', '584', 692, 'USD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('FM', 'MİKRONEZYA', 'FSM', '583', 691, 'USD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NI', 'NİKARAGUA', 'NIC', '558', 505, 'NIO', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PW', 'PALAU', 'PLW', '585', 680, 'USD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BZ', 'BELİZE', 'BLZ', '84 ', 501, 'BZD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BO', 'BOLİVYA', 'BOL', '68 ', 591, 'BOB', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BW', 'BOTSWANA ', 'BWA', '72 ', 267, 'BWP', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('EH', 'BATI SAHRA', 'ESH', '732', 212, 'MRO', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NC', 'NEW CALEDONIA', 'NCL', '540', 687, 'XPF', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ST', 'SAO TOME & PRINCIPE', 'STP', '678', 239, 'USD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SZ', 'Eswatini', 'SWZ', '748', 268, 'USD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GM', 'GAMBİYA', 'GMB', '270', 220, 'GMD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MP', 'MARIANA ISLANDS ', 'MNP', '580', 1670, 'USD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SV', 'EL SALVADOR', 'SLV', '222', 503, 'EUR', 'EUR', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GT', 'GUATEMALA', 'GTM', '320', 502, 'EUR', 'EUR', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('VU', 'VANUATU', 'LOD', '548', 678, 'VUV', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CU', 'KÜBA', 'CUB', '192', 53, 'CUP', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('VE', 'VENEZUELA', 'VEN', '862', 58, 'VEF', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LA', 'LAOS', 'LAO', '418', 856, 'LAK', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MM', 'MYANMAR', 'MMR', '104', 95, 'MMK', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('UY', 'URUGUAY', 'URY', '858', 598, 'UYU', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CL', 'CHILE', 'CHL', '152', 56, 'CLP', 'CLP', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CR', 'COSTA RICA', 'CRI', '188', 506, 'CRC', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('EC', 'ECUADOR', 'ECU', '218', 593, 'ECS', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NA', 'NAMIBIA', 'NAM', '516', 264, 'NAD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AG', 'ANTİGUA VE BARBUDA', 'ATG', '28 ', 1268, 'XCD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AW', 'ARUBA', 'ABW', '533', 297, 'AWG', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('HN', 'HONDURAS', 'HND', '340', 504, 'EUR', 'EUR', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MX', 'MEKSİKA', 'MEX', '484', 52, 'MXN', 'MXN', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PE', 'PERU', 'PER', '604', 51, 'PEN', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GY', 'GUYANA', 'GUY', '328', 592, 'GYD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AE', 'BİRLEŞİK ARAP EMİRLİKLERİ', 'ARE', '784', 971, 'AED', 'AED', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AF', 'AFGANİSTAN', 'AFG', '004', 93, 'AFN', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AL', 'ARNAVUTLUK', 'ALB', '008', 355, 'ALL', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AO', 'ANGOLA', 'AGO', '024', 244, 'AOA', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AR', 'ARJANTİN', 'ARG', '032', 54, 'ARS', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AT', 'AVUSTURYA', 'AUT', '040', 43, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AU', 'AVUSTRALYA', 'AUS', '036', 61, 'AUD', 'AUD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('AZ', 'AZERBAYCAN', 'AZE', '031', 994, 'AZN', 'AZN', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BA', 'BOSNA', 'BIH', '070', 387, 'BAM', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BD', 'BANGLADEŞ', 'BGD', '050', 880, 'BDT', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BE', 'BELÇİKA', 'BEL', '056', 32, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BF', 'BURKİNA FASO', 'BFA', '854', 226, 'XOF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BG', 'BULGARİSTAN', 'BGR', '100', 359, 'BGN', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BH', 'BAHREYN', 'BHR', '048', 973, 'BHD', 'BHD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BJ', 'BENİN', 'BEN', '204', 229, 'XOF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BN', 'BRUNEİ', 'BRN', '096', 673, 'BND', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BR', 'BREZİLYA', 'BRA', '076', 55, 'BRL', 'BRL', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BY', 'BELARUS (BEYAZ RUSYA)', 'BLR', '112', 375, 'BYN', 'USD', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CA', 'KANADA', 'CAN', '124', 1, 'CAD', 'CAD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CD', 'DEMOKRATİK KONGO CUMHURİYETİ', 'COD', '180', 243, 'CDF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CG', 'KONGO CUMHURİYETİ', 'COG', '178', 242, 'XAF', 'XAF', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CH', 'İSVİÇRE', 'CHE', '756', 41, 'CHF', 'CHF', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CI', 'FİLDİŞİ SAHİLİ', 'CIV', '384', 225, 'XOF', 'EUR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CM', 'KAMERUN', 'CMR', '120', 237, 'XAF', 'XAF', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CN', 'ÇİN', 'CHN', '156', 86, 'CNY', 'CNY', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CO', 'KOLOMBİYA ', 'COL', '170', 57, 'COP', 'COP', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CV', 'KAP VERDE', 'CPV', '132', 238, 'CVE', 'EUR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CY', 'Kuzey Kıbrıs Türk Cumhuriyeti', 'TUR', '792', 90, 'TRY', 'TRY', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CZ', 'ÇEKYA', 'CZE', '203', 420, 'CZK', 'CZK', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('DE', 'ALMANYA', 'DEU', '276', 49, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('DJ', 'CİBUTİ', 'DJI', '262', 253, 'DJF', 'DJF', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('DK', 'DANİMARKA', 'DNK', '208', 45, 'DKK', 'DKK', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('DZ', 'CEZAYİR', 'DZA', '012', 213, 'DZD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('EE', 'ESTONYA', 'EST', '233', 372, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('EG', 'MISIR', 'EGY', '818', 20, 'EGP', 'EGP', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ER', 'ERİTRE', 'ERI', '232', 291, 'ERN', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ES', 'İSPANYA', 'ESP', '724', 34, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ET', 'ETİYOPYA', 'ETH', '231', 251, 'ETB', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('FI', 'FİNLANDİYA', 'FIN', '246', 358, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('FR', 'FRANSA', 'FRA', '250', 33, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GA', 'GABON', 'GAB', '266', 241, 'XAF', 'XAF', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GE', 'GÜRCİSTAN', 'GEO', '268', 995, 'GEL', 'GEL', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GH', 'GANA', 'GHA', '288', 233, 'GHS', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GN', 'GİNE', 'GIN', '324', 224, 'GNF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GQ', 'EKVATOR GİNESİ', 'GNQ', '226', 240, 'GNF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GR', 'YUNANİSTAN', 'GRC', '300', 30, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('HK', 'HONG KONG', 'HKG', '344', 852, 'HKD', 'HKD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('HR', 'HIRVATİSTAN', 'HRV', '191', 385, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('HU', 'MACARİSTAN', 'HUN', '348', 36, 'HUF', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ID', 'ENDONEZYA', 'IDN', '360', 62, 'IDR', 'IDR', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IL', 'İSRAİL', 'ISR', '376', 972, 'ILS', 'ILS', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IN', 'HİNDİSTAN', 'IND', '356', 91, 'INR', 'INR', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IQ', 'IRAK', 'IRQ', '368', 964, 'IQD', 'USD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IR', 'İRAN', 'IRN', '364', 98, 'IRR', 'USD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('IT', 'İTALYA', 'ITA', '380', 39, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('JO', 'ÜRDÜN', 'JOR', '400', 962, 'JOD', 'JOD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('JP', 'JAPONYA', 'JPN', '392', 81, 'JPY', 'JPY', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KE', 'KENYA', 'KEN', '404', 254, 'KES', 'KES', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KG', 'KIRGIZİSTAN', 'KGZ', '417', 996, 'KGS', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('XK', 'KOSOVA CUMHURİYETİ', 'XKX', '688', 383, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KR', 'KORE', 'KOR', '410', 82, 'KRW', 'KRW', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KW', 'KUVEYT', 'KWT', '414', 965, 'KWD', 'KWD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KZ', 'KAZAKİSTAN', 'KAZ', '398', 7, 'KZT', 'KZT', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LB', 'LÜBNAN', 'LBN', '422', 961, 'LBP', 'USD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LK', 'SRİ LANKA', 'LKA', '144', 94, 'LKR', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LT', 'LİTVANYA', 'LTU', '440', 370, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LU', 'LÜKSEMBURG', 'LUX', '442', 352, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LV', 'LETONYA', 'LVA', '428', 371, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LY', 'LiBYA', 'LBY', '434', 218, 'LYD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MA', 'FAS', 'MAR', '504', 212, 'MAD', 'MAD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MC', 'MONAKO', 'MCO', '492', 377, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MD', 'MOLDOVA', 'MDA', '498', 373, 'MDL', 'USD', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MG', 'MADAGASKAR', 'MDG', '450', 261, 'MGA', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MK', 'MAKEDONYA', 'MKD', '807', 389, 'MKD', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ML', 'MALİ', 'MLI', '466', 223, 'XOF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MN', 'MOĞOLİSTAN', 'MNG', '496', 976, 'MNT', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MR', 'MORİTANYA', 'MRT', '478', 222, 'MRU', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MT', 'MALTA', 'MLT', '470', 356, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MU', 'MAURITIUS', 'MUS', '480', 230, 'MUR', 'MUR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MV', 'MALDİVLER', 'MDV', '462', 960, 'MVR', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MY', 'MALEZYA', 'MYS', '458', 60, 'MYR', 'MYR', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MZ', 'MOZAMBİK', 'MPM', '508', 258, 'MZN', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NE', 'NİJER', 'NER', '562', 227, 'XOF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NG', 'NİJERYA', 'NGA', '566', 234, 'NGN', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NL', 'HOLLANDA', 'NLD', '528', 31, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NO', 'NORVEÇ', 'NOR', '578', 47, 'NOK', 'NOK', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NP', 'NEPAL', 'NPL', '524', 977, 'NPR', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('NZ', 'YENİ ZELANDA', 'NZL', '554', 64, 'NZD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('OM', 'OMAN', 'OMN', '512', 968, 'OMR', 'OMR', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PH', 'FİLİPİNLER', 'PHL', '608', 63, 'PHP', 'PHP', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PK', 'PAKİSTAN', 'PAK', '586', 92, 'PKR', 'PKR', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PL', 'POLONYA', 'POL', '616', 48, 'PLN', 'PLN', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PR', 'PORTO RİKO', 'PRI', '630', 1787, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('QA', 'KATAR', 'QAT', '634', 974, 'QAR', 'QAR', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('RO', 'ROMANYA', 'ROU', '642', 40, 'RON', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('RS', 'SIRBİSTAN', 'SRB', '688', 381, 'RSD', 'RSD', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('RW', 'RUANDA', 'RWA', '646', 250, 'RWF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SA', 'SUUDİ ARABİSTAN', 'SAU', '682', 966, 'SAR', 'SAR', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SD', 'SUDAN', 'SDN', '736', 249, 'SDG', 'EUR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SE', 'İSVEÇ', 'SWE', '752', 46, 'SEK', 'SEK', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SG', 'SİNGAPUR', 'SGP', '702', 65, 'SGD', 'SGD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SI', 'SLOVENYA', 'SVN', '705', 386, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SN', 'SENEGAL', 'SEN', '686', 221, 'XOF', 'XOF', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SO', 'SOMALİ', 'SOM', '706', 252, 'SOS', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SY', 'SURİYE', 'SYR', '760', 963, 'SYP', 'USD', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TD', 'ÇAD', 'TCD', '148', 235, 'XAF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TG', 'TOGO', 'TCD', '768', 228, 'XAF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TH', 'TAYLAND', 'THA', '764', 66, 'THB', 'THB', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TJ', 'TACİKİSTAN', 'TJK', '762', 992, 'TJS', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TM', 'TÜRKMENİSTAN', 'TKM', '795', 993, 'TMT', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TN', 'TUNUS', 'TUN', '788', 216, 'TND', 'TND', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TR', 'TÜRKİYE', 'TUR', '792', 90, 'TRY', 'TRY', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TW', 'TAIWAN', 'TWN', '158', 886, 'TWD', 'TWD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TZ', 'TANZANYA', 'TZA', '834', 255, 'TZS', 'TZS', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('UA', 'UKRAYNA', 'UKR', '804', 380, 'UAH', 'USD', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('UG', 'UGANDA', 'UGA', '800', 256, 'UGX', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('UZ', 'ÖZBEKİSTAN', 'UZB', '860', 998, 'UZS', 'UZS', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('VN', 'VİETNAM', 'VNM', '704', 84, 'VND', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('YE', 'YEMEN CUMHURİYETİ', 'YEM', '887', 967, 'YER', 'EUR', 'ME');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ZA', 'GÜNEY AFRİKA CUMHURİYETİ', 'ZAF', '710', 27, 'ZAR', 'ZAR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ZM', 'ZAMBİYA', 'ZMB', '894', 260, 'ZMW', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SK', 'SLOVAKYA', 'SVK', '703', 421, 'EUR', 'EUR', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SC', 'SEYŞELLER', 'SYC', '690', 248, 'SYC', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('FJ', 'FİJİ', 'FJI', '242', 679, '-  ', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PA', 'PANAMA', 'PAN', '591', 507, 'USD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('ZW', 'ZİMBABVE', 'ZWE', '716', 263, 'USD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MW', 'MALAVİ', 'MWI', '454', 265, 'MWK', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KM', 'KOMORLAR', 'COM', '174', 269, 'KMF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CK', 'COOK ADALARI', 'COK', '184', 682, 'NZD', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MO', 'MACAU', 'MAC', '446', 853, 'MOP', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('CF', 'CENTRAL AFRICAN REP', 'CAF', '140', 236, 'XAF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GW', 'GUINEA BISSAU ', 'GNB', '624', 245, 'XOF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BI', 'BURUNDI', 'BDI', '108', 257, 'BIF', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SS', 'GÜNEY SUDAN', 'SSD', '728', 211, 'SSP', 'EUR', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('FO', 'FAROE ADALARI', 'FRO', '234', 298, 'DKK', 'DKK', 'EU');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BQ', 'BONAIRE', 'BES', '535', 599, 'USD', 'EUR', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MF', 'SAINT MARTIN', 'MAF', '663', 590, 'EUR', 'EUR', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LS', 'LESOTHO', 'LSO', '426', 266, 'USD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SH', 'SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA', 'SHN', '654', 290, 'USD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('PG', 'Papua New Guinea', 'PNG', '598', 675, 'PGK', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('BT', 'BHUTAN', 'BTN', '064', 975, 'INR', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('VC', 'Saint Vincent ve Grenadinler', 'VCT', '670', 1784, 'XCD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GL', 'GRÖNLAND', 'GRL', '304', 299, 'DKK', 'DKK', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SR', 'SURINAME', 'SUR', '740', 597, 'SRD', 'USD', 'SA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KH', 'KAMBOÇYA', 'KHM', '116', 855, 'KHR', 'USD', 'AP');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GD', 'GRENADA', 'GRD', '308', 1473, 'XCD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('HT', 'HAİTİ', 'HTI', '332', 509, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LC', 'SAİNT LUCİA', 'LCA', '662', 1758, 'XCD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TT', 'TRİNİDAD VE TOBAGO', 'TTO', '780', 1868, 'TTD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('TC', 'TURKS CAICOS ADALARI', 'TCA', '796', 1649, 'USD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('KY', 'CAYMAN ADASI', 'CYM', '136', 1345, 'KYD', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('MQ', 'MARTİNİK', 'MTQ', '474', 596, 'EUR', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('GP', 'GUADÖLUP', 'GLP', '312', 590, 'EUR', 'USD', 'NA');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('LR', 'LİBERYA', 'LBR', '430', 231, 'LRD', 'USD', 'AF');
INSERT INTO "commons".country (country_code, country_name, c3_code, numeric_code, phone_code, currency_code, secondary_currency_code, flight_map_region_code) VALUES ('SL', 'SİERRA LEONE', 'SLE', '694', 232, 'SLL', 'USD', 'AF');
