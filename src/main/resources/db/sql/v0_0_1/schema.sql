
-- changeset berkeucvet:2025-04-22-10-00
CREATE TYPE field_type AS ENUM ('INTEGER','TEXT', 'BOOLEAN', 'LIST', 'DATETIME');
CREATE TYPE criteria_type AS ENUM ('USER_DEFINED', 'SYSTEM_DEFINED');
CREATE TYPE selection_type AS ENUM ('SINGLE_SELECT', 'MULTI_SELECT');
CREATE TYPE request_type AS ENUM ('OFFER_REQUEST');

CREATE TABLE criteria (
                          id UUID PRIMARY KEY NOT NULL,
                          name VARCHAR(255) NOT NULL UNIQUE,
                          description VARCHAR(255) NOT NULL,
                          type CRITERIA_TYPE NOT NULL,
                          request_type REQUEST_TYPE NOT NULL,
                          mapping_field VARCHAR(255) NOT NULL,
                          field_type FIELD_TYPE NOT NULL,
                          selection_type SELECTION_TYPE,
                          allowed_values VARCHAR(255),
                          min_value DECIMAL,
                          max_value DECIMAL,
                          create_date TIMESTAMP NOT NULL,
                          criteria_group_id UUID NOT NULL
);

-- changeset emre.bulbul:2025-04-23-10-00
CREATE TABLE IF NOT EXISTS criteria_group (
                                               id UUID PRIMARY KEY NOT NULL,
                                               name VARCHAR(255) NOT NULL UNIQUE,
    display_no INTEGER NOT NULL UNIQUE
    );


-- changeset berkeucvet:2025-04-25-15-00
CREATE TABLE criteria_config (
                                 id UUID PRIMARY KEY,
                                 display_order INT2 NOT NULL,
                                 allowed_rules JSONB,
                                 mandatory_rules JSONB,
                                 criteria_id UUID NOT NULL,
                                 CONSTRAINT fk_config_criteria FOREIGN KEY (criteria_id) REFERENCES criteria(id) ON DELETE CASCADE
);

-- changeset emreblbl:2025-05-08-10-00-enum-types
CREATE TYPE bundle_status AS ENUM (
    'ACTIVE',
    'PASSIVE'
    );

CREATE TYPE bundle_type AS ENUM (
    'FLIGHT_INCLUSIVE',
    'STANDALONE'
    );

CREATE TYPE comparison_operator AS ENUM (
    'CONTAINS_ALL',
    'CONTAINS_ANY',
    'CONTAINS_NONE',
    'EQUALS',
    'GREATER_OR_EQUAL',
    'GREATER_THAN',
    'LESS_OR_EQUAL',
    'LESS_THAN',
    'NOT_EQUALS'
    );
CREATE TYPE rule_status AS ENUM (
    'ACTIVE',
    'DRAFT',
    'PASSIVE'
    );

CREATE TYPE rule_type AS ENUM (
    'ALA_CARTE',
    'BUNDLE'
    );



-- changeset emreblbl:2025-05-08-10-02-create-bundle-tables
CREATE TABLE IF NOT EXISTS bundle (
                                      id uuid PRIMARY KEY NOT NULL,
                                      description character varying(255) NOT NULL,
                                      effective_date_from timestamp(6) without time zone NOT NULL,
                                      effective_date_to timestamp(6) without time zone NOT NULL,
                                      image oid,
                                      name character varying(255) NOT NULL,
                                      version integer NOT NULL,
                                      status bundle_status NOT NULL,
                                      type bundle_type NOT NULL
);

CREATE TABLE IF NOT EXISTS product (
                                       id uuid PRIMARY KEY NOT NULL,
                                       category_name character varying(255) NOT NULL,
                                       description character varying(255) NOT NULL,
                                       iata_code character varying(255) NOT NULL,
                                       name character varying(255) NOT NULL,
                                       parent_category_name character varying(255) NOT NULL,
                                       product_id uuid NOT NULL,
                                       retailer_name character varying(255) NOT NULL,
                                       supplier_name character varying(255) NOT NULL,
                                       variant_id uuid NOT NULL
);

CREATE TABLE IF NOT EXISTS bundle_product (
                                              bundle_id uuid NOT NULL,
                                              product_id uuid NOT NULL,
                                              CONSTRAINT fk_bundle_product_bundle FOREIGN KEY (bundle_id) REFERENCES bundle(id),
                                              CONSTRAINT fk_bundle_product_product FOREIGN KEY (product_id) REFERENCES product(id)
);

CREATE TABLE IF NOT EXISTS condition_set_group_config (
                                                          id UUID PRIMARY KEY NOT NULL,
                                                          name VARCHAR(255) NOT NULL,
                                                          effective_date_from TIMESTAMP WITH TIME ZONE NOT NULL,
                                                          effective_date_to TIMESTAMP WITH TIME ZONE NOT NULL,
                                                          priority SMALLINT NOT NULL,
                                                          type rule_type NOT NULL
);

CREATE TABLE IF NOT EXISTS condition_set_group (
                                                   id UUID PRIMARY KEY NOT NULL,
                                                   description VARCHAR(255),
                                                   name VARCHAR(255) NOT NULL,
                                                   version INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS bundle_condition_set_group (
                                                          id UUID PRIMARY KEY NOT NULL,
                                                          bundle_id UUID,
                                                          condition_set_group_config_id UUID,
                                                          condition_set_group_id UUID,
                                                          CONSTRAINT fk_bundle_condition_set_group_bundle FOREIGN KEY (bundle_id) REFERENCES bundle(id),
                                                          CONSTRAINT fk_bundle_condition_set_group_config FOREIGN KEY (condition_set_group_config_id) REFERENCES condition_set_group_config(id),
                                                          CONSTRAINT fk_bundle_condition_set_group_condition_set_group FOREIGN KEY (condition_set_group_id) REFERENCES condition_set_group(id)
);


-- changeset emreblbl:2025-05-08-10-06-create-ruleDefinition-tables
CREATE TABLE IF NOT EXISTS condition_set (
                                             id UUID PRIMARY KEY NOT NULL,
                                             display_order VARCHAR(255) NOT NULL,
                                             version INTEGER NOT NULL,
                                             condition_set_group_id UUID NOT NULL,
                                             CONSTRAINT fk_condition_set_group FOREIGN KEY (condition_set_group_id) REFERENCES condition_set_group(id)
);

CREATE TABLE IF NOT EXISTS action (
                                      id UUID PRIMARY KEY NOT NULL,
                                      action_type VARCHAR(255) NOT NULL,
                                      parameters JSONB NOT NULL,
                                      condition_set_id UUID NOT NULL,
                                      CONSTRAINT fk_action_condition_set FOREIGN KEY (condition_set_id) REFERENCES condition_set(id)
);



-- changeset emreblbl:2025-05-08-10-04-create-condition-tables
CREATE TABLE IF NOT EXISTS condition (
                                         id uuid PRIMARY KEY NOT NULL,
                                         value character varying(255) NOT NULL,
                                         criteria_id uuid NOT NULL,
                                         operator comparison_operator NOT NULL,
                                         CONSTRAINT fk_condition_criteria FOREIGN KEY (criteria_id) REFERENCES criteria(id)
);

CREATE TABLE IF NOT EXISTS condition_node (
                                              id UUID PRIMARY KEY NOT NULL,
                                              condition_id UUID NOT NULL,
                                              condition_set_id UUID NOT NULL,
                                              next_node_id UUID,
                                              previous_node_id UUID,
                                              CONSTRAINT fk_condition_node_condition FOREIGN KEY (condition_id) REFERENCES condition(id),
                                              CONSTRAINT fk_condition_node_condition_set FOREIGN KEY (condition_set_id) REFERENCES condition_set(id)
);



-- changeset emreblbl:2025-05-08-10-07-create-ruleDefinition-template-tables
CREATE TABLE IF NOT EXISTS rule_template (
                                             id uuid PRIMARY KEY NOT NULL,
                                             author character varying(255) NOT NULL,
                                             name character varying(255) NOT NULL,
                                             status character varying(255) NOT NULL,
                                             version integer NOT NULL
);

CREATE TABLE IF NOT EXISTS rule_template_condition_set_group (
                                                                 rule_template_id UUID NOT NULL,
                                                                 condition_set_group_id UUID NOT NULL,
                                                                 CONSTRAINT pk_rule_template_condition_set_group PRIMARY KEY (rule_template_id, condition_set_group_id),
                                                                 CONSTRAINT fk_rule_template_condition_set_group_template FOREIGN KEY (rule_template_id) REFERENCES rule_template(id),
                                                                 CONSTRAINT fk_rule_template_condition_set_group_condition_set_group FOREIGN KEY (condition_set_group_id) REFERENCES condition_set_group(id)
);