databaseChangeLog:
  - changeSet:
      id: 027_update_criteria_table
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropColumn:
            columnName: allowed_values
            tableName: criteria

        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: allowed_values
                  type: text[]
                  constraints:
                    nullable: true

        - modifyDataType:
            tableName: criteria
            columnName: min_value
            newDataType: numeric

        - modifyDataType:
            tableName: criteria
            columnName: max_value
            newDataType: numeric

        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: start_date_time
                  type: timestamp without time zone
                  constraints:
                    nullable: true
              - column:
                  name: end_date_time
                  type: timestamp without time zone
                  constraints:
                    nullable: true

        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: start_time
                  type: time
                  constraints:
                    nullable: true
              - column:
                  name: end_time
                  type: time
                  constraints:
                    nullable: true

        - dropColumn:
            tableName: criteria
            columnName: create_date
