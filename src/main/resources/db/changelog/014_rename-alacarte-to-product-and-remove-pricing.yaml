databaseChangeLog:
  - changeSet:
      id: 014-rename-alacarte-to-product
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - renameTable:
            oldTableName: alacarte_condition_set_group
            newTableName: product_condition_set_group

        - dropForeignKeyConstraint:
            baseTableName: product_condition_set_group
            constraintName: fk_alacarte_condition_set_group_product
        - addForeignKeyConstraint:
            constraintName: fk_product_condition_set_group_product
            baseTableName: product_condition_set_group
            baseColumnNames: product_id
            referencedTableName: product
            referencedColumnNames: id

        - dropForeignKeyConstraint:
            baseTableName: product_condition_set_group
            constraintName: fk_alacarte_condition_set_group_condition_set_group
        - addForeignKeyConstraint:
            constraintName: fk_product_condition_set_group_condition_set_group
            baseTableName: product_condition_set_group
            baseColumnNames: condition_set_group_id
            referencedTableName: condition_set_group
            referencedColumnNames: id

        - dropForeignKeyConstraint:
            baseTableName: product_condition_set_group
            constraintName: fk_alacarte_condition_set_group_rule_definition
        - addForeignKeyConstraint:
            constraintName: fk_product_condition_set_group_rule_definition
            baseTableName: product_condition_set_group
            baseColumnNames: rule_definition_id
            referencedTableName: rule_definition
            referencedColumnNames: id

  - changeSet:
      id: 014-drop-pricing-condition-set-group-table
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropTable:
            tableName: pricing_condition_set_group
            cascadeConstraints: true