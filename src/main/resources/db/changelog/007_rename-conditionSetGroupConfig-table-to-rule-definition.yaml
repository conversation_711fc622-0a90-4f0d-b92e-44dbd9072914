databaseChangeLog:
  - changeSet:
      id: 007-rename-ruleDefinition-table-to-rule
      author: em<PERSON> <PERSON><PERSON><PERSON> - re<PERSON><PERSON>yon teklif ve siparis cozumleri mudurlugu
      changes:
        - renameTable:
            oldTableName: condition_set_group_config
            newTableName: rule_definition

        # Alacarte_condition_set_group rename and foreign key updates
        - renameColumn:
            tableName: alacarte_condition_set_group
            oldColumnName: condition_set_group_config_id
            newColumnName: rule_definition_id
        - dropForeignKeyConstraint:
            baseTableName: alacarte_condition_set_group
            constraintName: fk_alacarte_condition_set_group_condition_set_group_config
        - addForeignKeyConstraint:
            constraintName: fk_alacarte_condition_set_group_rule_definition
            baseTableName: alacarte_condition_set_group
            baseColumnNames: rule_definition_id
            referencedTableName: rule_definition
            referencedColumnNames: id
        # bundle_condition_set_group rename and foreign key updates
        - renameColumn:
            tableName: bundle_condition_set_group
            oldColumnName: condition_set_group_config_id
            newColumnName: rule_definition_id

        - dropForeignKeyConstraint:
            baseTableName: bundle_condition_set_group
            constraintName: fk_bundle_condition_set_group_config

        - addForeignKeyConstraint:
            constraintName: fk_bundle_condition_set_group_rule_definition
            baseTableName: bundle_condition_set_group
            baseColumnNames: rule_definition_id
            referencedTableName: rule_definition
            referencedColumnNames: id

        # pricing_condition_set_group rename and foreign key updates
        - renameColumn:
            tableName: pricing_condition_set_group
            oldColumnName: condition_set_group_config_id
            newColumnName: rule_definition_id

        - dropForeignKeyConstraint:
            baseTableName: pricing_condition_set_group
            constraintName: fk_pricing_condition_set_group_condition_set_group_config

        - addForeignKeyConstraint:
            constraintName: fk_pricing_condition_set_group_rule
            baseTableName: pricing_condition_set_group
            baseColumnNames: rule_definition_id
            referencedTableName: rule_definition
            referencedColumnNames: id
