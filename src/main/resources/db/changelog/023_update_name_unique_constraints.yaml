databaseChangeLog:
  - changeSet:
      id: 23-add-unique-constraints-to-name-fields
      author: ali emre deneri
      changes:
        - addUniqueConstraint:
            tableName: bundle
            columnNames: name
            constraintName: uk_bundle_name
        - addUniqueConstraint:
            tableName: condition_set_group
            columnNames: name
            constraintName: uk_condition_set_group_name
        - addUniqueConstraint:
            tableName: condition_set_group_template
            columnNames: name
            constraintName: uk_condition_set_group_template_name
        - addUniqueConstraint:
            tableName: rule
            columnNames: name
            constraintName: uk_rule_name
