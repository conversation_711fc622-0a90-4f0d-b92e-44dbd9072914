databaseChangeLog:
  - changeSet:
      id: 025_add_market_table
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
      - createTable:
          tableName: market
          columns:
            - column:
                name: id
                type: uuid
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: name
                type: varchar(255)
                constraints:
                  nullable: false
                  unique: true
                  uniqueConstraintName: uk_market_name
            - column:
                name: created_at
                type: timestamp with time zone
                constraints:
                  nullable: false
            - column:
                name: created_by
                type: varchar(255)
            - column:
                name: updated_at
                type: timestamp with time zone
                constraints:
                  nullable: false
            - column:
                name: updated_by
                type: varchar(255)

  - changeSet:
      id: 025_add-market-ports-table
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            tableName: market_ports
            columns:
              - column:
                  name: market_id
                  type: uuid
                  constraints:
                    nullable: false
                    foreignKeyName: fk_market_ports_market
                    references: market(id)
              - column:
                  name: port_code
                  type: varchar(4)
                  constraints:
                    nullable: false
                    foreignKeyName: fk_market_ports_port
                    references: commons.port(port_code)
        - addPrimaryKey:
            tableName: market_ports
            columnNames: market_id, port_code
            constraintName: pk_market_ports
