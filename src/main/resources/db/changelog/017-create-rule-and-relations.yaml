databaseChangeLog:
  - changeSet:
      id: 017-create-rule-table
      author: em<PERSON> <PERSON><PERSON><PERSON> - re<PERSON><PERSON><PERSON> teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            tableName: rule
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: effective_date_from
                  type: TIMESTAMP WITH TIME ZONE
                  constraints:
                    nullable: false
              - column:
                  name: effective_date_to
                  type: TIMESTAMP WITH TIME ZONE
                  constraints:
                    nullable: false
              - column:
                  name: priority
                  type: SMALLINT
                  constraints:
                    nullable: false
              - column:
                  name: rule_type
                  type: rule_type
                  constraints:
                    nullable: false
              - column:
                  name: rule_status
                  type: rule_status
                  constraints:
                    nullable: false
  - changeSet:
      id: 022-create-rule-condition-set-group-join-table
      author: sarp-dev-team
      changes:
        - createTable:
            tableName: rule_condition_set_group
            columns:
              - column:
                  name: rule_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rcsg_rule
                    references: rule(id)
              - column:
                  name: condition_set_group_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rcsg_condition_set_group
                    references: condition_set_group(id)
        - addPrimaryKey:
            tableName: rule_condition_set_group
            columnNames: rule_id, condition_set_group_id
            constraintName: pk_rule_condition_set_group
  - changeSet:
      id: 022-create-rule-bundle-join-table
      author: sarp-dev-team
      changes:
        - createTable:
            tableName: rule_bundle
            columns:
              - column:
                  name: rule_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rb_rule
                    references: rule(id)
              - column:
                  name: bundle_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rb_bundle
                    references: bundle(id)
        - addPrimaryKey:
            tableName: rule_bundle
            columnNames: rule_id, bundle_id
            constraintName: pk_rule_bundle
  - changeSet:
      id: 022-create-rule-product-join-table
      author: sarp-dev-team
      changes:
        - createTable:
            tableName: rule_product
            columns:
              - column:
                  name: rule_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rp_rule
                    references: rule(id)
              - column:
                  name: product_id
                  type: UUID
                  constraints:
                    nullable: false
                    foreignKeyName: fk_rp_product
                    references: product(id)
        - addPrimaryKey:
            tableName: rule_product
            columnNames: rule_id, product_id
            constraintName: pk_rule_product
