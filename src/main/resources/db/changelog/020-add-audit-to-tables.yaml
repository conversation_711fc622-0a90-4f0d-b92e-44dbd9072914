databaseChangeLog:
  - changeSet:
      id: 020-add-audit-columns
      author: ali emre deneri - rezer<PERSON>yon teklif ve siparis cozumleri mudurlugu
      changes:
        - addColumn:
            tableName: rule
            columns:
              - column:
                  name: created_at
                  type: timestamp with time zone
              - column:
                  name: created_by
                  type: varchar(255)
              - column:
                  name: updated_at
                  type: timestamp with time zone
              - column:
                  name: updated_by
                  type: varchar(255)
        - update:
            tableName: rule
            columns:
              - column:
                  name: created_at
                  valueDate: "'1970-01-01T00:00:00Z'"
              - column:
                  name: updated_at
                  valueDate: "'1970-01-01T00:00:00Z'"
            where: created_at IS NULL OR updated_at IS NULL
        - addColumn:
            tableName: condition_set_group
            columns:
              - column:
                  name: created_at
                  type: timestamp with time zone
              - column:
                  name: created_by
                  type: varchar(255)
              - column:
                  name: updated_at
                  type: timestamp with time zone
              - column:
                  name: updated_by
                  type: varchar(255)
        - update:
            tableName: condition_set_group
            columns:
              - column:
                  name: created_at
                  valueDate: "'1970-01-01T00:00:00Z'"
              - column:
                  name: updated_at
                  valueDate: "'1970-01-01T00:00:00Z'"
            where: created_at IS NULL OR updated_at IS NULL
        - addColumn:
            tableName: condition_set_group_template
            columns:
              - column:
                  name: created_at
                  type: timestamp with time zone
              - column:
                  name: created_by
                  type: varchar(255)
              - column:
                  name: updated_at
                  type: timestamp with time zone
              - column:
                  name: updated_by
                  type: varchar(255)
        - update:
            tableName: condition_set_group_template
            columns:
              - column:
                  name: created_at
                  valueDate: "'1970-01-01T00:00:00Z'"
              - column:
                  name: updated_at
                  valueDate: "'1970-01-01T00:00:00Z'"
            where: created_at IS NULL OR updated_at IS NULL
        - addColumn:
            tableName: bundle
            columns:
              - column:
                  name: created_at
                  type: timestamp with time zone
              - column:
                  name: created_by
                  type: varchar(255)
              - column:
                  name: updated_at
                  type: timestamp with time zone
              - column:
                  name: updated_by
                  type: varchar(255)
        - update:
            tableName: bundle
            columns:
              - column:
                  name: created_at
                  valueDate: "'1970-01-01T00:00:00Z'"
              - column:
                  name: updated_at
                  valueDate: "'1970-01-01T00:00:00Z'"
            where: created_at IS NULL OR updated_at IS NULL
        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: created_at
                  type: timestamp with time zone
              - column:
                  name: created_by
                  type: varchar(255)
              - column:
                  name: updated_at
                  type: timestamp with time zone
              - column:
                  name: updated_by
                  type: varchar(255)
        - update:
            tableName: criteria
            columns:
              - column:
                  name: created_at
                  valueDate: "'1970-01-01T00:00:00Z'"
              - column:
                  name: updated_at
                  valueDate: "'1970-01-01T00:00:00Z'"
            where: created_at IS NULL OR updated_at IS NULL
        - sql:
            sql: |
              ALTER TABLE rule 
                ALTER COLUMN created_at SET NOT NULL,
                ALTER COLUMN updated_at SET NOT NULL;
              
              ALTER TABLE criteria 
                ALTER COLUMN created_at SET NOT NULL,
                ALTER COLUMN updated_at SET NOT NULL;
              
              ALTER TABLE bundle 
                ALTER COLUMN created_at SET NOT NULL,
                ALTER COLUMN updated_at SET NOT NULL;
              
              ALTER TABLE condition_set_group_template 
                ALTER COLUMN created_at SET NOT NULL,
                ALTER COLUMN updated_at SET NOT NULL;
              
              ALTER TABLE condition_set_group 
                ALTER COLUMN created_at SET NOT NULL,
                ALTER COLUMN updated_at SET NOT NULL;
