databaseChangeLog:
  - changeSet:
      id: 031-update-condition-value-to-jsonb
      author: mert demirtaban - rezer<PERSON>yon teklif ve siparis cozumleri mudurlugu
      changes:
        - renameColumn:
            tableName: condition
            oldColumnName: value
            newColumnName: value_old
            columnDataType: text[]

        - addColumn:
            tableName: condition
            columns:
              - column:
                  name: value
                  type: jsonb

        - update:
            tableName: condition
            columns:
              - column:
                  name: value
                  valueComputed: "to_jsonb(value_old)"

        - addNotNullConstraint:
            tableName: condition
            columnName: value

        - dropColumn:
            tableName: condition
            columnName: value_old