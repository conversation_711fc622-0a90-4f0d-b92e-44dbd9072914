databaseChangeLog:
  - changeSet:
      id: 021_drop_bundle_product_table
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropTable:
            tableName: bundle_product

  - changeSet:
      id: 020_create_bundle_product_table
      author: system
      changes:
        - createTable:
            tableName: bundle_product
            columns:
              - column:
                  name: id
                  type: uuid
                  defaultValueComputed: gen_random_uuid()
                  constraints:
                    primaryKey: true
                    primaryKeyName: pk_bundle_product
                    nullable: false
              - column:
                  name: bundle_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: product_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: quantity
                  type: int
                  constraints:
                    nullable: true

        - addForeignKeyConstraint:
            baseTableName: bundle_product
            baseColumnNames: bundle_id
            constraintName: fk_bundle_product_bundle
            referencedTableName: bundle
            referencedColumnNames: id
            onDelete: CASCADE

        - addForeignKeyConstraint:
            baseTableName: bundle_product
            baseColumnNames: product_id
            constraintName: fk_bundle_product_product
            referencedTableName: product
            referencedColumnNames: id
            onDelete: CASCADE
