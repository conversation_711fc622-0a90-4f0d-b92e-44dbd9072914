databaseChangeLog:
  - changeSet:
      id: 016-system-defined-criteria-group
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - insert:
            tableName: criteria_group
            columns:
              - column:
                  name: id
                  value: 4f2afd96-b42d-4bfa-a8c7-2f965f8c9111
              - column:
                  name: name
                  value: System Defined Criteria Group
              - column:
                  name: display_order
                  valueNumeric: 1

  - changeSet:
      id: 016-day-of-week-criteria
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - insert:
            tableName: criteria
            columns:
              - column:
                  name: id
                  value: 4f2afd96-b42d-4bfa-a8c7-2f965f8c9222
              - column:
                  name: name
                  value: Day of Week Criteria
              - column:
                  name: description
                  value: System Defined Criteria for Day of Week
              - column:
                  name: type
                  value: SYSTEM_DEFINED
              - column:
                  name: request_type
                  value: OFFER_REQUEST
              - column:
                  name: mapping_field
                  value: originDestinationsCriteria.originDepCriteria.date
              - column:
                  name: field_type
                  value: TEXT
              - column:
                  name: selection_type
                  value: MULTI_SELECT
              - column:
                  name: allowed_values
                  value: "FRIDAY,SATURDAY,SUNDAY,MONDAY,TUESDAY,WEDNESDAY,THURSDAY"
              - column:
                  name: create_date
                  value: 2025-06-17T21:31:09.023199Z
              - column:
                  name: criteria_group_id
                  value: 4f2afd96-b42d-4bfa-a8c7-2f965f8c9111

  - changeSet:
      id: 016-passenger-age-criteria
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - insert:
            tableName: criteria
            columns:
              - column:
                  name: id
                  value: 4f2afd96-b42d-4bfa-a8c7-2f965f8c9333
              - column:
                  name: name
                  value: Passenger Age Criteria
              - column:
                  name: description
                  value: System Defined Criteria for Passenger Age
              - column:
                  name: type
                  value: SYSTEM_DEFINED
              - column:
                  name: request_type
                  value: OFFER_REQUEST
              - column:
                  name: mapping_field
                  value: paxList.birthdate
              - column:
                  name: field_type
                  value: INTEGER
              - column:
                  name: min_value
                  valueNumeric: 1
              - column:
                  name: max_value
                  valueNumeric: 150
              - column:
                  name: create_date
                  value: 2025-06-17T21:31:09.023199Z
              - column:
                  name: criteria_group_id
                  value: 4f2afd96-b42d-4bfa-a8c7-2f965f8c9111