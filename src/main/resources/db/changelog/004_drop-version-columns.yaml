databaseChangeLog:
  - changeSet:
      id: 004-drop-version-column-from-bundle
      author: muhammed unal
      preConditions:
        - onFail: MARK_RAN
        - columnExists:
            tableName: bundle
            columnName: version
      changes:
        - dropColumn:
            tableName: bundle
            columnName: version

  - changeSet:
      id: 004-drop-version-column-from-condition-set-group
      author: muhammed unal
      preConditions:
        - onFail: MARK_RAN
        - columnExists:
            tableName: condition_set_group
            columnName: version
      changes:
        - dropColumn:
            tableName: condition_set_group
            columnName: version

  - changeSet:
      id: 004-drop-version-column-from-condition-set
      author: muhammed unal
      preConditions:
        - onFail: MARK_RAN
        - columnExists:
            tableName: condition_set
            columnName: version
      changes:
        - dropColumn:
            tableName: condition_set
            columnName: version

  - changeSet:
      id: 004-drop-version-column-from-condition-set-group-template
      author: muhammed unal
      preConditions:
        - onFail: MARK_RAN
        - columnExists:
            tableName: condition_set_group_template
            columnName: version
      changes:
        - dropColumn:
            tableName: condition_set_group_template
            columnName: version
