databaseChangeLog:
  - changeSet:
      id: 015-add-condition-set-action
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            tableName: condition_set_action
            columns:
              - column:
                  name: condition_set_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: action_id
                  type: uuid
                  constraints:
                    nullable: false

        - addPrimaryKey:
            tableName: condition_set_action
            columnNames: condition_set_id, action_id
            constraintName: pk_condition_set_action

        - addForeignKeyConstraint:
            baseTableName: condition_set_action
            baseColumnNames: condition_set_id
            referencedTableName: condition_set
            referencedColumnNames: id
            constraintName: fk_condition_set_action_condition_set
            onDelete: CASCADE

        - addForeignKeyConstraint:
            baseTableName: condition_set_action
            baseColumnNames: action_id
            referencedTableName: action
            referencedColumnNames: id
            constraintName: fk_condition_set_action_action
            onDelete: CASCADE

  - changeSet:
      id: 015-drop-condition-set-id-from-action
      author: turkan ozdemir - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropColumn:
            columnName: condition_set_id
            tableName: action