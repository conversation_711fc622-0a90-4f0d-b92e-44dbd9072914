databaseChangeLog:
  - changeSet:
      id: 005-alter-action-type-to-action-table
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - sql:
            splitStatements: false
            stripComments: true
            sql: CREATE TYPE action_type AS ENUM ('PERCENTAGE_DISCOUNT', 'FIXED_DISCOUNT', 'PRICE');
        - dropColumn:
            tableName: action
            columnName: action_type
        - addColumn:
            tableName: action
            columns:
              - column:
                  name: action_type
                  type: action_type
                  constraints:
                    nullable: false
  - changeSet:
      id: 005-add-rule-id-to-action-table
      author: muhammed unal - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - addColumn:
            tableName: action
            columns:
              - column:
                  name: rule_id
                  type: uuid
                  constraints:
                    nullable: false