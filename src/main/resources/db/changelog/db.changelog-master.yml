databaseChangeLog:
  - include:
      file: db/changelog/001_initial-setup-and-add-alacarte-and-pricing-csg.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/002_add-status-column-to-condition-set-group-config-table.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/003_add-deleted-value-to-rule-status-enum.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/004_drop-version-columns.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/005_add-actiontype-rule-id.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/006_remove-unused-effective-date-columns.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/007_rename-conditionSetGroupConfig-table-to-rule-definition.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/008_rename-display-no-to-display-order-criteria-group.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/009-add-waiting-for-action-to-rule-status-enum.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/010-rename-rule-id-to-rule-definition-id-action.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/011_add_between_operator.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/012_modify_condition_value_column_type.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/013_remove_unique_constraint_display_order_criteriagroup.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/014_rename-alacarte-to-product-and-remove-pricing.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/015_add_condition_set_action.yml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/016_create-system-defined-criteria.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/017-create-rule-and-relations.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/018-rename-rule-definition-id-to-rule-id-action.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/019_update-mapping-field-of-system-defined-criteria.yaml
      relativeToChangelogFile: false
  - include:
        file: db/changelog/020-add-audit-to-tables.yaml
        relativeToChangelogFile: false
  - include:
      file: db/changelog/021_add_quantity_to_bundle_product.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/022-add-allowed-operators-to-criteria-and-change-enums.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/023_update_name_unique_constraints.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/024_add-new-values-to-rule-status-enum.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/025_add_market_table.yml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/026_update_market_ports_foreign_key.yml
      relativeToChangelogFile: false
  - include:
        file: db/changelog/027_update_criteria_table.yml
        relativeToChangelogFile: false
  - include:
      file: db/changelog/028_add-custom-operator-enum.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/029_update-system-defined-criterias.yaml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/030-ond-field-type-and-system-defined-criteria-type.yml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/031-update-condition-value-to-jsonb.yml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/032-insert-ond-criteria.yml
      relativeToChangelogFile: false
  - include:
      file: db/changelog/033-insert-criteria-config-for-system-defined-criterias.yml
      relativeToChangelogFile: false
