databaseChangeLog:
  - changeSet:
      id: 022-add-allowed-operators-to-criteria-and-change-enums
      author: sila boyraz - rezervasyon teklif ve siparis cozumleri mudurlugu
      preConditions:
        - onFail: MARK_RAN
        - not:
            - columnExists:
                tableName: criteria
                columnName: allowed_operators
      changes:
        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: allowed_operators
                  type: jsonb
        - sql:
            sql: |
              ALTER TYPE field_type ADD VALUE IF NOT EXISTS 'DECIMAL_NUMBER';
              ALTER TYPE field_type ADD VALUE IF NOT EXISTS 'DATE';
              ALTER TYPE field_type ADD VALUE IF NOT EXISTS 'TIME';
            splitStatements: true
        - sql:
            sql: ALTER TYPE comparison_operator ADD VALUE IF NOT EXISTS 'NOT_BETWEEN'
            splitStatements: true