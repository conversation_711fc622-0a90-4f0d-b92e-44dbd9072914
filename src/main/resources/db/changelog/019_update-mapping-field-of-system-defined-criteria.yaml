databaseChangeLog:
  - changeSet:
      id: 024-update-mapping-fields
      author: ahmet yil<PERSON>z - rezer<PERSON>yon teklif ve siparis cozumleri mudurlugu
      changes:
        - sql:
            splitStatements: false
            stripComments: true
            sql: CREATE TYPE system_defined_criteria_type AS ENUM ('TRAVEL_DATE_DAY_OF_WEEK', 'PASSENGER_AGE');
        - addColumn:
            tableName: criteria
            columns:
              - column:
                  name: system_defined_criteria_type
                  type: system_defined_criteria_type
        - dropNotNullConstraint:
            tableName: criteria
            columnName: mapping_field
        - update:
            tableName: criteria
            columns:
              - column:
                  name: mapping_field
                  valueComputed: "NULL"
            where: id = '4f2afd96-b42d-4bfa-a8c7-2f965f8c9222'
        - update:
            tableName: criteria
            columns:
              - column:
                  name: mapping_field
                  valueComputed: "NULL"
            where: id = '4f2afd96-b42d-4bfa-a8c7-2f965f8c9333'
        - update:
            tableName: criteria
            columns:
              - column:
                  name: system_defined_criteria_type
                  value: TRAVEL_DATE_DAY_OF_WEEK
            where: id = '4f2afd96-b42d-4bfa-a8c7-2f965f8c9222'
        - update:
            tableName: criteria
            columns:
              - column:
                  name: system_defined_criteria_type
                  value: PASSENGER_AGE
            where: id = '4f2afd96-b42d-4bfa-a8c7-2f965f8c9333'
