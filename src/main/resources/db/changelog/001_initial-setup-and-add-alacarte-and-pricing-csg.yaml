databaseChangeLog:
  - changeSet:
      id: 001-initial-schema
      author: ahmet.yilmaz
      changes:
        - sqlFile:
            path: db/sql/v0_0_1/schema.sql
            relativeToChangelogFile: false
            splitStatements: true
            stripComments: true
  - changeSet:
      id: 001-create-alacarte-condition-set-group
      author: mert demirtaban - rezer<PERSON>yon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            tableName: alacarte_condition_set_group
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
              - column:
                  name: product_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: condition_set_group_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: condition_set_group_config_id
                  type: UUID
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            constraintName: fk_alacarte_condition_set_group_product
            baseTableName: alacarte_condition_set_group
            baseColumnNames: product_id
            referencedTableName: product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            constraintName: fk_alacarte_condition_set_group_condition_set_group
            baseTableName: alacarte_condition_set_group
            baseColumnNames: condition_set_group_id
            referencedTableName: condition_set_group
            referencedColumnNames: id
        - addForeignKeyConstraint:
            constraintName: fk_alacarte_condition_set_group_condition_set_group_config
            baseTableName: alacarte_condition_set_group
            baseColumnNames: condition_set_group_config_id
            referencedTableName: condition_set_group_config
            referencedColumnNames: id
  - changeSet:
      id: 001-create-pricing-condition-set-group
      author: mert demirtaban - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - createTable:
            tableName: pricing_condition_set_group
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
              - column:
                  name: product_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: condition_set_group_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: condition_set_group_config_id
                  type: UUID
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            constraintName: fk_pricing_condition_set_group_product
            baseTableName: pricing_condition_set_group
            baseColumnNames: product_id
            referencedTableName: product
            referencedColumnNames: id
        - addForeignKeyConstraint:
            constraintName: fk_pricing_condition_set_group_condition_set_group
            baseTableName: pricing_condition_set_group
            baseColumnNames: condition_set_group_id
            referencedTableName: condition_set_group
            referencedColumnNames: id
        - addForeignKeyConstraint:
            constraintName: fk_pricing_condition_set_group_condition_set_group_config
            baseTableName: pricing_condition_set_group
            baseColumnNames: condition_set_group_config_id
            referencedTableName: condition_set_group_config
            referencedColumnNames: id

  - changeSet:
      id: 001-update-rule-type-enum
      author: mert demirtaban - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - sql:
            sql: ALTER TYPE rule_type ADD VALUE IF NOT EXISTS 'PRICING'
            splitStatements: true

  - changeSet:
      id: 001-remove-nullable-constraints
      author: mert demirtaban - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropNotNullConstraint:
            tableName: product
            columnName: variant_id
            columnDataType: VARCHAR(255)
        - dropNotNullConstraint:
            tableName: product
            columnName: iata_code
            columnDataType: VARCHAR(255)
        - dropNotNullConstraint:
            tableName: product
            columnName: supplier_name
            columnDataType: VARCHAR(255)
        - dropNotNullConstraint:
            tableName: product
            columnName: retailer_name
            columnDataType: VARCHAR(255)
        - dropNotNullConstraint:
            tableName: product
            columnName: parent_category_name
            columnDataType: VARCHAR(255)
        - dropNotNullConstraint:
            tableName: product
            columnName: description
            columnDataType: VARCHAR(255)

  - changeSet:
      id: 001-create-condition-set-group-template-delete-old-tables
      author: ali emre deneri - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
        - dropTable:
            tableName: rule_template
            cascadeConstraints: true
        - dropTable:
            tableName: rule_template_condition_set_group
            cascadeConstraints: true
        - createTable:
            tableName: condition_set_group_template
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: author
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
        - createTable:
            tableName: condition_set_group_template_condition_set_group
            columns:
              - column:
                  name: condition_set_group_template_id
                  type: UUID
                  constraints:
                    nullable: false
              - column:
                  name: condition_set_group_id
                  type: UUID
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            constraintName: fk_csgt_csgt
            baseTableName: condition_set_group_template_condition_set_group
            baseColumnNames: condition_set_group_template_id
            referencedTableName: condition_set_group_template
            referencedColumnNames: id
        - addPrimaryKey:
            tableName: condition_set_group_template_condition_set_group
            columnNames: condition_set_group_template_id, condition_set_group_id
            constraintName: pk_csgt_csg

  - changeSet:
      id: 001-remove-version-column-from-condition-set-group-and-condition-set
      author: ali emre deneri - rezervasyon teklif ve siparis cozumleri mudurlugu
      changes:
            - dropColumn:
                tableName: condition_set_group
                columnName: version
            - dropColumn:
                tableName: condition_set
                columnName: version