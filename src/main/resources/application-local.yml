spring:
  application:
    name: rule-admin-service
  mvc:
    servlet:
      path: /api

  datasource:
    url: ***************************************
    username: sarpdb
    password: sarpdb123
    driver-class-name: org.postgresql.Driver

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true

server:
  servlet:
    context-path: /rule-admin-service

springdoc:
  swagger-ui:
    try-it-out-enabled: true
    default-models-expand-depth: 1
    default-model-expand-depth: 1
    display-request-duration: true
    filter: true
  api-docs:
    path: /v3/api-docs

sarp:
  kafka:
    producer:
      bootstrap-servers: localhost:9092
      client-id: rule-admin-producer
    producers:
      rule-producer:
        bootstrap-servers: localhost:9092

logging:
  level:
    com.sarp.commons.kafka: DEBUG
    org.springframework.kafka: INFO
    org:
      hibernate:
        sql: debug
        type:
          descriptor:
            sql:
              BasicBinder: trace
        orm:
          jdbc:
            bind: trace

service:
  discovery:
    product:
      url: http://localhost:3000/

cors:
  allowed-origins: http://localhost:3000

liquibase:
  commons-properties:
    enabled: true
    changeLog: classpath:db/commons/changelog/db.changelog-master.yml
    defaultSchema: commons
    contexts: commons
    shouldRun: true
    schema-limit: 63
  public-properties:
    enabled: true
    changeLog: classpath:db/changelog/db.changelog-master.yml
    defaultSchema: public
    contexts: public
    shouldRun: true
