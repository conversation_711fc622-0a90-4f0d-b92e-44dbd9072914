components:
  schemas:
    ProductResponseDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        varianterAttributeId:
          type: string
          format: uuid
        briefingNeeded:
          type: boolean
        standalone:
          type: boolean
        hasVariants:
          type: boolean
        contentDescription:
          type: string
        contentType:
          type: string
        contentData:
          type: string
        deliveryUsageType:
          type: string
        deliveryUsageCount:
          type: integer
        deliveryUsageDuration:
          type: integer
        deliveryUsageDurationUnit:
          type: string
        deliveryActivationTrigger:
          type: string
        deliveryLocationType:
          type: string
        deliveryChannel:
          type: string
        refundAvailable:
          type: boolean
        changeAvailable:
          type: boolean
        status:
          type: string
        type:
          type: string
        stocked:
          type: boolean
        varianterAttributeName:
          type: string
        category:
          $ref: '#/components/schemas/CategoryDto'
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantDto'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductAttributeDto'

    CategoryDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    ProductVariantDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        varianterAttributeValues:
          type: array
          items:
            $ref: '#/components/schemas/VarianterAttributeValueDto'
        contentDescription:
          type: string
        contentType:
          type: string
        contentData:
          type: string
        deliveryUsageType:
          type: string
        deliveryUsageCount:
          type: integer
        deliveryUsageDuration:
          type: integer
        deliveryUsageDurationUnit:
          type: string
        deliveryActivationTrigger:
          type: string
        deliveryLocationType:
          type: string
        deliveryChannel:
          type: string
        refundAvailable:
          type: boolean
        changeAvailable:
          type: boolean

    VarianterAttributeValueDto:
      type: object
      properties:
        value:
          type: string
        unit:
          type: string
        lowValueForRange:
          type: string
        highValueForRange:
          type: string

    ProductAttributeDto:
      type: object
      properties:
        attributeId:
          type: string
          format: uuid
        attributeValues:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValueDto'
        attributeName:
          type: string
        id:
          type: string
          format: uuid

    AttributeValueDto:
      type: object
      properties:
        value:
          type: string
        unit:
          type: string
        lowValueForRange:
          type: string
        highValueForRange:
          type: string