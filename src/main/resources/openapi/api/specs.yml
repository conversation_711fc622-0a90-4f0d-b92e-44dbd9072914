openapi: 3.0.0
info:
  title: Rule Admin APIs
  version: 1.0.0
  description: API documentation for managing rules in the SARP system

tags:
  - name: Rule
    description: Rule Management
  - name: bundle
    description: Bundle Management
  - name: criteria
    description: Criteria Management
  - name: criteria-config
    description: Criteria Configuration Management
  - name: criteria-group
    description: Criteria Group Management
  - name: condition-set-group-template
    description: Condition Set Group Template Management
  - name: market
    description: Market Management
  - name: action
    description: Action Management
  - name: product
    description: Product Management

paths:
  /rules:
    $ref: './paths/rule.yml'
  /rules/{id}:
    $ref: './paths/rule-by-id.yml'
  /rules/activate/{id}:
    $ref: './paths/rule-activate.yml'

  /bundles:
    $ref: './paths/bundle.yml'
  /bundles/{id}:
    $ref: './paths/bundle-by-id.yml'
  /bundles/bulk:
    $ref: './paths/bundles-by-ids.yml'
  /criteria:
    $ref: './paths/criteria.yml'
  /criteria/{id}:
    $ref: './paths/criteria-id.yml'
  /criteria-groups:
    $ref: './paths/criteria-group.yml'
  /criteria-groups/with-details:
      $ref: './paths/criteria-group-details.yml'
  /criteria-groups/{id}:
    $ref: './paths/criteria-group-by-id.yml'
  /criteria-configs:
    $ref: './paths/criteria-config.yml'
  /condition-set-groups:
    $ref: './paths/condition-set-group.yml'
  /condition-set-groups/{id}:
    $ref: './paths/condition-set-group-by-id.yml'
  /condition-set-group-templates:
    $ref: './paths/condition-set-group-template.yml'
  /condition-set-group-templates/{id}:
    $ref: './paths/condition-set-group-template-by-id.yml'
  /markets:
    $ref: './paths/market.yml'
  /markets/{id}:
    $ref: './paths/market-id.yml'
  /actions/bulk:
    $ref: './paths/actions-by-ids.yml'
  /products:
    $ref: './paths/product.yml'

components:
  schemas:
    # Command Schemas :
    BaseResponse:
      $ref: './schemas/common.yml#/components/schemas/BaseResponseDTO'
    Error:
      $ref: './schemas/common.yml#/components/schemas/Error'
    Metadata:
      $ref: './schemas/common.yml#/components/schemas/Metadata'

    # Criteria Schemas :
    CreateCriteriaRequestDTO:
      $ref: './schemas/criteria.yml#/components/schemas/CreateCriteriaRequestDTO'
    UpdateCriteriaRequestDTO:
      $ref: './schemas/criteria.yml#/components/schemas/UpdateCriteriaRequestDTO'
    CriteriaResponseDTO:
      $ref: './schemas/criteria.yml#/components/schemas/CriteriaResponseDTO'

    #Condition Schemas :
    ConditionDTO:
      $ref: './schemas/condition.yml#/components/schemas/ConditionDTO'
    UpdateConditionDTO:
      $ref: './schemas/condition.yml#/components/schemas/UpdateConditionDTO'

    # Criteria Group Schemas :
    CreateCriteriaGroupRequestDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CreateCriteriaGroupRequestDTO'
    CreateCriteriaGroupResponseDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CreateCriteriaGroupResponseDTO'
    UpdateCriteriaGroupRequestDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/UpdateCriteriaGroupRequestDTO'
    CriteriaGroupResponseDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CriteriaGroupResponseDTO'
    CriteriaGroupWithCriteriaResponseDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CriteriaGroupWithCriteriaResponseDTO'
    CriteriaDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CriteriaDTO'
    CriteriaGroupWithDetailsResponseDTO:
      $ref: './schemas/criteria-group.yml#/components/schemas/CriteriaGroupWithDetailsResponseDTO'


    # Criteria Config
    SaveCriteriaConfigRequestDTO:
      $ref: './schemas/criteria-config.yml#/components/schemas/SaveCriteriaConfigRequestDTO'
    SaveCriteriaConfigResponseDTO:
      $ref: './schemas/criteria-config.yml#/components/schemas/SaveCriteriaConfigResponseDTO'
    CriteriaSaveDTO:
      $ref: './schemas/criteria-config.yml#/components/schemas/CriteriaConfigSaveDTO'

    # Rule Schemas :

    ## Create :
    CreateRuleRequestDTO:
      $ref: './schemas/rules/create.yml#/components/schemas/CreateRuleRequestDTO'
    CreateRuleResponseDTO:
      $ref: './schemas/rules/create.yml#/components/schemas/CreateRuleResponseDTO'

    ## Update :
    UpdateRuleResponseDTO:
      $ref: './schemas/rules/update.yml#/components/schemas/UpdateRuleResponseDTO'
    UpdateRuleRequestDTO:
      $ref: './schemas/rules/update.yml#/components/schemas/UpdateRuleRequestDTO'
    ActivateRuleRequestDTO:
      $ref: './schemas/rules/rule-activate.yml#/components/schemas/ActivateRuleRequestDTO'

    ## Commons :
    RuleRequestDTO:
      $ref: './schemas/rules/rule.yml#/components/schemas/RuleRequestDTO'
    RuleResponseDTO:
      $ref: './schemas/rules/rule.yml#/components/schemas/RuleResponseDTO'
    RuleTypeDTO:
      $ref: './schemas/rules/rule.yml#/components/schemas/RuleTypeDTO'


    # ConditionSetGroup Schemas
    ConditionSetGroupDTO:
      $ref: "./schemas/condition-set-group.yml/#/components/schemas/ConditionSetGroupDTO"
    UpdateConditionSetGroupDTO:
      $ref: "./schemas/condition-set-group.yml/#/components/schemas/UpdateConditionSetGroupDTO"


    #Bundle Schemas
    BundleDTO:
      $ref: './schemas/bundle/bundle.yml#/components/schemas/BundleDTO'
    BundleTypeDTO:
      $ref: './schemas/bundle/bundle.yml#/components/schemas/BundleTypeDTO'
    BundleStatusDTO:
      $ref: './schemas/bundle/bundle.yml#/components/schemas/BundleStatusDTO'

    # Create Bundle Schemas
    CreateBundlesRequestDTO:
      $ref: './schemas/bundle/create.yml#/components/schemas/CreateBundlesRequestDTO'
    CreateBundleResponseDTO:
      $ref: './schemas/bundle/create.yml#/components/schemas/CreateBundleResponseDTO'

    # Update Bundle Schemas
    UpdateBundleRequestDTO:
      $ref: './schemas/bundle/update.yml#/components/schemas/UpdateBundleRequestDTO'

    # Read Bundle Schemas :
    BundlesByIdsRequestDTO:
      $ref: './schemas/bundle/read.yml#/components/schemas/BundlesByIdsRequestDTO'
    BundlesByIdsResponseDTO:
      $ref: './schemas/bundle/read.yml#/components/schemas/BundlesByIdsResponseDTO'

    # Condition Set Schemas
    ConditionSetDTO:
      $ref: './schemas/condition-set.yml#/components/schemas/ConditionSetDTO'

    ConditionSetGroupResponseDTO:
      $ref: './schemas/condition-set-group.yml#/components/schemas/ConditionSetGroupResponseDTO'

    UpdateConditionSetGroupRequestDTO:
      $ref: "./schemas/condition-set-group.yml/#/components/schemas/UpdateConditionSetGroupRequestDTO"

    UpdateConditionSetDTO:
      $ref: './schemas/condition-set.yml#/components/schemas/UpdateConditionSetDTO'

    CreateConditionSetRequestDTO:
      $ref: './schemas/condition-set-group.yml#/components/schemas/CreateConditionSetGroupRequestDTO'

    # Product Schemas
    ProductDTO:
      $ref: './schemas/product.yml#/components/schemas/ProductDTO'

    # Rule Action Schemas
    ActionDTO: # Referenced within RuleDTO
      $ref: './schemas/action.yml#/components/schemas/ActionDTO'
    UpdateActionDTO: # Referenced within UpdateConditionSetDTO
      $ref: './schemas/action.yml#/components/schemas/UpdateActionDTO'
    GetActionsByIdsRequestDTO:
      $ref: './schemas/action.yml#/components/schemas/GetActionsByIdsRequestDTO'
    GetActionsByIdsResponseDTO:
      $ref: './schemas/action.yml#/components/schemas/GetActionsByIdsResponseDTO'

    # Condition Node Schemas
    ConditionNodeDTO: # Referenced within ConditionSetDTO
      $ref: './schemas/condition-node.yml#/components/schemas/ConditionNodeDTO'
    UpdateConditionNodeDTO: # Referenced within UpdateConditionSetDTO
      $ref: './schemas/condition-node.yml#/components/schemas/UpdateConditionNodeDTO'


    # Condition Set Group Template Schemas
    ConditionSetGroupTemplateDTO:
      $ref: './schemas/condition-set-group-template.yml#/components/schemas/ConditionSetGroupTemplateDTO'
    ConditionSetGroupTemplateResponseDTO:
      $ref: './schemas/condition-set-group-template.yml#/components/schemas/ConditionSetGroupTemplateResponseDTO'
    CreateConditionSetGroupTemplateRequestDTO:
      $ref: './schemas/condition-set-group-template.yml#/components/schemas/CreateConditionSetGroupTemplateRequestDTO'
    UpdateConditionSetGroupTemplateRequestDTO:
      $ref: './schemas/condition-set-group-template.yml#/components/schemas/UpdateConditionSetGroupTemplateRequestDTO'

    # Market Schemas

    CreateMarketRequestDTO:
      $ref: './schemas/market.yml#/components/schemas/CreateMarketRequestDTO'
    UpdateMarketRequestDTO:
      $ref: './schemas/market.yml#/components/schemas/UpdateMarketRequestDTO'
    MarketResponseDTO:
      $ref: './schemas/market.yml#/components/schemas/MarketResponseDTO'

