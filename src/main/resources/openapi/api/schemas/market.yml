components:
  schemas:
    CreateMarketRequestDTO:
      type: object
      required:
        - name
        - portCodes
      properties:
        name:
          type: string
          description: "Name of the market (e.g., 'International', 'Domestic')"
          example: "International"
        portCodes:
          type: array
          description: "List of unique IATA codes"
          items:
            type: string
            minLength: 3
            maxLength: 3
            example: "CUN"
          minItems: 1
          uniqueItems: true
          example: [ "CUN", "SXM", "IND" ]


    UpdateMarketRequestDTO:
      type: object
      required:
        - portCodes
      properties:
        name:
          type: string
          example: "Updated International"
          description: "New name for the market (optional)"
        portCodes:
          type: array
          description: "List of unique IATA codes"
          items:
            type: string
            minLength: 3
            maxLength: 3
            example: "CUN"
          minItems: 1
          uniqueItems: true
          example: [ "CUN", "SXM", "IND" ]

    MarketResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "International"
        ports:
          type: array
          items:
            $ref: "../schemas/port.yml#/components/schemas/PortResponseDTO"
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"


