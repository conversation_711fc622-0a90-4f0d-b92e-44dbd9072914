components:
  schemas:
    ActionDTO:
      type: object
      required:
        - actionType
        - parameters
      properties:
        actionType:
          $ref: '#/components/schemas/ActionTypeDTO'
          description: "Type of action to execute"
          example: "PERCENTAGE_DISCOUNT"
        parameters:
          type: object
          description: "JSON configuration parameters for the action"
          example: '{
            "discountInfo": {
              "1": {
                "value": 12.25,
                "productDiscountInfo": {
                  "1": {
                    "value": 5.00
                  },
                  "2": {
                    "value": 10.00
                  }
                }
              },
              "2": {}
            }
          }'

    UpdateActionDTO:
      type: object
      required:
        - actionType
        - parameters
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule action"
          example: "123e4567-e89b-12d3-a456-************"
        actionType:
          $ref: '#/components/schemas/ActionTypeDTO'
          description: "Type of action to execute"
          example: "PERCENTAGE_DISCOUNT"
        parameters:
          type: object
          description: "JSON configuration parameters for the action"
          example: '{
            "discountInfo": {
              "1": {
                "value": 12.25,
                "productDiscountInfo": {
                  "1": {
                    "value": 5.00
                  },
                  "2": {
                    "value": 10.00
                  }
                }
              },
              "2": {}
            }
          }'

    ActionResponseDTO:
      type: object
      required:
        - id
        - actionType
        - parameters
        - ruleId
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule action"
          example: "123e4567-e89b-12d3-a456-************"
        actionType:
          $ref: '#/components/schemas/ActionTypeDTO'
          description: "Type of action to execute"
          example: "PERCENTAGE_DISCOUNT"
        parameters:
          type: object
          description: "JSON configuration parameters for the action"
          example: '{
            "discountInfo": {
              "1": {
                "value": 12.25,
                "productDiscountInfo": {
                  "1": {
                    "value": 5.00
                  },
                  "2": {
                    "value": 10.00
                  }
                }
              },
              "2": {}
            }
          }'
        ruleId:
          type: string
          format: uuid
          description: "unique identifier of rule to which this action belongs"
          example: "123e4567-e89b-12d3-a456-************"

    ActionTypeDTO:
      type: string
      enum:
        - PERCENTAGE_DISCOUNT
        - FIXED_DISCOUNT
        - PRICE

    GetActionsByIdsRequestDTO:
      type: object
      required:
        - actionIds
      properties:
        actionIds:
          type: array
          items:
            type: string
            format: uuid

    GetActionsByIdsResponseDTO:
      type: object
      required:
        - actions
      properties:
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ActionResponseDTO'
