components:
  schemas:
    PortResponseDTO:
      type: object
      required:
        - portCode
        - portName
        - portType
      properties:
        portCode:
          type: string
          maxLength: 4
          description: Unique identifier of the port (e.g., IATA code)
        portName:
          type: string
          maxLength: 100
          description: Name of the port
        cityCode:
          type: string
          maxLength: 3
          description: City code where the port is located
        longitude:
          type: number
          format: decimal
          description: Geographical longitude of the port
        latitude:
          type: number
          format: decimal
          description: Geographical latitude of the port
        portType:
          type: string
          maxLength: 20
          default: "AIRPORT"
          description: Type of the port
        hasComfort:
          type: boolean
          default: false
          description: Indicates if the port has comfort facilities
        hasConvertibleCurrency:
          type: boolean
          default: true
          description: Indicates if convertible currency is available
        isRoundTripMandatory:
          type: boolean
          default: false
          description: Indicates if round trip is mandatory for this port
        isDomestic:
          type: boolean
          default: false
          description: Indicates if this is a domestic port
        isActive:
          type: boolean
          default: true
          description: Indicates if the port is currently active
        isRefundable:
          type: boolean
          default: true
          description: Indicates if services at this port are refundable
        isAward:
          type: boolean
          default: false
          description: Indicates if this is an award port
        isStarAward:
          type: boolean
          default: false
          description: Indicates if this is a star award port
        ajetActive:
          type: boolean
          default: false
          description: Indicates if AJET is active at this port
        isSpa:
          type: boolean
          default: false
          description: Indicates if the port has spa facilities
        isSpaArrival:
          type: boolean
          default: false
          description: Indicates if spa services are available on arrival
        isActiveForWebAgent:
          type: boolean
          default: true
          description: Indicates if the port is active for web agents
        isDeleted:
          type: boolean
          default: false
          description: Indicates if the port is marked as deleted
