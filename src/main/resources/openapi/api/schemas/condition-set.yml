components:
  schemas:
    ConditionSetDTO:
      type: object
      required:
        - displayOrder
        - conditionNodes
        - actions
      properties:
        displayOrder:
          type: string
          description: "Display order for the condition node in conditionSet"
          example: "1"
        conditionNodes:
          type: array
          description: "List of condition nodes in this condition set"
          items:
            $ref: "./condition-node.yml#/components/schemas/ConditionNodeDTO"
        actions:
          type: array
          description: "List of actions to be performed when the condition set is satisfied"
          items:
            $ref: "./action.yml#/components/schemas/ActionDTO"

    UpdateConditionSetDTO:
      type: object
      required:
        - displayOrder
        - conditionNodes
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule"
          example: "123e4567-e89b-12d3-a456-************"
        displayOrder:
          type: string
          description: "Display order for the condition node in conditionSet"
          example: "1"
        conditionNodes:
          type: array
          description: "List of condition nodes in this condition set"
          items:
            $ref: "./condition-node.yml#/components/schemas/UpdateConditionNodeDTO"
        actions:
          type: array
          description: "List of actions to be performed when the condition set is satisfied"
          items:
            $ref: "./action.yml#/components/schemas/UpdateActionDTO"

    UpdateConditionSetRequestDTO:
      type: object
      required:
        - id
        - displayOrder
        - conditionNodes
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule"
          example: "123e4567-e89b-12d3-a456-************"
        displayOrder:
          type: string
          description: "Display order for the condition node in conditionSet"
          example: "1"
        conditionNodes:
          type: array
          description: "List of condition nodes in this condition set"
          items:
            $ref: "./condition-node.yml#/components/schemas/ConditionNodeDTO"

    ConditionSetResponseDTO:
      type: object
      required:
        - displayOrder
        - conditionNodes
        - actions
        - id
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule"
          example: "123e4567-e89b-12d3-a456-************"
        displayOrder:
          type: string
          description: "Display order for the condition node in conditionSet"
          example: "1"
        conditionNodes:
          type: array
          description: "List of condition nodes in this condition set"
          items:
            $ref: "./condition-node.yml#/components/schemas/ConditionNodeResponseDTO"
        actions:
          type: array
          description: "List of actions to be performed when the condition set is satisfied"
          items:
            $ref: "./action.yml#/components/schemas/ActionResponseDTO"