components:
  schemas:
    SaveCriteriaConfigRequestDTO:
      type: object
      required:
        - groups
      properties:
        groups:
          type: array
          description: List of criteria groups to be saved along with their criterias.
          items:
            $ref: '#/components/schemas/CriteriaGroupConfigSaveDTO'

    CriteriaGroupConfigSaveDTO:
      type: object
      required:
        - groupId
        - displayOrder
        - criterias
      properties:
        groupId:
          type: string
          format: uuid
          example: "2c2f43c2-1f7d-4c53-90e2-6c6be672d13a"
        displayOrder:
          type: integer
          format: int32
          example: 1
        criterias:
          type: array
          description: List of criterias belonging to this group
          items:
            $ref: '#/components/schemas/CriteriaConfigSaveDTO'

    CriteriaConfigSaveDTO:
      type: object
      required:
        - criteriaId
        - displayOrder
        - allowedRuleTypes
        - mandatoryRuleTypes
      properties:
        criteriaId:
          type: string
          format: uuid
          example: "b1b6172d-d57f-42c3-8e3e-287d093f3c8d"
        displayOrder:
          type: integer
          format: int32
          example: 1
        allowedRuleTypes:
          type: array
          description: List of allowed rule types for the criteria
          items:
            $ref: '../schemas/criteria.yml#/components/schemas/CriteriaTypeDTO'
        mandatoryRuleTypes:
          type: array
          description: List of mandatory rule types for the criteria
          items:
            $ref: '../schemas/criteria.yml#/components/schemas/CriteriaTypeDTO'

    SaveCriteriaConfigResponseDTO:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Criteria configuration saved successfully."
    CriteriaConfigResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        displayOrder:
          type: integer
          format: int32
          example: 1
        allowedRuleTypes:
          type: array
          description: List of allowed rule types for the criteria
          items:
            type: string
            enum:
              - BUNDLE
              - ALA_CARTE
            example: BUNDLE
        mandatoryRuleTypes:
          type: array
          description: List of mandatory rule types for the criteria
          items:
            type: string
            enum:
              - BUNDLE
              - ALA_CARTE
            example: BUNDLE
