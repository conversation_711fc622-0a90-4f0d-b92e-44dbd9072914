components:
  schemas:
    ConditionNodeDTO:
      type: object
      required:
        - condition
        - displayOrder
      properties:
        condition:
          $ref: "./condition.yml#/components/schemas/ConditionDTO"
        nextNodeId:
          type: string
          format: uuid
          description: "ID of the next node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true
        previousNodeId:
          type: string
          format: uuid
          description: "ID of the previous node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true

    UpdateConditionNodeDTO:
      type: object
      required:
        - condition
        - displayOrder
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the condition node"
          example: "123e4567-e89b-12d3-a456-************"
        condition:
          $ref: "./condition.yml#/components/schemas/UpdateConditionDTO"
        nextNodeId:
          type: string
          format: uuid
          description: "ID of the next node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true
        previousNodeId:
          type: string
          format: uuid
          description: "ID of the previous node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true



    ConditionNodeResponseDTO:
      type: object
      required:
        - condition
        - displayOrder
        - id
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the condition node"
          example: "123e4567-e89b-12d3-a456-************"
        condition:
          $ref: "./condition.yml#/components/schemas/ConditionResponseDTO"
        nextNodeId:
          type: string
          format: uuid
          description: "ID of the next node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true
        previousNodeId:
          type: string
          format: uuid
          description: "ID of the previous node in the condition chain (if any)"
          example: "123e4567-e89b-12d3-a456-************"
          nullable: true
