components:
  schemas:
    ConditionSetGroupTemplateDTO:
      type: object
      required:
        - name
        - author
        - conditionSetGroupIds
      properties:
        name:
          type: string
          description: "Name of the condition set group template"
          example: "Example Condition Set Group Template"
        author:
          type: string
          description: "Author of the condition set group template"
          example: "<PERSON>"
        conditionSetGroupIds:
          type: array
          description: "List of ConditionSetGroup IDs to associate"
          items:
            type: string
            format: uuid

    ConditionSetGroupTemplateResponseDTO:
      type: object
      required:
        - id
        - name
        - author
        - conditionSetGroupIds
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the condition set group template"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the condition set group template"
          example: "Example Condition Set Group Template"
        author:
          type: string
          description: "Author of the condition set group template"
          example: "<PERSON>"
        conditionSetGroupIds:
          type: array
          description: "List of ConditionSetGroup IDs to associate"
          items:
            type: string
            format: uuid
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"

    CreateConditionSetGroupTemplateRequestDTO:
        type: object
        required:
          - name
          - author
        properties:
          name:
            type: string
            description: "Name of the condition set group template"
            example: "Example Condition Set Group Template"
          author:
            type: string
            description: "Author of the condition set group template"
            example: "John Doe"
          conditionSetGroupIds:
            type: array
            description: "List of ConditionSetGroup IDs to associate"
            items:
              type: string
              format: uuid

    UpdateConditionSetGroupTemplateRequestDTO:
        type: object
        properties:
          name:
            type: string
            description: "Name of the condition set group template"
            example: "Example Condition Set Group Template"
          author:
            type: string
            description: "Author of the condition set group template"
            example: "John Doe"
          conditionSetGroupIds:
            type: array
            description: "List of ConditionSetGroup IDs to associate"
            items:
              type: string
              format: uuid

    ConditionSetGroupTemplateForRuleRequestDTO:
      type: object
      required:
        - name
        - author
      properties:
        name:
          type: string
          description: "Name of the condition set group template"
          example: "Example Condition Set Group Template"
        author:
          type: string
          description: "Author of the condition set group template"
          example: "John Doe"
    ConditionSetGroupTemplateForRuleResponseDTO:
      type: object
      required:
        - name
        - author
      properties:
        name:
          type: string
          description: "Name of the condition set group template"
          example: "Example Condition Set Group Template"
        author:
          type: string
          description: "Author of the condition set group template"
          example: "John Doe"
