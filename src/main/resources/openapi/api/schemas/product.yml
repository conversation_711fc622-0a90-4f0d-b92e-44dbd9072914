components:
  schemas:
    ProductDTO:
      type : object
      required:
        - productId
        - variantId
        - name
        - iataCode
        - supplierName
        - retailerName
        - categoryName
        - parentCategoryName
        - description
      properties:
        productId:
          type: string
          format: uuid
          description: "Unique identifier for the product"
          example: "123e4567-e89b-12d3-a456-************"
        variantId:
            type: string
            format: uuid
            description: "Unique identifier for the product variant"
            example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the product"
          minLength: 1
          maxLength: 100
          example: "Economy Plus Seat"
        iataCode:
            type: string
            description: "IATA code for the product"
            example: "TK123123123"
        supplierName:
            type: string
            description: "Name of the supplier"
            example: "Turkish Airlines"
        retailerName:
            type: string
            description: "Name of the retailer"
            example: "SARP Travel"
        categoryName:
          type: string
          description: "Category of the product"
          example: "Seat Selection"
        parentCategoryName:
          type: string
          description: "Parent category name"
          example: "Ancillary Services"
        description:
          type: string
          description: "Product description"
          example: "Economy Plus seat with extra legroom"

    ProductResponseDTO:
      type: object
      required:
        - id
        - productId
        - variantId
        - name
        - iataCode
        - supplierName
        - retailerName
        - categoryName
        - parentCategoryName
        - description
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the product response"
          example: "123e4567-e89b-12d3-a456-************"
        productId:
          type: string
          format: uuid
          description: "Unique identifier for the product"
          example: "123e4567-e89b-12d3-a456-************"
        variantId:
          type: string
          format: uuid
          description: "Unique identifier for the product variant"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the product"
          minLength: 1
          maxLength: 100
          example: "Economy Plus Seat"
        iataCode:
          type: string
          description: "IATA code for the product"
          example: "TK123123123"
        supplierName:
          type: string
          description: "Name of the supplier"
          example: "Turkish Airlines"
        retailerName:
          type: string
          description: "Name of the retailer"
          example: "SARP Travel"
        categoryName:
          type: string
          description: "Category of the product"
          example: "Seat Selection"
        parentCategoryName:
          type: string
          description: "Parent category name"
          example: "Ancillary Services"
        description:
          type: string
          description: "Product description"
          example: "Economy Plus seat with extra legroom"

    VariantDTO:
      type: object
      required:
        - variantId
        - quantity
      properties:
        variantId:
          type: string
          format: uuid
          example: "a23e4567-e89b-12d3-a456-************"
        quantity:
          type: integer
          minimum: 1
          example: 3


    ProductWithQuantityDTO:
      type: object
      required:
        - product
        - quantity
      properties:
        product:
          $ref: "#/components/schemas/ProductResponseDTO"
        quantity:
          type: integer
          description: Quantity of associate products
          example: 1

