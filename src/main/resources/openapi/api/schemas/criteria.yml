components:
  schemas:
    CreateCriteriaRequestDTO:
      type: object
      required:
        - name
        - description
        - criteriaGroupId
        - mappingField
      properties:
        criteriaGroupId:
          type: string
          example: "some-uuid-value"
          minLength: 1
          maxLength: 50
        description:
          type: string
          example: "Description of the criteria"
          minLength: 1
          maxLength: 255
        name:
          type: string
          example: "My Criteria"
          minLength: 1
          maxLength: 50
        fieldType:
          type: string
          enum:
            - INTEGER
            - DECIMAL_NUMBER
            - TEXT
            - BOOLEAN
            - LIST
            - DATETIME
            - DATE
            - TIME
            - OND
          example: INTEGER
        mappingField:
          type: string
          example: "customer.age"
        allowedValues:
          type: array
          items:
            type: string
          example: [ "1", "2", "3" ]
        minValue:
          type: number
          format: double
          example: 1.0
        maxValue:
          type: number
          format: double
          example: 100.0
        selectionType:
          type: string
          enum:
            - SINGLE_SELECT
            - MULTI_SELECT
          example: SINGLE_SELECT
        startDateTime:
          type: string
          format: local-date-time
          example: "2024-07-11T08:00:00"
        endDateTime:
          type: string
          format: local-date-time
          example: "2024-07-11T18:00:00"
        startTime:
          type: string
          format: local-time
          pattern: '^([01]\d|2[0-3]):[0-5]\d$'
          example: "11:50"
        endTime:
            type: string
            format: local-time
            pattern: '^([01]\d|2[0-3]):[0-5]\d$'
            example: "18:45"
        allowedOperators:
          type: array
          items:
            $ref: '#/components/schemas/ComparisonOperatorDTO'

    CriteriaResponseDTO:
      type: object
      properties:
        id:
          type: string
          example: "some-uuid-value"
          minLength: 1
          maxLength: 50
        criteriaGroupId:
          type: string
          example: "some-uuid-value"
          minLength: 1
          maxLength: 50
        name:
          type: string
          example: "My Criteria"
          minLength: 1
          maxLength: 50
        description:
          type: string
          example: "Description of the rule"
          minLength: 1
          maxLength: 255
        fieldType:
          type: string
          enum:
            - INTEGER
            - DECIMAL_NUMBER
            - TEXT
            - BOOLEAN
            - LIST
            - DATETIME
            - DATE
            - TIME
            - OND
          example: INTEGER
        mappingField:
          type: string
          example: "customer.age"
        allowedValues:
          type: array
          items:
            type: string
          example: [ "All", "Economy"]
        minValue:
          type: number
          format: double
          example: 1.0
        maxValue:
          type: number
          format: double
          example: 100.0
        selectionType:
          type: string
          enum:
            - SINGLE_SELECT
            - MULTI_SELECT
          example: SINGLE_SELECT
        startDateTime:
          type: string
          format: local-date-time
          example: "2024-07-11T08:00:00"
        endDateTime:
          type: string
          format: local-date-time
          example: "2024-07-11T18:00:00"
        startTime:
          type: string
          format: local-time
          pattern: '^([01]\d|2[0-3]):[0-5]\d$'
          example: "11:50"
        endTime:
            type: string
            format: local-time
            pattern: '^([01]\d|2[0-3]):[0-5]\d$'
            example: "18:45"
        allowedOperators:
          type: array
          items:
            $ref: '#/components/schemas/ComparisonOperatorDTO'
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"

    UpdateCriteriaRequestDTO:
      type: object
      properties:
        name:
          type: string
          description: Name of the criteria
          example: "Age Criteria"
        description:
          type: string
          description: Detailed description of the criteria
          example: "Criteria for filtering by age range"

    CriteriaDTO:
      type: object
      properties:
        id:
          type: string
          example: "some-uuid-value"
          minLength: 1
          maxLength: 50
        criteriaGroupId:
          type: string
          example: "some-uuid-value"
          minLength: 1
          maxLength: 50
        name:
          type: string
          example: "My Criteria"
          minLength: 1
          maxLength: 50
        description:
          type: string
          example: "Description of the rule"
          minLength: 1
          maxLength: 255
        fieldType:
          type: string
          enum:
            - INTEGER
            - DECIMAL_NUMBER
            - TEXT
            - BOOLEAN
            - LIST
            - DATETIME
            - DATE
            - TIME
            - OND
          example: INTEGER
        mappingField:
          type: string
          example: "customer.age"
        allowedValues:
          type: string
          example: "1,2,3"
        minValue:
          type: integer
          format: int64
          example: 1
        maxValue:
          type: integer
          format: int64
          example: 100
        selectionType:
          type: string
          enum:
            - SINGLE_SELECT
            - MULTI_SELECT
          example: SINGLE_SELECT
        allowedOperators:
          type: array
          items:
            $ref: '#/components/schemas/ComparisonOperatorDTO'
#TODO

    CriteriaTypeDTO:
      type: string
      enum:
        - BUNDLE
        - ALA_CARTE

    ComparisonOperatorDTO:
      type: string
      enum:
        - EQUALS
        - NOT_EQUALS
        - GREATER_THAN
        - GREATER_OR_EQUAL
        - LESS_THAN
        - LESS_OR_EQUAL
        - CONTAINS_ANY
        - CONTAINS_ALL
        - CONTAINS_NONE
        - BETWEEN
        - NOT_BETWEEN
        - CUSTOM
      example: GREATER_THAN
