# Add this to your common.yml if not already present
components:
  schemas:
    Pageable:
      type: object
      properties:
        page:
          type: integer
          description: Page number (zero-based)
          default: 0
        size:
          type: integer
          description: Number of items per page
          default: 20
        sort:
          type: array
          items:
            type: string
          description: Sorting criteria in the format - property,asc|desc
