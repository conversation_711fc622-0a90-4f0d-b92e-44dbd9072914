components:
  schemas:
    UpdateRuleRequestDTO:
      type: object
      required:
        - name
        - effectiveDateFrom
        - effectiveDateTo
        - type
        - priority
        - conditionSetGroups
      properties:
        name:
          type: string
          description: "Name of the rule"
          example: "Sample Rule"
          minLength: 1
          maxLength: 100
        effectiveDateFrom:
          type: string
          format: date-time
          description: "Start date when the rule becomes effective in UTC"
          example: "2025-06-03T10:40:37.542Z"
        effectiveDateTo:
          type: string
          format: date-time
          description: "End date when the rule expires in UTC"
          example: "2025-06-03T10:40:37.542Z"
        priority:
          type: integer
          description: "Priority of the rule"
          example: 1
        type:
          $ref: './rule.yml#/components/schemas/RuleTypeDTO'
        saveAsDraft:
          type: boolean
          description: "Indicates if the rule will be saved as draft"
          example: false
        conditionSetGroups:
          type: array
          description: "List of rule sets for this rule"
          items:
            $ref: "../condition-set-group.yml#/components/schemas/UpdateConditionSetGroupDTO"
        bundleIds:
          type: array
          description: "List of bundle ids for this rule"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the bundle"
            example: "123e4567-e89b-12d3-a456-************"
        variantIds:
          type: array
          description: "Products"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the product"
            example: "123e4567-e89b-12d3-a456-************"

    UpdateRuleResponseDTO:
      type: object
      required:
        - conditionSetGroups
        - ruleDefinition
      properties:
        ruleType:
          $ref: './rule.yml#/components/schemas/RuleTypeDTO'
        conditionSetGroupTemplate:
          $ref: '../condition-set-group-template.yml#/components/schemas/ConditionSetGroupTemplateForRuleResponseDTO'
        conditionSetGroups:
          type: array
          description: "List of rule sets for this rule"
          items:
            $ref: "../condition-set-group.yml#/components/schemas/ConditionSetGroupResponseDTO"
        bundleIds:
          type: array
          description: "List of bundle ids for this rule"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the bundle"
            example: "123e4567-e89b-12d3-a456-************"
        variantIds:
          type: array
          description: "Products"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the product"
            example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"

