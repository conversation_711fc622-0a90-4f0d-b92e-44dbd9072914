components:
  schemas:
    ActivateRuleRequestDTO:
      type: object
      properties:
        effectiveDateFrom:
          type: string
          format: date-time
          description: "Start date when the rule becomes effective in UTC"
          example: "2025-06-03T10:40:37.542Z"
        effectiveDateTo:
          type: string
          format: date-time
          description: "End date when the rule expires in UTC"
          example: "2025-06-03T10:40:37.542Z"