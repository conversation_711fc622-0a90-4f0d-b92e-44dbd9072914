components:
  schemas:
    RuleRequestDTO:
      type: object
      required:
        - name
        - effectiveDateFrom
        - effectiveDateTo
        - type
        - priority
        - conditionSetGroups
      properties:
        name:
          type: string
          description: "Name of the rule"
          example: "Sample Rule"
          minLength: 1
          maxLength: 100
        effectiveDateFrom:
          type: string
          format: date-time
          description: "Start date when the rule becomes effective in UTC"
          example: "2025-06-03T10:40:37.542Z"
        effectiveDateTo:
          type: string
          format: date-time
          description: "End date when the rule expires in UTC"
          example: "2025-06-03T10:40:37.542Z"
        priority:
          type: integer
          description: "Priority of the rule"
          example: 1
        type:
          $ref: '#/components/schemas/RuleTypeDTO'
        saveAsDraft:
          type: boolean
          description: "Indicates if the rule will be saved as draft"
          example: false
        conditionSetGroupTemplate:
          $ref: '../condition-set-group-template.yml#/components/schemas/ConditionSetGroupTemplateForRuleRequestDTO'
        conditionSetGroups:
          type: array
          description: "List of rule sets for this rule"
          items:
            $ref: "../condition-set-group.yml#/components/schemas/ConditionSetGroupDTO"
        bundles:
          type: array
          description: "List of bundles for this rule"
          items:
            $ref: "../bundle/bundle.yml#/components/schemas/BundleDTO"
        variantIds:
          type: array
          description: "Products"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the product"
            example: "123e4567-e89b-12d3-a456-************"


    RuleResponseDTO:
      type: object
      required:
        - id
        - name
        - effectiveDateFrom
        - effectiveDateTo
        - type
        - priority
        - conditionSetGroups
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the rule"
          example: "Sample Rule"
          minLength: 1
          maxLength: 100
        effectiveDateFrom:
          type: string
          format: date-time
          description: "Start date when the rule becomes effective in UTC"
          example: "2024-01-01T00:00:00Z"
        effectiveDateTo:
          type: string
          format: date-time
          description: "End date when the rule expires in UTC"
          example: "2025-01-01T00:00:00Z"
        priority:
          type: integer
          description: "Priority of the rule"
          example: 1
        type:
          $ref: '#/components/schemas/RuleTypeDTO'
        status:
          $ref: '#/components/schemas/RuleStatusDTO'
        ruleType:
          $ref: './rule.yml#/components/schemas/RuleTypeDTO'
        conditionSetGroupTemplate:
          $ref: '../condition-set-group-template.yml#/components/schemas/ConditionSetGroupTemplateForRuleResponseDTO'
        conditionSetGroups:
          type: array
          description: "List of rule sets for this rule"
          items:
            $ref: "../condition-set-group.yml#/components/schemas/ConditionSetGroupResponseDTO"
        bundles:
          type: array
          description: "List of bundles for this rule"
          items:
            $ref: "../bundle/bundle.yml#/components/schemas/BundleResponseDTO"
        variantIds:
          type: array
          description: "Products"
          items:
            type: string
            format: uuid
            description: "Unique identifier for the product"
            example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"


    RuleTypeDTO:
      type: string
      enum:
        - BUNDLE
        - ALA_CARTE
        - PRICING

    RuleStatusDTO:
      type: string
      enum:
        - ACTIVE
        - PASSIVE
        - DRAFT
        - DELETED
        - WAITING_FOR_ACTION
        - EXPIRED
        - PROCESSING
        - RULE_ENGINE_ERROR
        - IN_REVIEW
