components:
  schemas:
    UpdateBundleRequestDTO:
      type: object
      properties:
        name:
          type: string
          description: "New name for the bundle (optional)"
          example: "Updated Premium Package"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "New description for the bundle (optional)"
          example: "Updated premium bundle with all services and new perks"
          minLength: 1
          maxLength: 250
        type:
          $ref: './bundle.yml#/components/schemas/BundleTypeDTO'
        status:
          $ref: './bundle.yml#/components/schemas/BundleStatusDTO'
        variants: # Client sends the complete new list of variant IDs with corresponding quantities
          type: array
          nullable: true
          items:
            $ref: "../product.yml#/components/schemas/VariantDTO"
          description: "Complete list of product variants with quantities to associate with the bundle. Variants not in this list will be removed."
          example:
            - variantId: "168bb2db4-9d03-4d9f-93d3-88ae2e3e9b57"
              quantity: 1
            - variantId: "ffbc84c0-6a59-4b1f-b957-43a5d1c7a79a"
              quantity: 2
