components:
  schemas:
    BundleDTO:
      type: object
      required:
        - name
        - description
        - type
        - status
        - variants
      properties:
        name:
          type: string
          description: "Name of the bundle"
          example: "Premium Package"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the bundle"
          example: "Premium bundle with all services included"
          minLength: 1
          maxLength: 250
        type:
          $ref: '#/components/schemas/BundleTypeDTO'
        status:
          $ref: '#/components/schemas/BundleStatusDTO'
        variants:
          type: array
          items:
            $ref: "../product.yml#/components/schemas/VariantDTO"
          description: "List of Variants to associate with the bundle"


    BundleResponseDTO:
      type: object
      required:
        - id
        - name
        - description
        - type
        - status
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the bundle"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the bundle"
          example: "Premium Package"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the bundle"
          example: "Premium bundle with all services included"
          minLength: 1
          maxLength: 250
        type:
          $ref: '#/components/schemas/BundleTypeDTO'
        status:
          $ref: '#/components/schemas/BundleStatusDTO'
        productsWithQuantities:
          type: array
          description: "Associated products with quantities"
          items:
            $ref: "../../schemas/product.yml#/components/schemas/ProductWithQuantityDTO"
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"


    BundleTypeDTO:
      type: string
      enum:
        - FLIGHT_INCLUSIVE
        - STANDALONE

    BundleStatusDTO:
      type: string
      enum:
        - ACTIVE
        - PASSIVE
      example: ACTIVE
