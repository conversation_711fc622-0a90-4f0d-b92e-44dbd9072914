components:
  schemas:
    CreateCriteriaGroupRequestDTO:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Criteria Group Name"
          minLength: 1
          maxLength: 100

    CreateCriteriaGroupResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        name:
          type: string
          example: "Criteria Group Name"
          minLength: 1
          maxLength: 100
        displayOrder:
          type: integer
          format: int32
          example: 1

    UpdateCriteriaGroupRequestDTO:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Updated Criteria Group Name"
          minLength: 1
          maxLength: 50
      example:
        name: "Updated Criteria Group Name"

    CriteriaGroupWithCriteriaResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "Criteria Group Name"
        displayOrder:
          type: integer
          format: int32
          example: 1
        criteriaList:
          type: array
          items:
            $ref: '#/components/schemas/CriteriaDTO'
          description: List of criteria associated with this group
      example:
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        name: "Customer"
        displayOrder: 1
        criteriaList:
          - id: "3fa85f64-5717-4562-b3fc-2c963f66afa7"
            name: "Passenger Age"
            description: "Age of the passenger"
            type: "USER_DEFINED"
            requestType: "OFFER_REQUEST"
            mappingField: "passenger.age"
            fieldType: "INTEGER"
            selectionType: "SINGLE_SELECT"
            allowedValues: null
            minValue: 0
            maxValue: 150
            allowedOperators: "BETWEEN,CONTAIN_ANY"

    CriteriaGroupResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "Criteria Group Name"
        displayOrder:
          type: integer
          format: int32
          example: 1

    CriteriaDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the criteria
        name:
          type: string
          description: Name of the criteria
        description:
          type: string
          description: Description of the criteria
        type:
          type: string
          enum: [ USER_DEFINED, SYSTEM_DEFINED ]
          description: Type of the criteria
        requestType:
          type: string
          enum: [ OFFER_REQUEST ]
          description: Request type of the criteria
        mappingField:
          type: string
          description: Mapping field of the criteria
        fieldType:
          type: string
          enum: [ INTEGER, DECIMAL_NUMBER, TEXT, BOOLEAN, LIST, DATETIME, DATE, TIME, OND ]
          description: Field type of the criteria
        selectionType:
          type: string
          enum: [ SINGLE_SELECT, MULTI_SELECT ]
          description: Selection type for list fields
        allowedValues:
          type: array
          items:
            type: string
          description: Allowed values
          nullable: true
        minValue:
          type: string
          format: biginteger
          description: Minimum value for integer fields (supports arbitrarily large integers)
          nullable: true
        maxValue:
          type: string
          format: biginteger
          description: Maximum value for integer fields (supports arbitrarily large integers)
          nullable: true
        allowedOperators:
          type: array
          items:
            $ref: '../schemas/criteria.yml#/components/schemas/ComparisonOperatorDTO'
          description: Allowed operators for the field type
        startDateTime:
          type: string
          format: local-date-time
          description: Start date and time for the criteria (if applicable)
          nullable: true
        endDateTime:
          type: string
          format: local-date-time
          description: End date and time for the criteria (if applicable)
          nullable: true
        startTime:
          type: string
          format: local-time
          description: Start time for the criteria (if applicable)
          nullable: true
        endTime:
          type: string
          format: local-time
          description: End time for the criteria (if applicable)
          nullable: true
      example:
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        name: "Passenger Age"
        description: "Age of the passenger"
        type: "USER_DEFINED"
        requestType: "OFFER_REQUEST"
        mappingField: "passenger.age"
        fieldType: "INTEGER"
        selectionType: "SINGLE_SELECT"
        allowedValues: null
        minValue: "0"
        maxValue: "150"
        allowedOperators: [
          "EQUALS",
          "GREATER_THAN",
          "LESS_THAN",
          "BETWEEN"
        ]
        startDateTime: "2025-07-10T00:00:00"
        endDateTime: "2025-12-31T23:59:59"
        startTime: "00:00"
        endTime: "23:59"


    CriteriaGroupWithDetailsResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "Criteria Group Name"
        displayNo:
          type: integer
          format: int32
          example: 1
        criteriaDetails:
          type: array
          items:
            $ref: '#/components/schemas/CriteriaWithConfigDetailsResponseDTO'
          description: List of criteria with configuration details
      example:
        id: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        name: "Customer"
        displayNo: 1
        criteriaDetails:
          - criteriaId: "3fa85f64-5717-4562-b3fc-2c963f66afa7"
            criteriaName: "Passenger Age"
            mappingField: "passenger.age"
            displayOrder: 1
            allowedRules: ["EQUALS", "GREATER_THAN", "LESS_THAN"]
            mandatoryRules: []

    CriteriaWithConfigDetailsResponseDTO:
      type: object
      properties:
        criteriaId:
          type: string
          format: uuid
          description: Unique identifier for the criteria
        criteriaName:
          type: string
          description: Name of the criteria
        description:
          type: string
          description: Description of the criteria
        type:
          type: string
          enum: [ USER_DEFINED, SYSTEM_DEFINED ]
          description: Type of the criteria
        requestType:
          type: string
          enum: [ OFFER_REQUEST ]
          description: Request type of the criteria
        mappingField:
          type: string
          description: Mapping field of the criteria
        fieldType:
          type: string
          enum: [ INTEGER, DECIMAL_NUMBER, TEXT, BOOLEAN, LIST, DATETIME, DATE, TIME, OND ]
          description: Field type of the criteria
        selectionType:
          type: string
          enum: [ SINGLE_SELECT, MULTI_SELECT ]
          description: Selection type for list fields
        allowedValues:
          type: array
          items:
            type: string
          description: Allowed values for the criteria
        minValue:
          type: number
          format: double
          description: Minimum value for numeric fields
        maxValue:
          type: number
          format: double
          description: Maximum value for numeric fields
        startDateTime:
          type: string
          format: local-date-time
          description: Start datetime for DATETIME field type
        endDateTime:
          type: string
          format: local-date-time
          description: End datetime for DATETIME field type
        startTime:
          type: string
          format: local-time
          pattern: '^([01]\\d|2[0-3]):[0-5]\\d$'
          example: "08:00"
          description: Start time for TIME field type (HH:mm)
        endTime:
          type: string
          format: local-time
          pattern: '^([01]\\d|2[0-3]):[0-5]\\d$'
          example: "17:30"
          description: End time for TIME field type (HH:mm)
        allowedOperators:
          type: array
          items:
            $ref: '../schemas/criteria.yml#/components/schemas/ComparisonOperatorDTO'
          description: Allowed operators for the field type
        displayOrder:
          type: integer
          format: int32
          description: Display order for this criteria within the group
        allowedRules:
          type: array
          items:
            type: string
          description: List of rule types allowed for this criteria
        mandatoryRules:
          type: array
          items:
            type: string
          description: List of rule types that are mandatory for this criteria
