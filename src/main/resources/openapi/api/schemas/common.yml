components:
  schemas:
    BaseResponseDTO:
      type: object
      properties:
        status:
          type: string
          enum: [SUCCESS, FAIL]  # Match your enum values
          example: "SUCCESS"
        message:
          type: string
          example: "Operation successful"
        data:
          type: object
          nullable: true
          description: "Generic data field that can hold any response data"
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
          nullable: true
        metadata:
          $ref: '#/components/schemas/Metadata'
          nullable: true

    Error:
      type: object
      properties:
        code:
          type: string
          example: "INVALID_INPUT"
        field:
          type: string
          example: "name"
        message:
          type: string
          example: "Name is required"

    Metadata:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          example: "2024-01-01T12:00:00Z"
        page:
          type: integer
          example: 1
        size:
          type: integer
          example: 10
        total:
          type: integer
          example: 100

    BaseSearchCriteriaDTO:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          example: 1
        size:
          type: integer
          minimum: 1
          maximum: 100
          default: 100
          example: 100