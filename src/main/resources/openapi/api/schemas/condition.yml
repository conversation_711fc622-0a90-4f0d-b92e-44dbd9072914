components:
  schemas:
    ConditionDTO:
      type: object
      required:
        - criteria
        - operator
        - value
      properties:
        criteria:
          $ref: '../schemas/criteria-group.yml#/components/schemas/CriteriaDTO'
        operator:
          $ref: '#/components/schemas/ComparisonOperatorDTO'
        value:
          type: array
          items:
            type: object
          description: "The values against which criteria is compared"
          example: ["10", "20"]

    UpdateConditionDTO:
      type: object
      required:
        - criteria
        - operator
        - value
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the condition"
          example: "123e4567-e89b-12d3-a456-************"
        criteria:
          $ref: '../schemas/criteria-group.yml#/components/schemas/CriteriaDTO'
        operator:
          $ref: '#/components/schemas/ComparisonOperatorDTO'
        value:
          type: array
          items:
            type: string
          description: "The values against which criteria is compared"
          example: [ "10", "20" ]



    ConditionResponseDTO:
      type: object
      required:
        - criteriaId
        - operator
        - value
        - id
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the condition"
          example: "123e4567-e89b-12d3-a456-************"
        criteria:
          $ref: '../schemas/criteria-group.yml#/components/schemas/CriteriaDTO'
        operator:
          $ref: '#/components/schemas/ComparisonOperatorDTO'
        value:
          type: array
          items:
            type: object
          description: "The values against which criteria is compared"
          example: ["10", "20"]

    ComparisonOperatorDTO:
      type: string
      enum:
        - EQUALS
        - NOT_EQUALS
        - GREATER_THAN
        - GREATER_OR_EQUAL
        - LESS_THAN
        - LESS_OR_EQUAL
        - CONTAINS_ANY
        - CONTAINS_ALL
        - CONTAINS_NONE
        - BETWEEN
        - NOT_BETWEEN
        - CUSTOM
      description: "Comparison operators for evaluating conditions"