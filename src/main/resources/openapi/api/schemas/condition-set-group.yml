components:
  schemas:
    ConditionSetGroupDTO:
      type: object
      properties:
        name:
          type: string
          description: "Name of the rule set"
          example: "Premium Rules"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the rule set"
          example: "Rules applied for premium customers"
          maxLength: 250
        conditionSets:
          type: array
          description: "Rules contained within this rule set"
          items:
            $ref: "./condition-set.yml#/components/schemas/ConditionSetDTO"

    UpdateConditionSetGroupDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule set"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the rule set"
          example: "Premium Rules"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the rule set"
          example: "Rules applied for premium customers"
          maxLength: 250
        conditionSets:
          type: array
          description: "Rules contained within this rule set"
          items:
            $ref: "./condition-set.yml#/components/schemas/UpdateConditionSetDTO"

    ConditionSetGroupResponseDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: "Unique identifier for the rule set"
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          description: "Name of the rule set"
          example: "Premium Rules"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the rule set"
          example: "Rules applied for premium customers"
          maxLength: 250
        conditionSets:
          type: array
          description: "Rules contained within this rule set"
          items:
            $ref: "./condition-set.yml#/components/schemas/ConditionSetResponseDTO"
        createdAt:
          type: string
          format: date-time
          description: "Creation timestamp of the condition set group template"
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: "Last update timestamp of the condition set group template"
          example: "2024-01-02T12:00:00Z"
        createdBy:
          type: string
          description: "User who created the condition set group template"
        updatedBy:
          type: string
          description: "User who last updated the condition set group template"

    CreateConditionSetGroupRequestDTO:
      type: object
      required:
        - conditionSets
      properties:
        name:
          type: string
          description: "Name of the rule set"
          example: "Premium Rules"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the rule set"
          example: "Rules applied for premium customers"
          maxLength: 250
        conditionSets:
          type: array
          description: "Rules contained within this rule set"
          items:
            $ref: "./condition-set.yml#/components/schemas/UpdateConditionSetDTO"

    UpdateConditionSetGroupRequestDTO:
      type: object
      required:
        - name
        - description
        - conditionSets
      properties:
        name:
          type: string
          description: "Name of the rule set"
          example: "Premium Rules"
          minLength: 1
          maxLength: 100
        description:
          type: string
          description: "Description of the rule set"
          example: "Rules applied for premium customers"
          maxLength: 250
        conditionSets:
          type: array
          description: "Rules contained within this rule set"
          items:
            $ref: "./condition-set.yml#/components/schemas/UpdateConditionSetRequestDTO"
