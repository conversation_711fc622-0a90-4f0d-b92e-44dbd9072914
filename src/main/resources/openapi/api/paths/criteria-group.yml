post:
  tags:
    - CriteriaGroup
  summary: Create a new criteria group
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/criteria-group.yml#/components/schemas/CreateCriteriaGroupRequestDTO'
  responses:
    '200':
      description: Criteria Group is created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

get:
  tags:
    - CriteriaGroup
  summary: Get all criteria groups with their criteria
  operationId: getAll
  parameters:
    - name: page
      in: query
      required: false
      schema:
        type: integer
        default: 0
    - name: size
      in: query
      required: false
      schema:
        type: integer
        default: 100
  responses:
    '200':
      description: Criteria Groups retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'