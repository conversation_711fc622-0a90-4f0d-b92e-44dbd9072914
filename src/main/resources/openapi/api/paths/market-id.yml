get:
  tags:
    - Market
  summary: Get market by ID
  operationId: getById
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the market
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Market retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

put:
  tags:
    - Market
  summary: Update a market by ID
  operationId: update
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/market.yml#/components/schemas/UpdateMarketRequestDTO'
  responses:
    '200':
      description: Market updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '404':
      description: Market not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '500':
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

delete:
  tags:
    - Market
  summary: Delete market by ID
  operationId: deleteById
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the market to delete
      schema:
        type: string
        format: uuid
  responses:
    '204':
      description: Market deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Market not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
