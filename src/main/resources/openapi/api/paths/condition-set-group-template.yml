post:
  tags:
    - ConditionSetGroupTemplate
  summary: Create a condition set group template
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/condition-set-group-template.yml#/components/schemas/CreateConditionSetGroupTemplateRequestDTO'
  responses:
    '200':
      description: Condition set group template created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

get:
  tags:
    - ConditionSetGroupTemplate
  summary: Retrieve a list of condition set group templates
  operationId: getConditionSetGroupTemplates
  parameters:
    - name: page
      in: query
      description: Page number of the requested set of results (0-indexed).
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
    - name: size
      in: query
      description: Number of items to return per page.
      required: false
      schema:
        type: integer
        default: 20
        minimum: 1
        maximum: 100
  responses:
    '200':
      description: A paginated list of condition set group templates retrieved successfully.
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'