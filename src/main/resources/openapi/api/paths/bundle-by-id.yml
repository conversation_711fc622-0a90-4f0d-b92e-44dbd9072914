get:
  tags:
    - Bundle
  summary: Get bundle by ID
  operationId: getBundleById
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the bundle
  responses:
    '200':
      description: Bundle retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Bundle not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'


delete:
  tags:
    - Bundle
  summary: Delete a bundle
  operationId: deleteBundleById
  parameters:
    - name: id
      in: path
      required: true
      description: The bundle ID
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Bundle deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Bundle not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

put:
  tags:
    - Bundle
  summary: Update an existing bundle
  operationId: update
  parameters:
    - name: id
      in: path
      required: true
      description: The ID of the bundle to update
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/bundle/update.yml#/components/schemas/UpdateBundleRequestDTO'
  responses:
    '200':
      description: Bundle updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '400':
      description: Invalid input, such as non-existent product IDs
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '404':
      description: Bundle not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
