get:
  tags:
    - CriteriaGroup
  summary: Get criteria group by ID
  operationId: getCriteriaGroup
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the criteria group
  responses:
    '200':
      description: Criteria Group retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Criteria group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

put:
  tags:
    - CriteriaGroup
  summary: Update a criteria group
  operationId: update
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the criteria group
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/criteria-group.yml#/components/schemas/UpdateCriteriaGroupRequestDTO'
  responses:
    '200':
      description: Criteria Group updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Criteria Group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '409':
      description: Criteria Group with name already exists
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

delete:
  tags:
    - CriteriaGroup
  summary: Delete a criteria group
  operationId: delete
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the criteria group
  responses:
    '200':
      description: Criteria Group deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Criteria Group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '409':
      description: Cannot delete Criteria Group with associated Criteria
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'