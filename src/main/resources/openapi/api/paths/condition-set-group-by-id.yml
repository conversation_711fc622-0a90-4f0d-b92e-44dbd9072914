get:
  tags:
    - Condition Set Group
  summary: Retrieve a condition set group by ID
  operationId: getConditionSetGroupById
  parameters:
    - name: id
      in: path
      required: true
      description: The unique identifier of the condition set group
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Condition set group retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition set group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'


delete:
  tags:
    - Condition Set Group
  summary: Delete a condition set group by ID
  operationId: deleteConditionSetGroupById
  parameters:
    - name: id
      in: path
      required: true
      description: The unique identifier of the condition set group
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Condition set group deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition set group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

put:
  tags:
    - Condition Set Group
  summary: Update an existing condition set group
  operationId: updateConditionSetGroupById
  parameters:
    - name: id
      in: path
      required: true
      description: The unique identifier of the condition set group
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/condition-set-group.yml#/components/schemas/UpdateConditionSetGroupRequestDTO'
  responses:
    '200':
      description: Condition set group updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition set group not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'