post:
  tags:
    - Bundle
  summary: Create one or multiple bundles
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/bundle/create.yml#/components/schemas/CreateBundlesRequestDTO'
  responses:
    '200':
      description: Bundle(s) created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

get:
  tags:
    - Bundle
  summary: Retrieve a list of bundles
  operationId: getBundles
  parameters:
    - name: page
      in: query
      description: Page number of the requested set of results (0-indexed).
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
    - name: size
      in: query
      description: Number of items to return per page.
      required: false
      schema:
        type: integer
        default: 20
        minimum: 1
        maximum: 100
    - name: status
      in: query
      description: Filter bundles by status.
      required: false
      schema:
        $ref: '../schemas/bundle/bundle.yml#/components/schemas/BundleStatusDTO'
  responses:
    '200':
      description: A paginated list of bundles retrieved successfully.
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
