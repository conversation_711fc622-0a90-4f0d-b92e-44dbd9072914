get:
  tags:
    - Product
  summary: Get all products with their details
  operationId: getAllProducts
  parameters:
    - name: search
      in: query
      required: false
      description: RSQL query string for filtering products
      schema:
        type: string
    - in: query
      name: pageable
      required: false
      schema:
        $ref: '../schemas/pageable.yml#/components/schemas/Pageable'
  responses:
    '200':
      description: Products with details retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
