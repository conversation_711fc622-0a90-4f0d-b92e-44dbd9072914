post:
  tags:
    - Condition Set Group
  summary: Create one condition set group
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/condition-set-group.yml#/components/schemas/CreateConditionSetGroupRequestDTO'
  responses:
    '200':
      description: Condition set group created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'


get:
  tags:
    - Condition Set Group
  summary: Retrieve a list of condition set groups
  operationId: getConditionSetGroups
  parameters:
    - name: page
      in: query
      description: Page number of the requested set of results (0-indexed).
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
    - name: size
      in: query
      description: Number of items to return per page.
      required: false
      schema:
        type: integer
        default: 20
        minimum: 1
        maximum: 100
  responses:
    '200':
      description: A paginated list of condition set groups retrieved successfully.
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
