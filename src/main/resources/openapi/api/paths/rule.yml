post:
  tags:
    - Rule
  summary: Create a new rule
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/rules/create.yml#/components/schemas/CreateRuleRequestDTO'
  responses:
    '200':
      description: Rule is created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

get:
  tags:
    - Rule
  summary: Get all rules with their groups and configs
  operationId: getAllRules
  parameters:
    - name: search
      in: query
      required: false
      description: RSQL query string for filtering rules
      schema:
        type: string
    - in: query
      name: pageable
      required: false
      schema:
        $ref: '../schemas/pageable.yml#/components/schemas/Pageable'
  responses:
    '200':
      description: Rules with details retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
