get:
  tags:
    - criteria
  summary: Get a criteria by ID
  operationId: getById
  parameters:
    - name: id
      in: path
      required: true
      description: The ID of the criteria to retrieve
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Criteria retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'


put:
  tags:
    - criteria
  summary: Update a criteria by ID
  operationId: update
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/criteria.yml#/components/schemas/UpdateCriteriaRequestDTO'
  responses:
    '200':
      description: Criteria updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '404':
      description: Criteria not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '500':
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

delete:
  tags:
    - criteria
  summary: Delete a criteria by ID
  operationId: deleteCriteriaById
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: The ID of the criteria to delete
  responses:
    '200':
      description: Criteria deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Criteria not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '409':
      description: Criteria is in use and cannot be deleted
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'