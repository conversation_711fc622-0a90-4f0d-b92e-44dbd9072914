get:
  tags:
    - ConditionSetGroupTemplate
  summary: Get Condition Set Group Template by ID
  operationId: getConditionSetGroupTemplateById
  parameters:
    - name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the Condition Set Group Template to retrieve, update, or delete.
  responses:
    '200':
      description: Condition Set Group Template retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition Set Group Template not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

delete:
  tags:
    - ConditionSetGroupTemplate
  summary: Delete Condition Set Group Template by ID
  operationId: deleteConditionSetGroupTemplateById
  parameters:
    - name: id
      in: path
      required: true
      description: The bundle ID
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Condition Set Group Template deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition Set Group Template not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'

put:
  tags:
    - ConditionSetGroupTemplate
  summary: Update an existing Condition Set Group Template
  operationId: updateConditionSetGroupTemplateById
  parameters:
    - name: id
      in: path
      required: true
      description: The ID of the Condition Set Group Template to update
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/condition-set-group-template.yml#/components/schemas/UpdateConditionSetGroupTemplateRequestDTO'
  responses:
    '200':
      description: Condition Set Group Template updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Condition Set Group Template not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'