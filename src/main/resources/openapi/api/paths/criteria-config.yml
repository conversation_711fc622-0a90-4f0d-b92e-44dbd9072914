post:
  tags:
    - criteria-config
  summary: Save criteria configuration
  operationId: saveCriteriaConfig
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/criteria-config.yml#/components/schemas/SaveCriteriaConfigRequestDTO'
  responses:
    '201':
      description: Criteria configuration is saved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '400':
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
    '500':
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/Error'
