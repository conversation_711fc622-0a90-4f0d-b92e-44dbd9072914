get:
  tags:
    - CriteriaGroup
  summary: Get all criteria groups with their criteria and configs
  operationId: getAllWithDetails
  parameters:
    - name: page
      in: query
      required: false
      schema:
        type: integer
        default: 1
    - name: size
      in: query
      required: false
      schema:
        type: integer
        default: 100
    - name: allowedRule
      in: query
      required: false
      schema:
        $ref: '../schemas/rules/rule.yml#/components/schemas/RuleTypeDTO'
  responses:
    '200':
      description: Criteria Group with details retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'


