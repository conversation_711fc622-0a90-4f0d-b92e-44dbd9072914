get:
  tags:
    - Rule
  summary: Get rule by ID
  operationId: getById
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the rule
      schema:
        type: string
        format: uuid
  responses:
    '200':
      description: Rule retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

put:
  tags:
    - Rule
  summary: Update an existing rule
  operationId: update
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the rule
      schema:
        type: string
        format: uuid
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/rules/update.yml#/components/schemas/UpdateRuleRequestDTO'
  responses:
    '200':
      description: Rule is updated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

delete:
  tags:
    - Rule
  summary: Delete rule by ID
  operationId: deleteById
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the rule to delete
      schema:
        type: string
        format: uuid
  responses:
    '204':
      description: Rule deleted successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
    '404':
      description: Rule not found
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
