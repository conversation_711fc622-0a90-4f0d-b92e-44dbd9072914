post:
  tags:
    - Market
  summary: Create a new market
  operationId: create
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../schemas/market.yml#/components/schemas/CreateMarketRequestDTO'
  responses:
    '201':
      description: Market created successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'

get:
  tags:
    - Market
  summary: Get all market with their ports
  operationId: getAll
  parameters:
    - in: query
      name: pageable
      required: false
      schema:
        $ref: '../schemas/pageable.yml#/components/schemas/Pageable'
  responses:
    '200':
      description: Market with ports retrieved successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
