put:
  tags:
    - Rule
  summary: Activate expired or passive rule
  operationId: activate-rule
  parameters:
    - name: id
      in: path
      required: true
      description: ID of the rule
      schema:
        type: string
        format: uuid
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../schemas/rules/rule-activate.yml#/components/schemas/ActivateRuleRequestDTO'
  responses:
    '200':
      description: Rule is activated successfully
      content:
        application/json:
          schema:
            $ref: '../schemas/common.yml#/components/schemas/BaseResponseDTO'
