# --- Bundle Validation Specific Error Codes (from BundleDomainException instances) ---
bundle_id_cannot_be_null=Bundle ID cannot be null.
bundle_name_cannot_be_null_or_empty=Bundle name must not be null or empty.
bundle_description_cannot_be_null_or_empty=Bundle description must not be null or empty.
bundle_status_cannot_be_null=Bundle status cannot be null.
bundle_effective_dates_cannot_be_null=Bundle effective dates cannot be null.
bundle_type_cannot_be_null=Bundle type cannot be null.
bundle_at_least_one_product_with_quantity=Bundle should include at least one product with quantity.
bundle_list_cannot_be_empty=The bundle list cannot be empty.
bundle_variant_id_list_cannot_be_empty=The product variant ID list of bundle cannot be empty for a bundle: {0}
bundle_variant_must_be_unique= product variant ID of Bundle is not unique
bundle_not_found=The bundle not found: {0}
bundle_cannot_contain_null_products= Bundle cannot contain null products

# --- Product Condition Set Group Error Codes (from ProductConditionSetGroupDomainException instances) ---
product_condition_set_group_product_id_list_cannot_be_empty=Product condition set group product id list cannot be empty. Rule type: {0}

# --- Condition Error Codes (from ConditionDomainException instances) ---
condition_list_cannot_be_empty=Condition list cannot be empty.
condition_operator_cannot_be_null=Condition operator cannot be empty.
condition_value_cannot_be_null=Condition value cannot be empty.
condition_criteria_id_cannot_be_null=Condition criteria id cannot be null.
condition_failed_to_save=Condition failed to save.
condition_id_cannot_be_null=Condition id cannot be null.

# --- Condition Set Error Codes ---
condition_set_display_order_cannot_be_null_or_empty=Condition set display order cannot be null or empty.
actions_in_condition_set_must_not_have_the_same_action_type=Actions in condition set must not have the same action type.

# --- Condition Set Group Error Codes ---
conditionSetGroup_not_found=Condition Set Group not found: {0}
conditionSetGroup_product_id_cannot_be_null=Condition Set Group product Id cannot be null
conditionSetGroup_id_cannot_be_null=Condition Set Group ID cannot be null
conditionSetGroup_rule_definition_id_cannot_be_null=Condition Set Group rule definition Id cannot be null
conditionSetGroup_bundle_id_cannot_be_null=Condition Set Group bundleId cannot be null
conditionSetGroup_name_cannot_be_null_or_empty=Condition Set Group name cannot be null or empty
conditionSetGroup_description_cannot_be_null_or_empty=Condition Set Group description cannot be null or empty
conditionSetGroup_conditionSets_cannot_be_null_or_empty=Condition Set Group conditionSets cannot be null or empty

# ConditionSetGroupTemplate Exception Messages
condition_set_group_template_not_found=Condition Set Group Template not found
condition_set_group_template_name_cannot_be_null_or_empty=Condition Set Group Template name cannot be null or empty
condition_set_group_template_author_cannot_be_null_or_empty=Condition Set Group Template author cannot be null or empty
condition_set_group_template_must_have_at_least_one_condition_set_group_id=Condition Set Group Template must have at least one Condition Set Group ID

# CriteriaConfig Exception Messages
criteria_config_id_cannot_be_null=Criteria config ID cannot be null.
criteria_config_criteria_cannot_be_null=Criteria config criteria cannot be null.
criteria_config_display_order_cannot_be_null_or_empty=Criteria config display order cannot be null or empty.
criteria_config_group_id_does_not_exist=Criteria config group ID does not exist.
criteria_config_criteria_id_does_not_exist=Criteria config criteria ID does not exist.

# Criteria Exception Messages
criteria_not_found=Criteria not found {0}
criteria_name_cannot_be_empty=Criteria name cannot be empty
criteria_creation_failed=Criteria creation failed
criteria_description_cannot_be_empty=Criteria description cannot be empty
criteria_type_cannot_be_null=Criteria type cannot be null
criteria_request_type_cannot_be_null=Criteria request type cannot be null
criteria_field_type_cannot_be_null=Criteria field type cannot be null
criteria_mapping_field_cannot_be_empty=Criteria mapping field cannot be empty
criteria_values_cannot_be_empty=Criteria values cannot be empty
criteria_min_value_cannot_be_null=Criteria minimum value cannot be null
criteria_max_value_cannot_be_null=Criteria maximum value cannot be null
criteria_create_date_cannot_be_null=Criteria create date cannot be null
criteria_is_linked_to_rule=Criteria is linked to a rule and cannot be deleted
criteria_has_condition_set=Criteria has a condition set and cannot be deleted {0}
criteria_selection_type_cannot_be_null=Criteria selection type cannot be null
criteria_system_defined_type_field_cannot_be_empty=System defined criteria type field cannot be empty
criteria_allowed_operators_is_not_valid=Criteria allowed operators is not compatible with field type.

# CriteriaGroup Exception Messages
criteria_group_not_found=Criteria group not found {0}
criteria_group_id_cannot_be_null=Criteria group ID cannot be null
criteria_group_name_cannot_be_null_or_empty=Criteria group name cannot be null or empty
criteria_group_display_no_cannot_be_null_or_empty=Criteria group display number cannot be null or empty
criteria_group_has_associated_criteria=Criteria group cannot be deleted as it has associated criteria {0} {1}

# Product Exception Messages
product_id_cannot_be_null=Product ID cannot be null.
product_variant_id_cannot_be_null=Product variantId cannot be null.
product_not_found=Product not found {0}
products_not_found_by_variant_ids=Products not found for the following variant IDs: {0}

# RuleAction Exception Messages
rule_action_not_found=Rule action not found
rule_action_parameters_cannot_be_null=Rule action parameters cannot be null
rule_action_action_type_cannot_be_null=Rule action type cannot be null

# BundleSetConfig Exception Messages
bundle_set_config_rule_type_can_not_be_null=Rule type cannot be null.
bundle_set_config_effective_dates_cannot_be_null=Effective dates cannot be null.
bundle_set_config_name_cannot_be_null_or_empty=Bundle set config name cannot be null or empty.

# Rule Create Exception Messages
rule_type_incompatible_with_action_type=Rule type incompatible with action type {0}.


# Rule Validation Exception Messages
rule_validation_failed=Rule validation failed. Please see the following issues: {0}
rule_field_cannot_be_null={0} cannot be null.
rule_bundle_target_required=Bundle rules must target at least one bundle.
rule_bundle_cannot_contain_products=Bundle rules cannot contain products.
rule_product_target_required=Product rules must target at least one product.
rule_product_cannot_contain_bundles=Product rules cannot contain bundles.
rule_variant_id_list_cannot_be_empty= Rule variant id list cannot be empty
rule_condition_set_group_cannot_be_null= Condition set group of Rule cannot be null
rule_type_validator_not_found=No validator found for rule type: {0}
product_mapping_strategy_not_found=No product mapping strategy found for rule type: {0}

# ProductWithQuantity Exception Messages
product_cannot_be_null=Product cannot be null.
variant_id_cannot_be_null= Variant ID cannot be null.
