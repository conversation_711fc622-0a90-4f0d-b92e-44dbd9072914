spring:
  application:
    name: rule-admin-service
  mvc:
    servlet:
      path: /api
  messages:
    basename: i18n/rule_messages,i18n/core_messages
    encoding: UTF-8


  datasource:
    host: ${SPRING_DATASOURCE_HOST}
    db: ${SPRING_DATASOURCE_DB}
    url: jdbc:postgresql://${spring.datasource.host}:5432/${spring.datasource.db}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    show-sql: false

  liquibase:
    enabled: false
    drop-first: false
    change-log: classpath:db/changelog/db.changelog-master.yml
    default-schema: public

server:
  servlet:
    context-path: /rule-admin-service

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    try-it-out-enabled: true
    default-models-expand-depth: 1
    default-model-expand-depth: 1
    display-request-duration: true
    filter: true
  show-actuator: false
  packages-to-scan: com.sarp.rule.controller

sarp:
  kafka:
    producer:
      bootstrap-servers: ${SARP_KAFKA_PRODUCER_BOOTSTRAP_SERVERS:localhost:9092}
      client-id: ${SARP_KAFKA_PRODUCER_CLIENT_ID:rule-admin-producer}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 5
        batch.size: 16384
        compression.type: lz4
        buffer.memory: 33554432
    producers:
      rule-producer:
        bootstrap-servers: ${SARP_KAFKA_PRODUCER_BOOTSTRAP_SERVERS:localhost:9092}
        client-id: ${SARP_KAFKA_PRODUCER_CLIENT_ID:rule-admin-rule-producer}
  rule:
    event:
      producer-name: ${SARP_RULE_EVENT_PRODUCER:rule-producer}
      topic: ${SARP_RULE_EVENT_TOPIC:sarp.offer.rule.0}
  market:
    event:
      producer-name: ${SARP_RULE_EVENT_PRODUCER:rule-producer}
      topic: ${SARP_RULE_MARKET_TOPIC:sarp.offer.market.0}


  error:
    locale:
      supported: en,tr
      default: en
    service-prefix: "RUL"

service:
  discovery:
    product:
      url: ${SERVICE_DISCOVERY_PRODUCT_URL:http://localhost:3000/}

cors:
  allowed-origins: http://localhost:3000

liquibase:
  commons-properties:
    enabled: false
    changeLog: classpath:db/commons/changelog/db.changelog-master.yml
    defaultSchema: commons
    contexts: commons
    shouldRun: true
    schema-limit: 63
  public-properties:
    enabled: false
    changeLog: classpath:db/changelog/db.changelog-master.yml
    defaultSchema: public
    contexts: public
    shouldRun: true
