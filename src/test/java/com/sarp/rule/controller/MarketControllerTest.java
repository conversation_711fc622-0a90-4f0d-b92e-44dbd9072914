package com.sarp.rule.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.generated.openapi.api.dto.CreateMarketRequestDTO;
import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateMarketRequestDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketQueryMapper;
import com.sarp.rule.application.handler.market.*;
import com.sarp.rule.application.mapper.market.CreateMarketCommandMapper;
import com.sarp.rule.application.mapper.market.UpdateMarketCommandMapper;
import com.sarp.rule.domain.command.market.*;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.query.market.MarketByIdQuery;
import com.sarp.rule.domain.query.market.MarketsQuery;
import java.util.Collections;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class MarketControllerTest {

    @Mock
    private CreateMarketCommandHandler createMarketCommandHandler;

    @Mock
    private CreateMarketCommandMapper createMarketCommandMapper;

    @Mock
    private UpdateMarketCommandHandler updateMarketCommandHandler;

    @Mock
    private UpdateMarketCommandMapper updateMarketCommandMapper;

    @Mock
    private DeleteMarketCommandHandler deleteMarketCommandHandler;

    @Mock
    private MarketsQueryHandler marketsQueryHandler;

    @Mock
    private MarketByIdQueryHandler marketByIdQueryHandler;

    @Mock
    private MarketQueryMapper marketQueryMapper;

    @InjectMocks
    private MarketController marketController;

    private UUID marketId;
    private MarketResponseDTO marketResponseDTO;
    private CreateMarketRequestDTO createMarketRequestDTO;
    private UpdateMarketRequestDTO updateMarketRequestDTO;
    private Market market;

    @BeforeEach
    void setUp() {
        marketId = UUID.randomUUID();
        marketResponseDTO = new MarketResponseDTO();
        createMarketRequestDTO = new CreateMarketRequestDTO();
        updateMarketRequestDTO = new UpdateMarketRequestDTO();
        market = Market.builder().id(marketId).name("Test Market").build();
    }

    @Test
    @DisplayName("Should successfully create a market")
    void create_ShouldCreateMarket() {
        // Given
        CreateMarketCommand command = new CreateMarketCommand(market);
        when(createMarketCommandMapper.toCommand(any())).thenReturn(command);
        when(createMarketCommandHandler.handle(any())).thenReturn(marketResponseDTO);

        // When
        ResponseEntity<BaseResponseDTO> response = marketController.create(createMarketRequestDTO);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getMessage()).isEqualTo("Market created successfully");
        verify(createMarketCommandMapper).toCommand(createMarketRequestDTO);
        verify(createMarketCommandHandler).handle(command);
    }

    @Test
    @DisplayName("Should successfully get all markets")
    void getAll_ShouldReturnAllMarkets() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<MarketResponseDTO> marketPage = new PageImpl<>(Collections.singletonList(marketResponseDTO));
        when(marketsQueryHandler.handle(any(MarketsQuery.class))).thenReturn(marketPage);

        // When
        ResponseEntity<BaseResponseDTO> response = marketController.getAll(pageable);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getMessage()).isEqualTo("Marketss retrieved successfully");
        verify(marketsQueryHandler).handle(any(MarketsQuery.class));
    }

    @Test
    @DisplayName("Should successfully get market by ID")
    void getById_ShouldReturnMarket() {
        // Given
        MarketByIdQuery query = new MarketByIdQuery(marketId);
        when(marketQueryMapper.toQuery(marketId)).thenReturn(query);
        when(marketByIdQueryHandler.handle(query)).thenReturn(marketResponseDTO);

        // When
        ResponseEntity<BaseResponseDTO> response = marketController.getById(marketId);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getMessage()).isEqualTo("Market fetched successfully");
        verify(marketQueryMapper).toQuery(marketId);
        verify(marketByIdQueryHandler).handle(query);
    }

    @Test
    @DisplayName("Should successfully update market")
    void update_ShouldUpdateMarket() {
        // Given
        UpdateMarketCommand command = new UpdateMarketCommand(marketId, market);
        when(updateMarketCommandMapper.toCommand(any(), any())).thenReturn(command);
        when(updateMarketCommandHandler.handle(command)).thenReturn(marketResponseDTO);

        // When
        ResponseEntity<BaseResponseDTO> response = marketController.update(marketId, updateMarketRequestDTO);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getMessage()).isEqualTo("Market updated successfully");
        verify(updateMarketCommandMapper).toCommand(marketId, updateMarketRequestDTO);
        verify(updateMarketCommandHandler).handle(command);
    }

    @Test
    @DisplayName("Should successfully delete market")
    void deleteById_ShouldDeleteMarket() {
        // Given
        DeleteMarketCommand expectedCommand = new DeleteMarketCommand(marketId);
        doNothing().when(deleteMarketCommandHandler).handle(any(DeleteMarketCommand.class));

        // When
        ResponseEntity<BaseResponseDTO> response = marketController.deleteById(marketId);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getMessage()).isEqualTo("Market deleted successfully");
        verify(deleteMarketCommandHandler)
                .handle(argThat(command -> command.getId().equals(expectedCommand.getId())));
    }
}
