package com.sarp.rule.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.generated.openapi.api.dto.ActivateRuleRequestDTO;
import com.sarp.rule.application.handler.rule.ActivateRuleCommandHandler;
import com.sarp.rule.application.mapper.rule.ActivateRuleCommandMapper;
import com.sarp.rule.domain.command.rule.ActivateRuleCommand;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.ResponseEntity;

@ExtendWith(org.mockito.junit.jupiter.MockitoExtension.class)
class RuleControllerTest {
    @Mock
    private ActivateRuleCommandHandler activateRuleCommandHandler;

    @Mock
    private ActivateRuleCommandMapper activateRuleCommandMapper;

    @InjectMocks
    private RuleController ruleController;

    @Test
    void testActivateRule() {
        UUID id = UUID.randomUUID();
        ActivateRuleRequestDTO requestDTO = new ActivateRuleRequestDTO();
        ActivateRuleCommand command = mock(ActivateRuleCommand.class);

        when(activateRuleCommandMapper.toCommand(id, requestDTO)).thenReturn(command);

        ResponseEntity<BaseResponseDTO> response = ruleController.activateRule(id, requestDTO);

        verify(activateRuleCommandHandler).handle(command);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
    }
}
