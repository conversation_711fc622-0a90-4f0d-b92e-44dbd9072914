package com.sarp.rule.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sarp.core.controller.dto.BaseResponseDTO;
import com.sarp.rule.application.dto.product.ProductApplicationDTO;
import com.sarp.rule.application.handler.product.ProductsQueryHandler;
import com.sarp.rule.domain.command.product.ProductsQuery;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class ProductControllerTest {

    @Mock
    private ProductsQueryHandler productsQueryHandler;

    @InjectMocks
    private ProductController productController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAllProducts_returnsProductsSuccessfully() {
        Pageable pageable = PageRequest.of(0, 10);
        ProductApplicationDTO dto = ProductApplicationDTO.builder().name("test").build();
        Page<ProductApplicationDTO> page = new PageImpl<>(Collections.singletonList(dto));
        when(productsQueryHandler.handle(any(ProductsQuery.class))).thenReturn(page);

        ResponseEntity<BaseResponseDTO> response = productController.getAllProducts(null, pageable);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        verify(productsQueryHandler, times(1)).handle(any(ProductsQuery.class));
    }

    @Test
    void getAllProducts_withSearchParam() {
        Pageable pageable = PageRequest.of(0, 5);
        String search = "name==test";
        Page<ProductApplicationDTO> page = new PageImpl<>(Collections.emptyList());
        when(productsQueryHandler.handle(any(ProductsQuery.class))).thenReturn(page);

        ResponseEntity<BaseResponseDTO> response = productController.getAllProducts(search, pageable);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        verify(productsQueryHandler, times(1)).handle(any(ProductsQuery.class));
    }
}
