package com.sarp.rule.application.mapper.market;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.UpdateMarketRequestDTO;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import com.sarp.rule.domain.entity.Port;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateMarketCommandMapperTest {

    private UpdateMarketCommandMapper mapper;
    private UUID marketId;
    private String marketName;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(UpdateMarketCommandMapper.class);
        marketId = UUID.randomUUID();
        marketName = "Updated Market";
    }

    @Test
    @DisplayName("Should map UpdateMarketRequestDTO to UpdateMarketCommand")
    void toCommand_ShouldMapRequestToCommand() {
        // Given
        Set<String> portCodes = Set.of("IST", "SAW");
        UpdateMarketRequestDTO requestDTO =
                new UpdateMarketRequestDTO().name(marketName).portCodes(portCodes);

        // When
        UpdateMarketCommand result = mapper.toCommand(marketId, requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts())
                .hasSize(2)
                .extracting(Port::getPortCode)
                .containsExactlyInAnyOrder("IST", "SAW");
    }

    @Test
    @DisplayName("Should handle null port codes in request")
    void toCommand_ShouldHandleNullPortCodes() {
        // Given
        UpdateMarketRequestDTO requestDTO =
                new UpdateMarketRequestDTO().name(marketName).portCodes(null);

        // When
        UpdateMarketCommand result = mapper.toCommand(marketId, requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts()).isEmpty();
    }

    @Test
    @DisplayName("Should handle empty port codes in request")
    void toCommand_ShouldHandleEmptyPortCodes() {
        // Given
        UpdateMarketRequestDTO requestDTO =
                new UpdateMarketRequestDTO().name(marketName).portCodes(new HashSet<>());

        // When
        UpdateMarketCommand result = mapper.toCommand(marketId, requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts()).isEmpty();
    }

    @Test
    @DisplayName("Should handle null name in request")
    void toCommand_ShouldHandleNullName() {
        // Given
        Set<String> portCodes = Set.of("IST", "SAW");
        UpdateMarketRequestDTO requestDTO =
                new UpdateMarketRequestDTO().name(null).portCodes(portCodes);

        // When
        UpdateMarketCommand result = mapper.toCommand(marketId, requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isNull();
        assertThat(result.getMarket().getPorts())
                .hasSize(2)
                .extracting(Port::getPortCode)
                .containsExactlyInAnyOrder("IST", "SAW");
    }

    @Test
    @DisplayName("Should correctly map port codes to Port objects")
    void portCodesToPorts_ShouldMapCorrectly() {
        // Given
        Set<String> portCodes = Set.of("IST", "SAW", "ADB");

        // When
        Set<Port> result = mapper.portCodesToPorts(portCodes);

        // Then
        assertThat(result).hasSize(3).extracting(Port::getPortCode).containsExactlyInAnyOrder("IST", "SAW", "ADB");
    }

    @Test
    @DisplayName("Should return empty set for null port codes")
    void portCodesToPorts_ShouldHandleNullInput() {
        // When
        Set<Port> result = mapper.portCodesToPorts(null);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should return empty set for empty port codes")
    void portCodesToPorts_ShouldHandleEmptyInput() {
        // When
        Set<Port> result = mapper.portCodesToPorts(new HashSet<>());

        // Then
        assertThat(result).isEmpty();
    }
}
