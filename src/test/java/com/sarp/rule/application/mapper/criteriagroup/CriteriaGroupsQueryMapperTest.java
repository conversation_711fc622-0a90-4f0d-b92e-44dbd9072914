package com.sarp.rule.application.mapper.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupWithDetailsResponseDTO;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsQuery;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;

class CriteriaGroupsQueryMapperTest {

    private CriteriaGroupsQueryMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(CriteriaGroupsQueryMapper.class);
    }

    @Test
    @DisplayName("Should map page and size to CriteriaGroupsQuery")
    void shouldMapPageAndSizeToCriteriaGroupsQuery() {
        // Given
        int page = 1;
        int size = 10;

        // When
        CriteriaGroupsQuery query = mapper.toQuery(page, size);

        // Then
        assertThat(query).isNotNull();
        assertThat(query.getPagingParams().getPageSize()).isEqualTo(size);
        assertThat(query.getPagingParams().getPageNumber()).isEqualTo(page);
    }

    @Test
    @DisplayName("Should map PaginatedResultApplicationDTO to Page of CriteriaGroupWithCriteriaResponseDTO")
    void shouldMapPaginatedResultToCriteriaGroupResponsePage() {
        // Given
        CriteriaGroupApplicationDTO applicationDTO = CriteriaGroupApplicationDTO.builder()
                .id(UUID.randomUUID())
                .name("Test Group")
                .displayOrder(null)
                .criteriaList(null)
                .build();

        PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO> paginatedResult =
                new PaginatedResultApplicationDTO<>(List.of(applicationDTO), 1, 10, 1, 1);

        // When
        Page<CriteriaGroupWithCriteriaResponseDTO> responsePage = mapper.toResponsePage(paginatedResult);

        // Then
        assertThat(responsePage).isNotNull();
        assertThat(responsePage.getContent()).hasSize(1);
        assertThat(responsePage.getContent().getFirst().getName()).isEqualTo("Test Group");
    }

    @Test
    @DisplayName("Should map CriteriaGroupWithDetailsApplicationDTO to CriteriaGroupWithDetailsResponseDTO")
    void shouldMapDetailsApplicationDtoToResponseDto() {
        // Given
        CriteriaGroupWithDetailsApplicationDTO detailsApplicationDTO = CriteriaGroupWithDetailsApplicationDTO.builder()
                .id(UUID.randomUUID())
                .name("Detailed Group")
                .displayOrder(1)
                .criteriaDetails(null)
                .build();

        // When
        CriteriaGroupWithDetailsResponseDTO responseDTO = mapper.toDetailsResponseDTO(detailsApplicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getName()).isEqualTo("Detailed Group");
    }
}
