package com.sarp.rule.application.mapper.market;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CreateMarketRequestDTO;
import com.sarp.rule.domain.command.market.CreateMarketCommand;
import com.sarp.rule.domain.entity.Port;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CreateMarketCommandMapperTest {

    private CreateMarketCommandMapper mapper;
    private String marketName;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(CreateMarketCommandMapper.class);
        marketName = "Test Market";
    }

    @Test
    @DisplayName("Should map CreateMarketRequestDTO to CreateMarketCommand")
    void toCommand_ShouldMapRequestToCommand() {
        // Given
        Set<String> portCodes = Set.of("IST", "SAW");
        CreateMarketRequestDTO requestDTO =
                new CreateMarketRequestDTO().name(marketName).portCodes(portCodes);

        // When
        CreateMarketCommand result = mapper.toCommand(requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts())
                .hasSize(2)
                .extracting(Port::getPortCode)
                .containsExactlyInAnyOrder("IST", "SAW");
    }

    @Test
    @DisplayName("Should handle null port codes in request")
    void toCommand_ShouldHandleNullPortCodes() {
        // Given
        CreateMarketRequestDTO requestDTO =
                new CreateMarketRequestDTO().name(marketName).portCodes(null);

        // When
        CreateMarketCommand result = mapper.toCommand(requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts()).isEmpty();
    }

    @Test
    @DisplayName("Should handle empty port codes in request")
    void toCommand_ShouldHandleEmptyPortCodes() {
        // Given
        CreateMarketRequestDTO requestDTO =
                new CreateMarketRequestDTO().name(marketName).portCodes(new HashSet<>());

        // When
        CreateMarketCommand result = mapper.toCommand(requestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMarket()).isNotNull();
        assertThat(result.getMarket().getName()).isEqualTo(marketName);
        assertThat(result.getMarket().getPorts()).isEmpty();
    }

    @Test
    @DisplayName("Should correctly map port codes to Port objects")
    void portCodesToPorts_ShouldMapCorrectly() {
        // Given
        Set<String> portCodes = Set.of("IST", "SAW", "ADB");

        // When
        Set<Port> result = mapper.portCodesToPorts(portCodes);

        // Then
        assertThat(result).hasSize(3).extracting(Port::getPortCode).containsExactlyInAnyOrder("IST", "SAW", "ADB");
    }

    @Test
    @DisplayName("Should return empty set for null port codes")
    void portCodesToPorts_ShouldHandleNullInput() {
        // When
        Set<Port> result = mapper.portCodesToPorts(null);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should return empty set for empty port codes")
    void portCodesToPorts_ShouldHandleEmptyInput() {
        // When
        Set<Port> result = mapper.portCodesToPorts(new HashSet<>());

        // Then
        assertThat(result).isEmpty();
    }
}
