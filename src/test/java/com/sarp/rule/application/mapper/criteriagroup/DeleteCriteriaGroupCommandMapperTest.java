package com.sarp.rule.application.mapper.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.rule.domain.command.criteriagroup.DeleteCriteriaGroupCommand;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class DeleteCriteriaGroupCommandMapperTest {

    private DeleteCriteriaGroupCommandMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(DeleteCriteriaGroupCommandMapper.class);
    }

    @Test
    @DisplayName("Should map UUID to DeleteCriteriaGroupCommand")
    void shouldMapUuidToDeleteCriteriaGroupCommand() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();

        // When
        DeleteCriteriaGroupCommand command = mapper.toCommand(criteriaGroupId);

        // Then
        assertThat(command).isNotNull();
        assertThat(command.getCriteriaGroupId()).isEqualTo(criteriaGroupId);
    }
}
