package com.sarp.rule.application.mapper.bundle;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.BundlesByIdsRequestDTO;
import com.sarp.generated.openapi.api.dto.BundlesByIdsResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.BundleMapper;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.query.bundle.BundlesByIdsQuery;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.bundle.BundleType;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class BundlesByIdsQueryMapperTest {

    private BundlesByIdsQueryMapper mapper;
    private BundleMapper bundleMapper;

    private UUID bundleId1;
    private UUID bundleId2;
    private Bundle bundle1;
    private Bundle bundle2;
    private BundlesByIdsRequestDTO requestDTO;

    @BeforeEach
    void setUp() {
        bundleMapper = Mappers.getMapper(BundleMapper.class);
        mapper = Mappers.getMapper(BundlesByIdsQueryMapper.class);
        mapper.bundleMapper = bundleMapper;

        bundleId1 = UUID.randomUUID();
        bundleId2 = UUID.randomUUID();

        // Create test bundles
        bundle1 = Bundle.builder()
                .id(bundleId1)
                .name("Test Bundle 1")
                .description("Test Description 1")
                .type(BundleType.FLIGHT_INCLUSIVE)
                .status(BundleStatus.ACTIVE)
                .build();

        bundle2 = Bundle.builder()
                .id(bundleId2)
                .name("Test Bundle 2")
                .description("Test Description 2")
                .type(BundleType.STANDALONE)
                .status(BundleStatus.ACTIVE)
                .build();

        // Create request DTO
        requestDTO = new BundlesByIdsRequestDTO();
        requestDTO.setBundleIds(List.of(bundleId1, bundleId2));
    }

    @Test
    @DisplayName("Should map BundlesByIdsRequestDTO to BundlesByIdsQuery")
    void shouldMapRequestDtoToQuery() {
        // When
        BundlesByIdsQuery query = mapper.toQuery(requestDTO);

        // Then
        assertThat(query).isNotNull();
        assertThat(query.getBundleIds()).isEqualTo(requestDTO.getBundleIds());
    }

    @Test
    @DisplayName("Should map List<Bundle> to BundlesByIdsResponseDTO")
    void shouldMapBundlesToResponseDTO() {
        // When
        BundlesByIdsResponseDTO responseDTO = mapper.toResponse(List.of(bundle1, bundle2));

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getBundles()).hasSize(2);

        var firstBundle = responseDTO.getBundles().get(0);
        assertThat(firstBundle.getId()).isEqualTo(bundleId1);
        assertThat(firstBundle.getName()).isEqualTo("Test Bundle 1");
        assertThat(firstBundle.getDescription()).isEqualTo("Test Description 1");
        assertThat(firstBundle.getType().getValue()).isEqualTo(BundleType.FLIGHT_INCLUSIVE.name());
        assertThat(firstBundle.getStatus().getValue()).isEqualTo(BundleStatus.ACTIVE.name());

        var secondBundle = responseDTO.getBundles().get(1);
        assertThat(secondBundle.getId()).isEqualTo(bundleId2);
        assertThat(secondBundle.getName()).isEqualTo("Test Bundle 2");
        assertThat(secondBundle.getDescription()).isEqualTo("Test Description 2");
        assertThat(secondBundle.getType().getValue()).isEqualTo(BundleType.STANDALONE.name());
        assertThat(secondBundle.getStatus().getValue()).isEqualTo(BundleStatus.ACTIVE.name());
    }
}
