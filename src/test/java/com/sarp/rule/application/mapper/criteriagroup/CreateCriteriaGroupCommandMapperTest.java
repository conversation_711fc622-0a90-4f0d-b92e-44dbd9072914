package com.sarp.rule.application.mapper.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupRequestDTO;
import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupResponseDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CreateCriteriaGroupCommand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CreateCriteriaGroupCommandMapperTest {

    private static final String GROUP_NAME = "Test Group";
    private CreateCriteriaGroupCommandMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(CreateCriteriaGroupCommandMapper.class);
    }

    @Test
    @DisplayName("Should map CreateCriteriaGroupRequestDTO to CreateCriteriaGroupCommand")
    void shouldMapRequestDtoToCommand() {
        // Given
        CreateCriteriaGroupRequestDTO requestDTO = new CreateCriteriaGroupRequestDTO();
        requestDTO.setName(GROUP_NAME);

        // When
        CreateCriteriaGroupCommand command = mapper.toCommand(requestDTO);

        // Then
        assertThat(command).isNotNull();
        assertThat(command.getCriteriaGroupName()).isEqualTo(GROUP_NAME);
    }

    @Test
    @DisplayName("Should map CriteriaGroupApplicationDTO to CreateCriteriaGroupResponseDTO")
    void shouldMapApplicationDtoToResponseDto() {
        // Given
        CriteriaGroupApplicationDTO applicationDTO = CriteriaGroupApplicationDTO.builder()
                .id(null)
                .name(GROUP_NAME)
                .displayOrder(null)
                .criteriaList(null)
                .build();

        // When
        CreateCriteriaGroupResponseDTO responseDTO = mapper.toResponse(applicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getName()).isEqualTo(GROUP_NAME);
    }
}
