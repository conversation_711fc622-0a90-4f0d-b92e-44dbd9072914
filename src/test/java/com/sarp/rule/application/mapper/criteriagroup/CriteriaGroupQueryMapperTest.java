package com.sarp.rule.application.mapper.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupByIdQuery;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CriteriaGroupQueryMapperTest {

    private static final UUID CRITERIA_GROUP_ID = UUID.randomUUID();
    private static final String GROUP_NAME = "Test Group";

    private CriteriaGroupQueryMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(CriteriaGroupQueryMapper.class);
    }

    @Test
    @DisplayName("Should map UUID to CriteriaGroupByIdQuery")
    void shouldMapUuidToCriteriaGroupByIdQuery() {
        // When
        CriteriaGroupByIdQuery query = mapper.toQuery(CRITERIA_GROUP_ID);

        // Then
        assertThat(query).isNotNull();
        assertThat(query.getCriteriaGroupId()).isEqualTo(CRITERIA_GROUP_ID);
    }

    @Test
    @DisplayName("Should map CriteriaGroupApplicationDTO to CriteriaGroupWithCriteriaResponseDTO")
    void shouldMapApplicationDtoToResponseDto() {
        // Given
        CriteriaGroupApplicationDTO applicationDTO = CriteriaGroupApplicationDTO.builder()
                .id(CRITERIA_GROUP_ID)
                .name(GROUP_NAME)
                .displayOrder(null)
                .criteriaList(null)
                .build();

        // When
        CriteriaGroupWithCriteriaResponseDTO responseDTO = mapper.toResponse(applicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getId()).isEqualTo(CRITERIA_GROUP_ID);
        assertThat(responseDTO.getName()).isEqualTo(GROUP_NAME);
    }
}
