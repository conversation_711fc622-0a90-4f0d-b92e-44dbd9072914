package com.sarp.rule.application.mapper.action;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.ActionResponseDTO;
import com.sarp.generated.openapi.api.dto.GetActionsByIdsRequestDTO;
import com.sarp.generated.openapi.api.dto.GetActionsByIdsResponseDTO;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.query.action.ActionsByIdsQuery;
import com.sarp.rule.domain.valueobject.action.ActionType;
import com.sarp.rule.domain.valueobject.action.Parameters;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class ActionsByIdsQueryMapperTest {

    private ActionsByIdsQueryMapper mapper;

    private UUID actionId1;
    private UUID actionId2;
    private Action action1;
    private Action action2;
    private GetActionsByIdsRequestDTO requestDTO;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(ActionsByIdsQueryMapper.class);

        actionId1 = UUID.randomUUID();
        actionId2 = UUID.randomUUID();

        // Create test actions
        action1 = Action.builder()
                .id(actionId1)
                .actionType(ActionType.PERCENTAGE_DISCOUNT)
                .parameters(Parameters.of(Map.of("discount", 10)))
                .ruleId(UUID.randomUUID())
                .build();

        action2 = Action.builder()
                .id(actionId2)
                .actionType(ActionType.FIXED_DISCOUNT)
                .parameters(Parameters.of(Map.of("amount", 100)))
                .ruleId(UUID.randomUUID())
                .build();

        // Create request DTO
        requestDTO = new GetActionsByIdsRequestDTO();
        requestDTO.setActionIds(List.of(actionId1, actionId2));
    }

    @Test
    @DisplayName("Should map GetActionsByIdsRequestDTO to GetActionsByIdsQuery")
    void shouldMapRequestDtoToQuery() {
        // When
        ActionsByIdsQuery query = mapper.toQuery(requestDTO);

        // Then
        assertThat(query).isNotNull();
        assertThat(query.getActionIds()).isEqualTo(requestDTO.getActionIds());
    }

    @Test
    @DisplayName("Should map Action to ActionResponseDTO")
    void shouldMapActionToActionResponseDTO() {
        // When
        ActionResponseDTO responseDTO = mapper.toActionResponseDTO(action1);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getId()).isEqualTo(actionId1);
        assertThat(responseDTO.getActionType().getValue()).isEqualTo(ActionType.PERCENTAGE_DISCOUNT.name());
        assertThat(responseDTO.getParameters()).isEqualTo(Map.of("discount", 10));
        assertThat(responseDTO.getRuleId()).isEqualTo(action1.getRuleId());
    }

    @Test
    @DisplayName("Should map List<Action> to GetActionsByIdsResponseDTO")
    void shouldMapActionsToResponseDTO() {
        // When
        GetActionsByIdsResponseDTO responseDTO = mapper.toResponse(List.of(action1, action2));

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getActions()).hasSize(2);

        ActionResponseDTO firstAction = responseDTO.getActions().get(0);
        assertThat(firstAction.getId()).isEqualTo(actionId1);
        assertThat(firstAction.getActionType().getValue()).isEqualTo(ActionType.PERCENTAGE_DISCOUNT.name());
        assertThat(firstAction.getParameters()).isEqualTo(Map.of("discount", 10));
        assertThat(firstAction.getRuleId()).isEqualTo(action1.getRuleId());

        ActionResponseDTO secondAction = responseDTO.getActions().get(1);
        assertThat(secondAction.getId()).isEqualTo(actionId2);
        assertThat(secondAction.getActionType().getValue()).isEqualTo(ActionType.FIXED_DISCOUNT.name());
        assertThat(secondAction.getParameters()).isEqualTo(Map.of("amount", 100));
        assertThat(secondAction.getRuleId()).isEqualTo(action2.getRuleId());
    }
}
