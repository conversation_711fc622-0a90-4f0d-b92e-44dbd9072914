package com.sarp.rule.application.mapper.criteria;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaRequestDTO;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.command.criteria.UpdateCriteriaCommand;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class UpdateCriteriaCommandMapperTest {

    private static final UUID CRITERIA_ID = UUID.randomUUID();
    private static final String CRITERIA_NAME = "Updated Criteria";
    private static final String CRITERIA_DESCRIPTION = "Updated Description";
    private static final Double MIN_VALUE_DOUBLE = 1.0;
    private static final Double MAX_VALUE_DOUBLE = 100.0;
    private static final BigDecimal MIN_VALUE = BigDecimal.valueOf(MIN_VALUE_DOUBLE);
    private static final BigDecimal MAX_VALUE = BigDecimal.valueOf(MAX_VALUE_DOUBLE);

    private UpdateCriteriaCommandMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(UpdateCriteriaCommandMapper.class);
    }

    @Test
    @DisplayName("Should map UpdateCriteriaRequestDTO to UpdateCriteriaCommand")
    void shouldMapRequestDtoToCommand() {
        // Given
        UpdateCriteriaRequestDTO requestDTO = createRequestDTO();

        // When
        UpdateCriteriaCommand command = mapper.toCommand(CRITERIA_ID, requestDTO);

        // Then
        assertThat(command).isNotNull();
        assertThat(command.getId()).isEqualTo(CRITERIA_ID);
        assertThat(command.getName()).isEqualTo(CRITERIA_NAME);
        assertThat(command.getDescription()).isEqualTo(CRITERIA_DESCRIPTION);
    }

    @Test
    @DisplayName("Should map CriteriaApplicationDTO to CriteriaResponseDTO")
    void shouldMapApplicationDtoToResponseDto() {
        // Given
        CriteriaApplicationDTO applicationDTO = createApplicationDTO();

        // When
        CriteriaResponseDTO responseDTO = mapper.toResponse(applicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getId()).isEqualTo(CRITERIA_ID.toString());
        assertThat(responseDTO.getName()).isEqualTo(CRITERIA_NAME);
        assertThat(responseDTO.getDescription()).isEqualTo(CRITERIA_DESCRIPTION);
        assertThat(responseDTO.getMinValue()).isEqualTo(MIN_VALUE_DOUBLE);
        assertThat(responseDTO.getMaxValue()).isEqualTo(MAX_VALUE_DOUBLE);
    }

    private UpdateCriteriaRequestDTO createRequestDTO() {
        UpdateCriteriaRequestDTO requestDTO = new UpdateCriteriaRequestDTO();
        requestDTO.setName(CRITERIA_NAME);
        requestDTO.setDescription(CRITERIA_DESCRIPTION);
        return requestDTO;
    }

    private CriteriaApplicationDTO createApplicationDTO() {
        return CriteriaApplicationDTO.builder()
                .id(CRITERIA_ID)
                .name(CRITERIA_NAME)
                .description(CRITERIA_DESCRIPTION)
                .type("USER_DEFINED")
                .requestType("OFFER_REQUEST")
                .mappingField("testMappingField")
                .fieldType("INTEGER")
                .selectionType(null)
                .allowedValues(null)
                .minValue(MIN_VALUE)
                .maxValue(MAX_VALUE)
                .criteriaGroupId(null)
                .build();
    }
}
