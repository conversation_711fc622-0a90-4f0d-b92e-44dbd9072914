package com.sarp.rule.application.mapper.criteria;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CreateCriteriaRequestDTO;
import com.sarp.generated.openapi.api.dto.CriteriaResponseDTO;
import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.domain.command.criteria.CreateCriteriaCommand;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CreateCriteriaCommandMapperTest {

    private static final String CRITERIA_NAME = "Test Criteria";
    private static final String CRITERIA_DESCRIPTION = "Test Description";
    private static final Double MIN_VALUE_DOUBLE = 1.0;
    private static final Double MAX_VALUE_DOUBLE = 100.0;
    private static final BigDecimal MIN_VALUE = BigDecimal.valueOf(MIN_VALUE_DOUBLE);
    private static final BigDecimal MAX_VALUE = BigDecimal.valueOf(MAX_VALUE_DOUBLE);
    private static final UUID CRITERIA_ID = UUID.randomUUID();

    private CreateCriteriaCommandMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(CreateCriteriaCommandMapper.class);
    }

    @Test
    @DisplayName("Should map CreateCriteriaRequestDTO to CreateCriteriaCommand")
    void shouldMapRequestDtoToCommand() {
        // Given
        CreateCriteriaRequestDTO requestDTO = createRequestDTO();

        // When
        CreateCriteriaCommand command = mapper.toCommand(requestDTO);

        // Then
        assertThat(command).isNotNull();
        assertThat(command.getCriteria()).isNotNull();
        assertThat(command.getCriteria().getName()).isEqualTo(CRITERIA_NAME);
        assertThat(command.getCriteria().getDescription()).isEqualTo(CRITERIA_DESCRIPTION);
        assertThat(command.getCriteria().getMinValue()).isEqualByComparingTo(MIN_VALUE);
        assertThat(command.getCriteria().getMaxValue()).isEqualByComparingTo(MAX_VALUE);
    }

    @Test
    @DisplayName("Should map CriteriaApplicationDTO to CriteriaResponseDTO")
    void shouldMapApplicationDtoToResponseDto() {
        // Given
        CriteriaApplicationDTO applicationDTO = createApplicationDTO();

        // When
        CriteriaResponseDTO responseDTO = mapper.toResponse(applicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getId()).isEqualTo(CRITERIA_ID.toString());
        assertThat(responseDTO.getName()).isEqualTo(CRITERIA_NAME);
        assertThat(responseDTO.getDescription()).isEqualTo(CRITERIA_DESCRIPTION);
        assertThat(responseDTO.getMinValue()).isEqualTo(MIN_VALUE_DOUBLE);
        assertThat(responseDTO.getMaxValue()).isEqualTo(MAX_VALUE_DOUBLE);
    }

    private CreateCriteriaRequestDTO createRequestDTO() {
        CreateCriteriaRequestDTO requestDTO = new CreateCriteriaRequestDTO();
        requestDTO.setName(CRITERIA_NAME);
        requestDTO.setDescription(CRITERIA_DESCRIPTION);
        requestDTO.setMappingField("testMappingField");
        requestDTO.setFieldType(CreateCriteriaRequestDTO.FieldTypeEnum.INTEGER);
        requestDTO.setMinValue(MIN_VALUE_DOUBLE);
        requestDTO.setMaxValue(MAX_VALUE_DOUBLE);
        return requestDTO;
    }

    private CriteriaApplicationDTO createApplicationDTO() {
        return CriteriaApplicationDTO.builder()
                .id(CRITERIA_ID)
                .name(CRITERIA_NAME)
                .description(CRITERIA_DESCRIPTION)
                .minValue(MIN_VALUE)
                .maxValue(MAX_VALUE)
                .build();
    }
}
