package com.sarp.rule.application.mapper.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CriteriaGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.UpdateCriteriaGroupRequestDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.domain.command.criteriagroup.UpdateCriteriaGroupCommand;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class UpdateCriteriaGroupCommandMapperTest {

    private static final UUID CRITERIA_GROUP_ID = UUID.randomUUID();
    private static final String UPDATED_NAME = "Updated Group Name";

    private UpdateCriteriaGroupCommandMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(UpdateCriteriaGroupCommandMapper.class);
    }

    @Test
    @DisplayName("Should map UUID and UpdateCriteriaGroupRequestDTO to UpdateCriteriaGroupCommand")
    void shouldMapUuidAndRequestDtoToCommand() {
        // Given
        UpdateCriteriaGroupRequestDTO requestDTO = new UpdateCriteriaGroupRequestDTO();
        requestDTO.setName(UPDATED_NAME);

        // When
        UpdateCriteriaGroupCommand command = mapper.toCommand(CRITERIA_GROUP_ID, requestDTO);

        // Then
        assertThat(command).isNotNull();
        assertThat(command.getCriteriaGroupId()).isEqualTo(CRITERIA_GROUP_ID);
        assertThat(command.getName()).isEqualTo(UPDATED_NAME);
    }

    @Test
    @DisplayName("Should map CriteriaGroupApplicationDTO to CriteriaGroupResponseDTO")
    void shouldMapApplicationDtoToResponseDto() {
        // Given
        CriteriaGroupApplicationDTO applicationDTO = CriteriaGroupApplicationDTO.builder()
                .id(CRITERIA_GROUP_ID)
                .name(UPDATED_NAME)
                .displayOrder(null)
                .criteriaList(null)
                .build();

        // When
        CriteriaGroupResponseDTO responseDTO = mapper.toResponse(applicationDTO);

        // Then
        assertThat(responseDTO).isNotNull();
        assertThat(responseDTO.getId()).isEqualTo(CRITERIA_GROUP_ID);
        assertThat(responseDTO.getName()).isEqualTo(UPDATED_NAME);
    }
}
