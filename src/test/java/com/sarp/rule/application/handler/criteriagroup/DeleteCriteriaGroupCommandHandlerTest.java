package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.handler.criteria.CountCriteriaByCriteriaGroupIdQueryHandler;
import com.sarp.rule.domain.command.criteriagroup.DeleteCriteriaGroupCommand;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.validator.CriteriaGroupDeletionValidator;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeleteCriteriaGroupCommandHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupDeletionValidator criteriaGroupDeletionValidator;

    @Mock
    private CountCriteriaByCriteriaGroupIdQueryHandler countCriteriaByCriteriaGroupIdQueryHandler;

    @InjectMocks
    private DeleteCriteriaGroupCommandHandler handler;

    @Test
    @DisplayName("Should delete criteria group when it exists and is deletable")
    void shouldDeleteCriteriaGroupWhenExistsAndDeletable() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        DeleteCriteriaGroupCommand command = new DeleteCriteriaGroupCommand(criteriaGroupId);
        long associatedCriteriaCount = 0;

        when(criteriaGroupRepository.existsById(criteriaGroupId)).thenReturn(true);
        when(countCriteriaByCriteriaGroupIdQueryHandler.handle(
                        argThat(q -> q.getCriteriaGroupId().equals(criteriaGroupId))))
                .thenReturn(associatedCriteriaCount);

        // When
        handler.handle(command);

        // Then
        verify(criteriaGroupRepository).existsById(criteriaGroupId);
        verify(countCriteriaByCriteriaGroupIdQueryHandler)
                .handle(argThat(q -> q.getCriteriaGroupId().equals(criteriaGroupId)));
        verify(criteriaGroupDeletionValidator).ensureDeletable(criteriaGroupId, associatedCriteriaCount);
        verify(criteriaGroupRepository).deleteById(criteriaGroupId);
    }

    @Test
    @DisplayName("Should throw CriteriaGroupNotFoundException when criteria group does not exist")
    void shouldThrowCriteriaGroupNotFoundExceptionWhenCriteriaGroupDoesNotExist() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        DeleteCriteriaGroupCommand command = new DeleteCriteriaGroupCommand(criteriaGroupId);

        when(criteriaGroupRepository.existsById(criteriaGroupId)).thenReturn(false);

        // When / Then
        assertThatThrownBy(() -> handler.handle(command))
                .isInstanceOf(CriteriaGroupNotFoundException.class)
                .hasMessageContaining("criteria_group_not_found");
        verify(criteriaGroupRepository).existsById(criteriaGroupId);
        verifyNoInteractions(countCriteriaByCriteriaGroupIdQueryHandler, criteriaGroupDeletionValidator);
    }
}
