package com.sarp.rule.application.handler.criteria;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.command.criteria.UpdateCriteriaCommand;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.repository.CriteriaRepository;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateCriteriaCommandHandlerTest {

    @Mock
    private CriteriaRepository criteriaRepository;

    @Mock
    private CriteriaApplicationDTOMapper criteriaApplicationDTOMapper;

    @InjectMocks
    private UpdateCriteriaCommandHandler handler;

    @Test
    @DisplayName("Should handle UpdateCriteriaCommand and update criteria")
    void shouldHandleUpdateCriteriaCommand() {
        // Given
        UpdateCriteriaCommand command = createUpdateCommand();
        Criteria existingCriteria = createExistingCriteria();
        Criteria updatedCriteria = createUpdatedCriteria();
        CriteriaApplicationDTO expectedDto =
                CriteriaApplicationDTO.builder().name("Updated Criteria").build();

        when(criteriaRepository.findById(command.getId())).thenReturn(Optional.of(existingCriteria));
        when(criteriaRepository.update(any(Criteria.class))).thenReturn(updatedCriteria);
        when(criteriaApplicationDTOMapper.toDTO(updatedCriteria)).thenReturn(expectedDto);

        // When
        CriteriaApplicationDTO result = handler.handle(command);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Updated Criteria");
        verify(criteriaRepository).findById(command.getId());
        verify(criteriaRepository).update(any(Criteria.class));
        verify(criteriaApplicationDTOMapper).toDTO(updatedCriteria);
    }

    @Test
    @DisplayName("Should throw CriteriaNotFoundException when criteria does not exist")
    void shouldThrowCriteriaNotFoundException() {
        // Given
        UpdateCriteriaCommand command = createUpdateCommand();

        when(criteriaRepository.findById(command.getId())).thenReturn(Optional.empty());

        // When / Then
        assertThatThrownBy(() -> handler.handle(command)).isInstanceOf(CriteriaNotFoundException.class);
        verify(criteriaRepository).findById(command.getId());
        verifyNoMoreInteractions(criteriaRepository);
    }

    private UpdateCriteriaCommand createUpdateCommand() {
        return new UpdateCriteriaCommand(UUID.randomUUID(), "Updated Criteria", "Updated Description");
    }

    private Criteria createExistingCriteria() {
        return Criteria.builder()
                .id(UUID.randomUUID())
                .name("Existing Criteria")
                .description("Existing Description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .build();
    }

    private Criteria createUpdatedCriteria() {
        return Criteria.builder()
                .id(UUID.randomUUID())
                .name("Updated Criteria")
                .description("Updated Description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .build();
    }
}
