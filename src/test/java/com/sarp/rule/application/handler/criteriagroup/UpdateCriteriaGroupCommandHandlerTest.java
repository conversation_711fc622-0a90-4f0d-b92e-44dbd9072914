package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.UpdateCriteriaGroupCommand;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateCriteriaGroupCommandHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @InjectMocks
    private UpdateCriteriaGroupCommandHandler handler;

    @BeforeEach
    void setUp() {
        handler = new UpdateCriteriaGroupCommandHandler(criteriaGroupRepository, criteriaGroupApplicationDTOMapper);
    }

    @Test
    @DisplayName("Should update criteria group successfully")
    void shouldUpdateCriteriaGroupSuccessfully() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        UpdateCriteriaGroupCommand command = new UpdateCriteriaGroupCommand(criteriaGroupId, "UpdatedName", 1);

        CriteriaGroup existingGroup = CriteriaGroup.builder()
                .id(criteriaGroupId)
                .name("OldName")
                .displayOrder(1)
                .build();

        CriteriaGroup updatedGroup = CriteriaGroup.builder()
                .id(criteriaGroupId)
                .name("UpdatedName")
                .displayOrder(1)
                .build();

        when(criteriaGroupRepository.findByIdWithoutCriteriaList(criteriaGroupId))
                .thenReturn(Optional.of(existingGroup));
        when(criteriaGroupRepository.save(any(CriteriaGroup.class))).thenReturn(updatedGroup);

        when(criteriaGroupApplicationDTOMapper.toDTO(any(CriteriaGroup.class))).thenAnswer(invocation -> {
            CriteriaGroup group = invocation.getArgument(0);
            return CriteriaGroupApplicationDTO.builder()
                    .id(group.getId())
                    .name(group.getName())
                    .displayOrder(group.getDisplayOrder())
                    .build();
        });

        // When
        CriteriaGroupApplicationDTO result = handler.handle(command);

        // Then
        verify(criteriaGroupRepository).findByIdWithoutCriteriaList(criteriaGroupId);
        verify(criteriaGroupRepository)
                .save(argThat(group -> group.getName().equals("UpdatedName") && group.getDisplayOrder() == 1));
        assertThat(result.getName()).isEqualTo("UpdatedName");
        assertThat(result.getDisplayOrder()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should throw CriteriaGroupNotFoundException when criteria group does not exist")
    void shouldThrowCriteriaGroupNotFoundExceptionWhenCriteriaGroupDoesNotExist() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        UpdateCriteriaGroupCommand command = new UpdateCriteriaGroupCommand(criteriaGroupId, "UpdatedName", 1);

        when(criteriaGroupRepository.findByIdWithoutCriteriaList(criteriaGroupId))
                .thenReturn(Optional.empty());

        // When / Then
        assertThatThrownBy(() -> handler.handle(command))
                .isInstanceOf(CriteriaGroupNotFoundException.class)
                .hasMessageContaining("criteria_group_not_found");
        verify(criteriaGroupRepository).findByIdWithoutCriteriaList(criteriaGroupId);
        verifyNoMoreInteractions(criteriaGroupRepository);
    }
}
