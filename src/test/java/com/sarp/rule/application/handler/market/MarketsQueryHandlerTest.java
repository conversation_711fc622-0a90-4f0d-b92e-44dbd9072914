package com.sarp.rule.application.handler.market;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.query.market.MarketsQuery;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

@ExtendWith(MockitoExtension.class)
class MarketsQueryHandlerTest {

    @Mock
    private MarketRepository marketRepository;

    @Mock
    private MarketMapper marketMapper;

    @InjectMocks
    private MarketsQueryHandler marketsQueryHandler;

    private Market market1;
    private Market market2;
    private MarketResponseDTO marketResponseDTO1;
    private MarketResponseDTO marketResponseDTO2;
    private MarketsQuery query;
    private PagingParams pagingParams;
    private List<Market> marketList;

    @BeforeEach
    void setUp() {
        // Setup test data
        Port port = Port.builder().portCode("IST").portName("Istanbul").build();

        market1 = Market.builder()
                .id(UUID.randomUUID())
                .name("Market 1")
                .ports(new HashSet<>(List.of(port)))
                .build();

        market2 = Market.builder()
                .id(UUID.randomUUID())
                .name("Market 2")
                .ports(new HashSet<>(List.of(port)))
                .build();

        marketResponseDTO1 = new MarketResponseDTO();
        marketResponseDTO1.setName("Market 1");

        marketResponseDTO2 = new MarketResponseDTO();
        marketResponseDTO2.setName("Market 2");

        marketList = Arrays.asList(market1, market2);

        pagingParams = PagingParams.of(0, 10);
        query = MarketsQuery.builder().pagingParams(pagingParams).build();
    }

    @Test
    @DisplayName("Should return paginated market list")
    void handle_ShouldReturnPaginatedMarketList() {
        // Given
        PaginatedResult<Market> paginatedResult =
                new PaginatedResult<>(marketList, pagingParams.getPageNumber(), pagingParams.getPageSize(), 2L, 1);

        when(marketRepository.findAllMarkets(pagingParams)).thenReturn(paginatedResult);
        when(marketMapper.toMarketResponseDTO(market1)).thenReturn(marketResponseDTO1);
        when(marketMapper.toMarketResponseDTO(market2)).thenReturn(marketResponseDTO2);

        // When
        Page<MarketResponseDTO> result = marketsQueryHandler.handle(query);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(2).containsExactly(marketResponseDTO1, marketResponseDTO2);
        assertThat(result.getTotalElements()).isEqualTo(2L);
        assertThat(result.getNumber()).isZero();
        assertThat(result.getSize()).isEqualTo(10);

        verify(marketRepository).findAllMarkets(pagingParams);
        verify(marketMapper).toMarketResponseDTO(market1);
        verify(marketMapper).toMarketResponseDTO(market2);
    }

    @Test
    @DisplayName("Should return empty page when no markets found")
    void handle_ShouldReturnEmptyPage_WhenNoMarketsFound() {
        // Given
        PaginatedResult<Market> emptyResult =
                new PaginatedResult<>(List.of(), pagingParams.getPageNumber(), pagingParams.getPageSize(), 0L, 0);

        when(marketRepository.findAllMarkets(pagingParams)).thenReturn(emptyResult);

        // When
        Page<MarketResponseDTO> result = marketsQueryHandler.handle(query);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEmpty();
        assertThat(result.getTotalElements()).isZero();
        assertThat(result.getNumber()).isZero();
        assertThat(result.getSize()).isEqualTo(10);

        verify(marketRepository).findAllMarkets(pagingParams);
        verifyNoInteractions(marketMapper);
    }
}
