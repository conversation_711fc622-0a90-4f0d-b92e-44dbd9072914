package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteria.CriteriaWithConfigDetailsApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupWithDetailsApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupWithDetailsApplicationMapper;
import com.sarp.rule.domain.query.criteriagroup.CriteriaGroupWithDetailQuery;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.CriteriaWithConfigDetails;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import com.sarp.rule.domain.valueobject.criteria.SelectionType;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaGroupDetailQueryHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupWithDetailsApplicationMapper criteriaGroupWithDetailsApplicationDTOMapper;

    private CriteriaGroupDetailQueryHandler handler;

    @BeforeEach
    void setUp() {
        handler = new CriteriaGroupDetailQueryHandler(
                criteriaGroupRepository, criteriaGroupWithDetailsApplicationDTOMapper);
    }

    @Test
    @DisplayName("Should handle CriteriaGroupWithDetailQuery and return paginated results")
    void shouldHandleCriteriaGroupWithDetailQuery() {
        // Given
        RuleType ruleType = RuleType.BUNDLE;
        PagingParams pagingParams = PagingParams.of(1, 10);
        CriteriaGroupWithDetailQuery query = new CriteriaGroupWithDetailQuery(ruleType, pagingParams);
        CriteriaWithConfigDetails criteriaWithConfigDetails = createCriteriaWithConfigDetails();

        CriteriaGroupWithDetails criteriaGroupWithDetails =
                new CriteriaGroupWithDetails(UUID.randomUUID(), "GroupName", 1, List.of(criteriaWithConfigDetails));
        PaginatedResult<CriteriaGroupWithDetails> paginatedResult =
                new PaginatedResult<>(List.of(criteriaGroupWithDetails), 1, 10, 1, 1);

        when(criteriaGroupRepository.findAllWithDetailsByRuleType(ruleType, pagingParams))
                .thenReturn(paginatedResult);

        when(criteriaGroupWithDetailsApplicationDTOMapper.toDTO(any(CriteriaGroupWithDetails.class)))
                .thenAnswer(invocation -> {
                    CriteriaGroupWithDetails input = invocation.getArgument(0);
                    return CriteriaGroupWithDetailsApplicationDTO.builder()
                            .id(input.id())
                            .name(input.name())
                            .displayOrder(input.displayOrder())
                            .criteriaDetails(input.criteriaDetails().stream()
                                    .map(this::toCriteriaWithConfigDetailsApplicationDTO)
                                    .toList())
                            .build();
                });

        // When
        PaginatedResultApplicationDTO<CriteriaGroupWithDetailsApplicationDTO> result = handler.handle(query);

        // Then
        verify(criteriaGroupRepository).findAllWithDetailsByRuleType(ruleType, pagingParams);
        assertThat(result.getItems()).hasSize(1);
        assertThat(result.getItems().getFirst().getName()).isEqualTo("GroupName");
        assertThat(result.getCurrentPage()).isEqualTo(1);
        assertThat(result.getPageSize()).isEqualTo(10);

        CriteriaWithConfigDetailsApplicationDTO resultDetailsDTO =
                result.getItems().getFirst().getCriteriaDetails().getFirst();
        assertThat(resultDetailsDTO.getCriteriaName()).isEqualTo("CriteriaName");
        assertThat(resultDetailsDTO.getDescription()).isEqualTo("Description");
        assertThat(resultDetailsDTO.getType()).isEqualTo(CriteriaType.USER_DEFINED);
        assertThat(resultDetailsDTO.getRequestType()).isEqualTo(RequestType.OFFER_REQUEST);
        assertThat(resultDetailsDTO.getMappingField()).isEqualTo("test.mappingField");
        assertThat(resultDetailsDTO.getFieldType()).isEqualTo(FieldType.LIST);
        assertThat(resultDetailsDTO.getSelectionType()).isEqualTo(SelectionType.MULTI_SELECT);
        assertThat(resultDetailsDTO.getAllowedValues()).isEqualTo(List.of("AllowedValues"));
        assertThat(resultDetailsDTO.getMinValue()).isEqualTo(BigInteger.valueOf(1));
        assertThat(resultDetailsDTO.getMaxValue()).isEqualTo(BigInteger.valueOf(100));
        assertThat(resultDetailsDTO.getAllowedOperators()).isEqualTo(List.of(ComparisonOperator.CONTAINS_ALL));
        assertThat(resultDetailsDTO.getAllowedRules()).containsExactly(RuleType.BUNDLE.name());
        assertThat(resultDetailsDTO.getMandatoryRules()).containsExactly(RuleType.BUNDLE.name());
    }

    private CriteriaWithConfigDetails createCriteriaWithConfigDetails() {
        return new CriteriaWithConfigDetails(
                UUID.randomUUID(),
                "CriteriaName",
                "Description",
                CriteriaType.USER_DEFINED,
                RequestType.OFFER_REQUEST,
                "test.mappingField",
                FieldType.LIST,
                SelectionType.MULTI_SELECT,
                List.of("AllowedValues"), // Changed from String to List<String>
                BigInteger.valueOf(1),
                BigInteger.valueOf(100),
                List.of(ComparisonOperator.CONTAINS_ALL),
                LocalDateTime.now(), // Changed from Instant to LocalDateTime
                LocalDateTime.now().plusDays(1), // Added endDateTime
                LocalTime.of(0, 0), // Added startTime
                LocalTime.of(23, 59), // Added endTime
                1,
                List.of(RuleType.BUNDLE),
                List.of(RuleType.BUNDLE));
    }

    // This helper now uses the builder pattern
    private CriteriaWithConfigDetailsApplicationDTO toCriteriaWithConfigDetailsApplicationDTO(
            CriteriaWithConfigDetails details) {
        return CriteriaWithConfigDetailsApplicationDTO.builder()
                .criteriaId(details.criteriaId())
                .criteriaName(details.criteriaName())
                .description(details.description())
                .type(details.type())
                .requestType(details.requestType())
                .mappingField(details.mappingField())
                .fieldType(details.fieldType())
                .selectionType(details.selectionType())
                .allowedValues(details.allowedValues())
                .minValue(details.minValue())
                .maxValue(details.maxValue())
                .allowedOperators(details.allowedOperators())
                .displayOrder(details.displayOrder())
                .allowedRules(
                        details.allowedRules().stream().map(RuleType::name).toList())
                .mandatoryRules(
                        details.mandatoryRules().stream().map(RuleType::name).toList())
                .build();
    }
}
