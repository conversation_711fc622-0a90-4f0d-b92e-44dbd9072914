package com.sarp.rule.application.handler.market;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.application.service.MarketEventService;
import com.sarp.rule.domain.command.market.CreateMarketCommand;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.event.market.MarketCreatedEvent;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.service.PortVerifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreateMarketCommandHandlerTest {

    @Mock
    private MarketRepository marketRepository;

    @Mock
    private PortRepository portRepository;

    @Mock
    private MarketMapper marketMapper;

    @Mock
    private MarketEventService marketEventService;

    @Mock
    private PortVerifier portVerifier;

    @InjectMocks
    private CreateMarketCommandHandler createMarketCommandHandler;

    private CreateMarketCommand command;
    private Market market;
    private Port port1;
    private Port port2;
    private List<String> portCodes;
    private Set<Port> ports;
    private Market savedMarket;
    private MarketResponseDTO expectedResponse;

    @BeforeEach
    void setUp() {
        // Setup test data
        port1 = Port.builder().portCode("IST").portName("Istanbul").build();

        port2 = Port.builder().portCode("SAW").portName("Sabiha").build();

        portCodes = Arrays.asList("IST", "SAW");
        ports = new HashSet<>(Arrays.asList(port1, port2));

        market = Market.builder().name("Test Market").ports(ports).build();

        savedMarket = Market.builder()
                .id(UUID.randomUUID())
                .name("Test Market")
                .ports(ports)
                .build();

        command = new CreateMarketCommand(market);
        expectedResponse = new MarketResponseDTO();
        expectedResponse.setName("Test Market");
    }

    @Test
    @DisplayName("Should successfully create a market")
    void handle_ShouldCreateMarket() {
        // Given
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(savedMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);
        doNothing().when(marketEventService).sendMarketCreatedEventAfterCommit(any(MarketCreatedEvent.class));
        doNothing().when(portVerifier).verifyPortExists(anyList(), anySet());

        // When
        MarketResponseDTO result = createMarketCommandHandler.handle(command);

        // Then
        assertThat(result).isNotNull().isEqualTo(expectedResponse);

        verify(portRepository).findAllByPortCodeIn(portCodes);
        verify(portVerifier).verifyPortExists(eq(portCodes), any());
        verify(marketRepository).save(any(Market.class));
        verify(marketEventService).sendMarketCreatedEventAfterCommit(any(MarketCreatedEvent.class));
        verify(marketMapper).toMarketResponseDTO(any(Market.class));
    }

    @Test
    @DisplayName("Should verify ports exist when creating market")
    void handle_ShouldVerifyPortsExist() {
        // Given
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(savedMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);

        // When
        createMarketCommandHandler.handle(command);

        // Then
        verify(portVerifier).verifyPortExists(eq(portCodes), any());
    }

    @Test
    @DisplayName("Should send market created event after saving")
    void handle_ShouldSendMarketCreatedEvent() {
        // Given
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(savedMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);

        // When
        createMarketCommandHandler.handle(command);

        // Then
        verify(marketEventService)
                .sendMarketCreatedEventAfterCommit(
                        argThat(event -> event.getMarketId().equals(savedMarket.getId())
                                && event.getMarketName().equals(savedMarket.getName())
                                && event.getPortCodes().containsAll(portCodes)));
    }
}
