package com.sarp.rule.application.handler.criteria;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sarp.rule.application.handler.condition.ConditionExistByCriteriaIdQueryHandler;
import com.sarp.rule.domain.query.condition.ConditionExistsByCriteriaIdQuery;
import com.sarp.rule.domain.repository.ConditionRepository;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ConditionExistByCriteriaIdQueryHandlerTest {

    @Mock
    private ConditionRepository conditionRepository;

    @InjectMocks
    private ConditionExistByCriteriaIdQueryHandler handler;

    @Test
    @DisplayName("Should return true when condition exists for criteria ID")
    void shouldReturnTrueWhenConditionExists() {
        // Given
        UUID criteriaId = UUID.randomUUID();
        ConditionExistsByCriteriaIdQuery query = ConditionExistsByCriteriaIdQuery.of(criteriaId);

        when(conditionRepository.existsByCriteriaId(criteriaId)).thenReturn(true);

        // When
        boolean result = handler.handle(query);

        // Then
        assertThat(result).isTrue();
        verify(conditionRepository).existsByCriteriaId(criteriaId);
    }

    @Test
    @DisplayName("Should return false when condition does not exist for criteria ID")
    void shouldReturnFalseWhenConditionDoesNotExist() {
        // Given
        UUID criteriaId = UUID.randomUUID();
        ConditionExistsByCriteriaIdQuery query = ConditionExistsByCriteriaIdQuery.of(criteriaId);

        when(conditionRepository.existsByCriteriaId(criteriaId)).thenReturn(false);

        // When
        boolean result = handler.handle(query);

        // Then
        assertThat(result).isFalse();
        verify(conditionRepository).existsByCriteriaId(criteriaId);
    }
}
