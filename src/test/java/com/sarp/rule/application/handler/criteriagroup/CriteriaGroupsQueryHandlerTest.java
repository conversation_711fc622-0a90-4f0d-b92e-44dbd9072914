package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.PaginatedResultApplicationDTO;
import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsByIdsQuery;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupsQuery;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaGroupsQueryHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @InjectMocks
    private CriteriaGroupsQueryHandler handler;

    @BeforeEach
    void setUp() {
        handler = new CriteriaGroupsQueryHandler(criteriaGroupRepository, criteriaGroupApplicationDTOMapper);
    }

    @Test
    @DisplayName("Should handle CriteriaGroupsQuery and return paginated results")
    void shouldHandleCriteriaGroupsQuery() {
        // Given
        PagingParams pagingParams = PagingParams.of(0, 10);
        CriteriaGroupsQuery query = new CriteriaGroupsQuery(pagingParams);

        CriteriaGroup criteriaGroup = CriteriaGroup.builder()
                .id(UUID.randomUUID()) // It's good practice to include the ID
                .name("GroupName")
                .displayOrder(1)
                .build();

        PaginatedResult<CriteriaGroup> paginatedResult = new PaginatedResult<>(List.of(criteriaGroup), 0, 10, 1, 1);

        when(criteriaGroupRepository.findAllWithCriteriaDetails(pagingParams)).thenReturn(paginatedResult);

        when(criteriaGroupApplicationDTOMapper.toDTO(any(CriteriaGroup.class))).thenAnswer(invocation -> {
            CriteriaGroup group = invocation.getArgument(0);
            return CriteriaGroupApplicationDTO.builder()
                    .id(group.getId())
                    .name(group.getName())
                    .displayOrder(group.getDisplayOrder())
                    .criteriaList(Collections.emptyList())
                    .build();
        });

        // When
        PaginatedResultApplicationDTO<CriteriaGroupApplicationDTO> result = handler.handle(query);

        // Then
        verify(criteriaGroupRepository).findAllWithCriteriaDetails(pagingParams);
        assertThat(result.getItems()).hasSize(1);
        assertThat(result.getItems().getFirst().getName()).isEqualTo("GroupName");
        assertThat(result.getTotalElements()).isEqualTo(1);
        assertThat(result.getPageSize()).isEqualTo(10);
        // The current page should reflect the input paging params
        assertThat(result.getCurrentPage()).isZero();
    }

    @Test
    @DisplayName("Should handle CriteriaGroupsByIdsQuery and return criteria groups")
    void shouldHandleCriteriaGroupsByIdsQuery() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        CriteriaGroupsByIdsQuery query = new CriteriaGroupsByIdsQuery(List.of(criteriaGroupId));

        CriteriaGroup criteriaGroup = CriteriaGroup.builder()
                .id(criteriaGroupId)
                .name("GroupName")
                .displayOrder(1)
                .build();

        when(criteriaGroupRepository.findAllById(List.of(criteriaGroupId))).thenReturn(List.of(criteriaGroup));

        // When
        List<CriteriaGroup> result = handler.handle(query);

        // Then
        verify(criteriaGroupRepository).findAllById(List.of(criteriaGroupId));
        assertThat(result).hasSize(1);
        assertThat(result.getFirst().getName()).isEqualTo("GroupName");
        assertThat(result.getFirst().getId()).isEqualTo(criteriaGroupId);
    }
}
