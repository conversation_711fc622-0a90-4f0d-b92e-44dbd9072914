package com.sarp.rule.application.handler.criteria;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.handler.condition.ConditionExistByCriteriaIdQueryHandler;
import com.sarp.rule.domain.command.criteria.DeleteCriteriaCommand;
import com.sarp.rule.domain.exception.CriteriaNotFoundException;
import com.sarp.rule.domain.repository.CriteriaRepository;
import com.sarp.rule.domain.validator.CriteriaDeletionValidator;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeleteCriteriaCommandHandlerTest {

    @Mock
    private CriteriaRepository criteriaRepository;

    @Mock
    private CriteriaDeletionValidator criteriaDeletionValidator;

    @Mock
    private ConditionExistByCriteriaIdQueryHandler conditionExistByCriteriaIdQueryHandler;

    @InjectMocks
    private DeleteCriteriaCommandHandler handler;

    @Test
    @DisplayName("Should handle DeleteCriteriaCommand and delete criteria")
    void shouldHandleDeleteCriteriaCommand() {
        // Given
        UUID criteriaId = UUID.randomUUID();
        DeleteCriteriaCommand command = new DeleteCriteriaCommand(criteriaId);

        when(criteriaRepository.existsById(criteriaId)).thenReturn(true);
        when(conditionExistByCriteriaIdQueryHandler.handle(
                        argThat(query -> query.getCriteriaId().equals(criteriaId))))
                .thenReturn(false);

        // When
        handler.handle(command);

        // Then
        verify(criteriaRepository).existsById(criteriaId);
        verify(conditionExistByCriteriaIdQueryHandler)
                .handle(argThat(query -> query.getCriteriaId().equals(criteriaId)));
        verify(criteriaDeletionValidator).ensureDeletable(criteriaId, false);
        verify(criteriaRepository).deleteById(criteriaId);
    }

    @Test
    @DisplayName("Should throw CriteriaNotFoundException when criteria does not exist")
    void shouldThrowCriteriaNotFoundException() {
        // Given
        UUID criteriaId = UUID.randomUUID();
        DeleteCriteriaCommand command = new DeleteCriteriaCommand(criteriaId);

        when(criteriaRepository.existsById(criteriaId)).thenReturn(false);

        // When / Then
        assertThatThrownBy(() -> handler.handle(command)).isInstanceOf(CriteriaNotFoundException.class);
        verify(criteriaRepository).existsById(criteriaId);
        verifyNoInteractions(conditionExistByCriteriaIdQueryHandler, criteriaDeletionValidator);
    }
}
