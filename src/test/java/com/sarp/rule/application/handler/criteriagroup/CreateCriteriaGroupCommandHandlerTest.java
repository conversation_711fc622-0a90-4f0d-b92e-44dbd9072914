package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CreateCriteriaGroupCommand;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import com.sarp.rule.domain.service.criteriagroup.CriteriaGroupDisplayOrderCalculator;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreateCriteriaGroupCommandHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupDisplayOrderCalculator criteriaGroupDisplayOrderCalculator;

    @Mock
    private CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @InjectMocks
    private CreateCriteriaGroupCommandHandler handler;

    @Test
    @DisplayName("Should handle CreateCriteriaGroupCommand and save criteria group")
    void shouldHandleCreateCriteriaGroupCommand() {
        // Given
        CreateCriteriaGroupCommand command = new CreateCriteriaGroupCommand("GroupName");
        Integer nextDisplayOrder = 1;
        CriteriaGroup criteriaGroup = CriteriaGroup.builder()
                .name("GroupName")
                .displayOrder(nextDisplayOrder)
                .build();
        CriteriaGroupApplicationDTO expectedDto = CriteriaGroupApplicationDTO.builder()
                .name("GroupName")
                .displayOrder(nextDisplayOrder)
                .build();

        when(criteriaGroupDisplayOrderCalculator.calculateNextDisplayOrder(anyInt()))
                .thenReturn(nextDisplayOrder);
        when(criteriaGroupRepository.save(any(CriteriaGroup.class))).thenReturn(criteriaGroup);
        when(criteriaGroupApplicationDTOMapper.toDTO(criteriaGroup)).thenReturn(expectedDto); // Add this mock behavior

        // When
        CriteriaGroupApplicationDTO result = handler.handle(command);

        // Then
        verify(criteriaGroupDisplayOrderCalculator).calculateNextDisplayOrder(anyInt());
        verify(criteriaGroupRepository)
                .save(argThat(group -> group.getName().equals("GroupName")
                        && group.getDisplayOrder().equals(nextDisplayOrder)));
        verify(criteriaGroupApplicationDTOMapper).toDTO(criteriaGroup); // Verify the mapper was called
        assertThat(result.getName()).isEqualTo("GroupName");
        assertThat(result.getDisplayOrder()).isEqualTo(nextDisplayOrder);
    }
}
