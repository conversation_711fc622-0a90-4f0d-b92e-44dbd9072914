package com.sarp.rule.application.handler.rule;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.sarp.rule.domain.command.rule.ActivateRuleCommand;
import com.sarp.rule.domain.entity.Rule;
import com.sarp.rule.domain.event.rule.RuleActivatedEvent;
import com.sarp.rule.domain.exception.RuleNotFoundException;
import com.sarp.rule.domain.repository.RuleRepository;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ActivateRuleCommandHandlerTest {

    @Mock
    private RuleRepository ruleRepository;

    @InjectMocks
    private ActivateRuleCommandHandler activateRuleCommandHandler;

    @Test
    void testHandle_Success() {
        // Arrange
        UUID ruleId = UUID.randomUUID();
        ActivateRuleCommand command = ActivateRuleCommand.builder().id(ruleId).build();
        Rule rule = mock(Rule.class);
        Rule updatedRule = mock(Rule.class);

        when(ruleRepository.findById(ruleId)).thenReturn(Optional.of(rule));
        when(rule.updateRuleStatusToInReview(command)).thenReturn(updatedRule);

        // Act
        RuleActivatedEvent event = activateRuleCommandHandler.handle(command);

        // Assert
        verify(ruleRepository).findById(ruleId);
        verify(rule).updateRuleStatusToInReview(command);
        verify(ruleRepository).save(updatedRule);

        assertNotNull(event);
        assertEquals(ruleId, event.getId());
    }

    @Test
    void testHandle_RuleNotFound() {
        // Arrange
        UUID ruleId = UUID.randomUUID();
        ActivateRuleCommand command = ActivateRuleCommand.builder().id(ruleId).build();

        when(ruleRepository.findById(ruleId)).thenReturn(Optional.empty());

        // Act & Assert
        RuleNotFoundException exception =
                assertThrows(RuleNotFoundException.class, () -> activateRuleCommandHandler.handle(command));

        assertEquals("RUL-RUL-005", exception.getExceptionTypeCode());
        verify(ruleRepository).findById(ruleId);
        verifyNoMoreInteractions(ruleRepository);
    }
}
