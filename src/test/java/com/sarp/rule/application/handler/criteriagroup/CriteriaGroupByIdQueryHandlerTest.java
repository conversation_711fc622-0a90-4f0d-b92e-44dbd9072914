package com.sarp.rule.application.handler.criteriagroup;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.criteriagroup.CriteriaGroupApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteriagroup.CriteriaGroupApplicationDTOMapper;
import com.sarp.rule.domain.command.criteriagroup.CriteriaGroupByIdQuery;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.exception.CriteriaGroupNotFoundException;
import com.sarp.rule.domain.repository.CriteriaGroupRepository;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaGroupByIdQueryHandlerTest {

    @Mock
    private CriteriaGroupRepository criteriaGroupRepository;

    @Mock
    private CriteriaGroupApplicationDTOMapper criteriaGroupApplicationDTOMapper;

    @InjectMocks
    private CriteriaGroupByIdQueryHandler handler;

    @Test
    @DisplayName("Should handle CriteriaGroupByIdQuery and return CriteriaGroupApplicationDTO")
    void shouldHandleCriteriaGroupByIdQuery() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        CriteriaGroup criteriaGroup = CriteriaGroup.builder()
                .displayOrder(1)
                .id(criteriaGroupId)
                .name("GroupName")
                .build();
        CriteriaGroupByIdQuery query = new CriteriaGroupByIdQuery(criteriaGroupId);
        CriteriaGroupApplicationDTO expectedDto = CriteriaGroupApplicationDTO.builder()
                .id(criteriaGroupId)
                .name("GroupName")
                .displayOrder(1)
                .build();

        when(criteriaGroupRepository.findByIdWithCriteria(criteriaGroupId)).thenReturn(Optional.of(criteriaGroup));
        when(criteriaGroupApplicationDTOMapper.toDTO(criteriaGroup))
                .thenReturn(expectedDto); // Set up the mapper behavior

        // When
        CriteriaGroupApplicationDTO result = handler.handle(query);

        // Then
        verify(criteriaGroupRepository).findByIdWithCriteria(criteriaGroupId);
        verify(criteriaGroupApplicationDTOMapper).toDTO(criteriaGroup);
        assertThat(result.getName()).isEqualTo("GroupName");
        assertThat(result.getId()).isEqualTo(criteriaGroupId);
    }

    @Test
    @DisplayName("Should throw CriteriaGroupNotFoundException when CriteriaGroup is not found")
    void shouldThrowCriteriaGroupNotFoundExceptionWhenCriteriaGroupNotFound() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        CriteriaGroupByIdQuery query = new CriteriaGroupByIdQuery(criteriaGroupId);

        when(criteriaGroupRepository.findByIdWithCriteria(criteriaGroupId)).thenReturn(Optional.empty());

        // When / Then
        assertThatThrownBy(() -> handler.handle(query))
                .isInstanceOf(CriteriaGroupNotFoundException.class)
                .hasMessageContaining("criteria_group_not_found");
        verify(criteriaGroupRepository).findByIdWithCriteria(criteriaGroupId);
    }
}
