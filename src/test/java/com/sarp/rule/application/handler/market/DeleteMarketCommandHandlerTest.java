package com.sarp.rule.application.handler.market;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.rule.domain.command.market.DeleteMarketCommand;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.repository.MarketRepository;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeleteMarketCommandHandlerTest {

    @Mock
    private MarketRepository marketRepository;

    @InjectMocks
    private DeleteMarketCommandHandler deleteMarketCommandHandler;

    @Test
    @DisplayName("Should throw MarketNotFoundException when market not found")
    void handle_ShouldThrowException_WhenMarketNotFound() {
        // Given
        UUID marketId = UUID.randomUUID();
        DeleteMarketCommand command = new DeleteMarketCommand(marketId);

        when(marketRepository.findById(marketId)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> deleteMarketCommandHandler.handle(command))
                .isInstanceOf(MarketNotFoundException.class);

        verify(marketRepository).findById(marketId);
        verify(marketRepository, never()).deleteById(any());
    }
}
