package com.sarp.rule.application.handler.market;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.query.market.MarketByIdQuery;
import com.sarp.rule.domain.repository.MarketRepository;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MarketByIdQueryHandlerTest {

    @Mock
    private MarketRepository marketRepository;

    @Mock
    private MarketMapper marketMapper;

    @InjectMocks
    private MarketByIdQueryHandler marketByIdQueryHandler;

    private UUID marketId;
    private Market market;
    private MarketResponseDTO expectedResponse;
    private MarketByIdQuery query;

    @BeforeEach
    void setUp() {
        marketId = UUID.randomUUID();

        Set<Port> ports = new HashSet<>();
        ports.add(Port.builder().portCode("IST").portName("Istanbul").build());

        market = Market.builder().id(marketId).name("Test Market").ports(ports).build();

        expectedResponse = new MarketResponseDTO();
        expectedResponse.setName("Test Market");

        query = new MarketByIdQuery(marketId);
    }

    @Test
    @DisplayName("Should successfully retrieve market by ID")
    void handle_ShouldReturnMarket() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.of(market));
        when(marketMapper.toMarketResponseDTO(market)).thenReturn(expectedResponse);

        // When
        MarketResponseDTO result = marketByIdQueryHandler.handle(query);

        // Then
        assertThat(result).isNotNull().isEqualTo(expectedResponse);

        verify(marketRepository).findById(marketId);
        verify(marketMapper).toMarketResponseDTO(market);
    }

    @Test
    @DisplayName("Should throw MarketNotFoundException when market not found")
    void handle_ShouldThrowException_WhenMarketNotFound() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> marketByIdQueryHandler.handle(query)).isInstanceOf(MarketNotFoundException.class);

        verify(marketRepository).findById(marketId);
        verifyNoInteractions(marketMapper);
    }

    @Test
    @DisplayName("Should map market to DTO correctly")
    void handle_ShouldMapMarketToDTO() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.of(market));
        when(marketMapper.toMarketResponseDTO(market)).thenReturn(expectedResponse);

        // When
        marketByIdQueryHandler.handle(query);

        // Then
        verify(marketMapper)
                .toMarketResponseDTO(
                        argThat(m -> m.getId().equals(marketId) && m.getName().equals("Test Market")));
    }
}
