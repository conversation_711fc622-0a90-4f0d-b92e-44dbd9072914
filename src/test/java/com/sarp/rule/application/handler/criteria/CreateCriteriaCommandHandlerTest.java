package com.sarp.rule.application.handler.criteria;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.sarp.rule.application.dto.criteria.CriteriaApplicationDTO;
import com.sarp.rule.application.dto.mapper.criteria.CriteriaApplicationDTOMapper;
import com.sarp.rule.domain.command.criteria.CreateCriteriaCommand;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.repository.CriteriaRepository;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreateCriteriaCommandHandlerTest {

    @Mock
    private CriteriaRepository criteriaRepository;

    @InjectMocks
    private CreateCriteriaCommandHandler handler;

    @Mock
    private CriteriaApplicationDTOMapper criteriaApplicationDTOMapper;

    @Test
    @DisplayName("Should handle CreateCriteriaCommand and save criteria")
    void shouldHandleCreateCriteriaCommand() {
        // Given
        CreateCriteriaCommand command = createCommand();
        Criteria savedCriteria = createCriteria();

        CriteriaApplicationDTO expectedDto =
                CriteriaApplicationDTO.builder().name("Test Criteria").build();

        when(criteriaRepository.save(any(Criteria.class))).thenReturn(savedCriteria);
        when(criteriaApplicationDTOMapper.toDTO(savedCriteria)).thenReturn(expectedDto);

        // When
        CriteriaApplicationDTO result = handler.handle(command);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Criteria");
        verify(criteriaRepository).save(any(Criteria.class));
        verify(criteriaApplicationDTOMapper).toDTO(savedCriteria);
    }

    private CreateCriteriaCommand createCommand() {
        Criteria criteria = createCriteria();
        return new CreateCriteriaCommand(criteria);
    }

    private Criteria createCriteria() {
        return new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("Test Criteria")
                .description("Test description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .build();
    }
}
