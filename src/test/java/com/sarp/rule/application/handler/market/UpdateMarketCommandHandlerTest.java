package com.sarp.rule.application.handler.market;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.mapper.MarketMapper;
import com.sarp.rule.application.service.MarketEventService;
import com.sarp.rule.domain.command.market.UpdateMarketCommand;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import com.sarp.rule.domain.event.market.MarketUpdatedEvent;
import com.sarp.rule.domain.exception.MarketNotFoundException;
import com.sarp.rule.domain.repository.MarketRepository;
import com.sarp.rule.domain.repository.PortRepository;
import com.sarp.rule.domain.service.PortVerifier;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateMarketCommandHandlerTest {

    @Mock
    private MarketRepository marketRepository;

    @Mock
    private PortRepository portRepository;

    @Mock
    private MarketMapper marketMapper;

    @Mock
    private MarketEventService marketEventService;

    @Mock
    private PortVerifier portVerifier;

    @InjectMocks
    private UpdateMarketCommandHandler updateMarketCommandHandler;

    private UUID marketId;
    private Market existingMarket;
    private Market updateMarket;
    private UpdateMarketCommand command;
    private Port port1;
    private Port port2;
    private List<String> portCodes;
    private Set<Port> ports;
    private MarketResponseDTO expectedResponse;

    @BeforeEach
    void setUp() {
        marketId = UUID.randomUUID();

        // Setup ports
        port1 = Port.builder().portCode("IST").portName("Istanbul").active(true).build();

        port2 = Port.builder().portCode("SAW").portName("Sabiha").active(true).build();

        portCodes = Arrays.asList("IST", "SAW");
        ports = new HashSet<>(Arrays.asList(port1, port2));

        // Setup existing market
        existingMarket = Market.builder()
                .id(marketId)
                .name("Existing Market")
                .ports(new HashSet<>())
                .build();

        // Setup update market
        updateMarket = Market.builder()
                .id(marketId)
                .name("Updated Market")
                .ports(ports)
                .build();

        command = new UpdateMarketCommand(marketId, updateMarket);

        expectedResponse = new MarketResponseDTO();
        expectedResponse.setName("Updated Market");
    }

    @Test
    @DisplayName("Should successfully update a market")
    void handle_ShouldUpdateMarket() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.of(existingMarket));
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(updateMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);
        doNothing().when(marketEventService).sendMarketUpdatedEventAfterCommit(any(MarketUpdatedEvent.class));
        doNothing().when(portVerifier).verifyPortExists(anyList(), anySet());
        doNothing().when(portVerifier).verifyPortsActive(anyList(), anySet());

        // When
        MarketResponseDTO result = updateMarketCommandHandler.handle(command);

        // Then
        assertThat(result).isNotNull().isEqualTo(expectedResponse);

        verify(marketRepository).findById(marketId);
        verify(portRepository).findAllByPortCodeIn(portCodes);
        verify(portVerifier).verifyPortExists(eq(portCodes), any());
        verify(portVerifier).verifyPortsActive(eq(portCodes), any());
        verify(marketRepository).save(any(Market.class));
        verify(marketEventService).sendMarketUpdatedEventAfterCommit(any(MarketUpdatedEvent.class));
        verify(marketMapper).toMarketResponseDTO(any(Market.class));
    }

    @Test
    @DisplayName("Should throw MarketNotFoundException when market not found")
    void handle_ShouldThrowException_WhenMarketNotFound() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> updateMarketCommandHandler.handle(command))
                .isInstanceOf(MarketNotFoundException.class);

        verify(marketRepository).findById(marketId);
        verifyNoInteractions(portRepository, marketMapper, marketEventService);
    }

    @Test
    @DisplayName("Should keep existing name when update name is null")
    void handle_ShouldKeepExistingName_WhenUpdateNameIsNull() {
        // Given
        Market marketWithNullName =
                Market.builder().id(marketId).name(null).ports(ports).build();
        UpdateMarketCommand commandWithNullName = new UpdateMarketCommand(marketId, marketWithNullName);

        when(marketRepository.findById(marketId)).thenReturn(Optional.of(existingMarket));
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(existingMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);

        // When
        updateMarketCommandHandler.handle(commandWithNullName);

        // Then
        verify(marketRepository).save(argThat(market -> market.getName().equals(existingMarket.getName())));
    }

    @Test
    @DisplayName("Should verify ports exist and are active when updating market")
    void handle_ShouldVerifyPorts() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.of(existingMarket));
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(updateMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);

        // When
        updateMarketCommandHandler.handle(command);

        // Then
        verify(portVerifier).verifyPortExists(eq(portCodes), any());
        verify(portVerifier).verifyPortsActive(eq(portCodes), any());
    }

    @Test
    @DisplayName("Should send market updated event after saving")
    void handle_ShouldSendMarketUpdatedEvent() {
        // Given
        when(marketRepository.findById(marketId)).thenReturn(Optional.of(existingMarket));
        when(portRepository.findAllByPortCodeIn(eq(portCodes))).thenReturn(new ArrayList<>(ports));
        when(marketRepository.save(any(Market.class))).thenReturn(updateMarket);
        when(marketMapper.toMarketResponseDTO(any(Market.class))).thenReturn(expectedResponse);

        // When
        updateMarketCommandHandler.handle(command);

        // Then
        verify(marketEventService)
                .sendMarketUpdatedEventAfterCommit(
                        argThat(event -> event.getMarketId().equals(updateMarket.getId())
                                && event.getMarketName().equals(updateMarket.getName())
                                && event.getPortCodes().containsAll(portCodes)));
    }
}
