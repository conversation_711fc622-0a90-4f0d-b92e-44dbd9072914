package com.sarp.rule.application.event.messaging;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class RuleDeletePayloadTest {

    @Test
    @DisplayName("Should create RuleDeletePayload with all fields")
    void shouldCreateRuleDeletePayload() {
        // Given
        UUID id = UUID.randomUUID();
        UUID oldConditionSetId = UUID.randomUUID();

        // When
        RuleDeletePayload payload = RuleDeletePayload.builder()
                .id(id)
                .oldConditionSets(List.of(oldConditionSetId))
                .build();

        // Then
        assertThat(payload.getId()).isEqualTo(id);
        assertThat(payload.getOldConditionSets()).containsExactly(oldConditionSetId);
    }
}
