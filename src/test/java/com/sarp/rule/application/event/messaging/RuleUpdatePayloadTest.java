package com.sarp.rule.application.event.messaging;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.adapter.persistence.model.enums.CriteriaType;
import com.sarp.rule.adapter.persistence.model.enums.FieldType;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class RuleUpdatePayloadTest {

    @Test
    @DisplayName("Should create RuleUpdatePayload with all fields")
    void shouldCreateRuleUpdatePayload() {
        // Given
        UUID id = UUID.randomUUID();
        RuleType ruleType = RuleType.BUNDLE;
        UUID conditionSetId = UUID.randomUUID();
        UUID oldConditionSetId = UUID.randomUUID();
        UUID actionId = UUID.randomUUID();

        Condition condition = Condition.builder()
                .type(FieldType.TEXT)
                .value(Collections.singletonList("test"))
                .operator(ComparisonOperator.EQUALS)
                .criteriaMappingField("name")
                .criteriaType(CriteriaType.USER_DEFINED)
                .build();

        When when = When.builder().conditions(List.of(condition)).build();

        Then then = Then.builder().actions(List.of(actionId)).build();

        ConditionSet conditionSet = ConditionSet.builder()
                .id(conditionSetId)
                .priority(1)
                .when(when)
                .then(then)
                .build();

        // When
        RuleUpdatePayload payload = RuleUpdatePayload.builder()
                .id(id)
                .ruleType(ruleType)
                .conditionSets(List.of(conditionSet))
                .oldConditionSets(List.of(oldConditionSetId))
                .build();

        // Then
        assertThat(payload.getId()).isEqualTo(id);
        assertThat(payload.getRuleType()).isEqualTo(ruleType);
        assertThat(payload.getConditionSets()).hasSize(1);
        assertThat(payload.getOldConditionSets()).containsExactly(oldConditionSetId);

        ConditionSet resultConditionSet = payload.getConditionSets().getFirst();
        assertThat(resultConditionSet.getId()).isEqualTo(conditionSetId);
        assertThat(resultConditionSet.getPriority()).isEqualTo(1);

        When resultWhen = resultConditionSet.getWhen();
        assertThat(resultWhen.getConditions()).hasSize(1);
        assertThat(resultWhen.getConditions().getFirst()).satisfies(c -> {
            assertThat(c.getType()).isEqualTo(FieldType.TEXT);
            assertThat(c.getValue()).isEqualTo(Collections.singletonList("test"));
            assertThat(c.getOperator()).isEqualTo(ComparisonOperator.EQUALS);
            assertThat(c.getCriteriaMappingField()).isEqualTo("name");
        });

        Then resultThen = resultConditionSet.getThen();
        assertThat(resultThen.getActions()).containsExactly(actionId);
    }
}
