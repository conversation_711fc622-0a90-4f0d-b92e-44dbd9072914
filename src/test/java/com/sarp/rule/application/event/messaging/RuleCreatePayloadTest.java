package com.sarp.rule.application.event.messaging;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.adapter.persistence.model.enums.CriteriaType;
import com.sarp.rule.adapter.persistence.model.enums.FieldType;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class RuleCreatePayloadTest {

    @Test
    @DisplayName("Should create RuleCreatePayload with all fields")
    void shouldCreateRuleCreatePayload() {
        // Given
        UUID ruleDefinitionId = UUID.randomUUID();
        RuleType ruleType = RuleType.BUNDLE;
        UUID conditionSetId = UUID.randomUUID();
        UUID actionId = UUID.randomUUID();

        Condition condition = Condition.builder()
                .type(FieldType.TEXT)
                .value(List.of("test"))
                .operator(ComparisonOperator.EQUALS)
                .criteriaMappingField("name")
                .criteriaType(CriteriaType.USER_DEFINED)
                .build();

        When when = When.builder().conditions(List.of(condition)).build();

        Then then = Then.builder().actions(List.of(actionId)).build();

        ConditionSet conditionSet = ConditionSet.builder()
                .id(conditionSetId)
                .priority(1)
                .when(when)
                .then(then)
                .build();

        // When
        RuleCreatePayload payload = RuleCreatePayload.builder()
                .id(ruleDefinitionId)
                .ruleType(ruleType)
                .conditionSets(List.of(conditionSet))
                .build();

        // Then
        assertThat(payload.getId()).isEqualTo(ruleDefinitionId);
        assertThat(payload.getRuleType()).isEqualTo(ruleType);
        assertThat(payload.getConditionSets()).hasSize(1);

        ConditionSet resultConditionSet = payload.getConditionSets().getFirst();
        assertThat(resultConditionSet.getId()).isEqualTo(conditionSetId);
        assertThat(resultConditionSet.getPriority()).isEqualTo(1);

        When resultWhen = resultConditionSet.getWhen();
        assertThat(resultWhen.getConditions()).hasSize(1);
        assertThat(resultWhen.getConditions().getFirst()).satisfies(c -> {
            assertThat(c.getType()).isEqualTo(FieldType.TEXT);
            assertThat(c.getValue()).isEqualTo((List.of("test")));
            assertThat(c.getOperator()).isEqualTo(ComparisonOperator.EQUALS);
            assertThat(c.getCriteriaMappingField()).isEqualTo("name");
        });

        Then resultThen = resultConditionSet.getThen();
        assertThat(resultThen.getActions()).containsExactly(actionId);
    }
}
