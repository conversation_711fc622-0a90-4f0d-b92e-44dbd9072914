// package com.sarp.rule.application.service;
//
// import static org.assertj.core.api.Assertions.assertThat;
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.ArgumentMatchers.eq;
// import static org.mockito.Mockito.verify;
// import static org.mockito.Mockito.when;
//
// import com.sarp.rule.domain.entity.Bundle;
// import com.sarp.rule.domain.event.bundle.BundleUpdatedEvent;
// import com.sarp.rule.domain.event.bundle.BundlesCreatedEvent;
// import com.sarp.rule.domain.service.BundleDomainService;
// import com.sarp.rule.domain.valueobject.bundle.BundleId;
// import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
// import com.sarp.rule.domain.valueobject.bundle.BundleType;
// import java.time.LocalDateTime;
// import java.util.Arrays;
// import java.util.List;
// import java.util.UUID;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// class BundleApplicationServiceTest {
//
//    @Mock private BundleDomainService bundleDomainService;
//
//    @InjectMocks private BundleApplicationService bundleApplicationService;
//
//    private Bundle bundle;
//    private UUID bundleId;
//    private BundlesCreatedEvent bundlesCreatedEvent;
//    private BundleUpdatedEvent bundleUpdatedEvent;
//    private BundleDeletedEvent bundleDeletedEvent;
//    private List<Bundle> bundleList;
//
//    @BeforeEach
//    void setUp() {
//        bundleId = UUID.randomUUID();
//
//        // Create a bundle for testing
//        bundle =
//                new Bundle.Builder()
//                        .id(bundleId)
//                        .name("Test Bundle")
//                        .description("Test Bundle Description")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        // Create domain events
//        bundlesCreatedEvent = new BundlesCreatedEvent(bundle, LocalDateTime.now());
//        bundleUpdatedEvent = new BundleUpdatedEvent(bundle, LocalDateTime.now());
//        bundleDeletedEvent = new BundleDeletedEvent(bundle, LocalDateTime.now());
//
//        // Create bundle list
//        Bundle bundle2 =
//                new Bundle.Builder()
//                        .id(UUID.randomUUID())
//                        .name("Test Bundle 2")
//                        .description("Test Bundle Description 2")
//                        .type(BundleType.STANDALONE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        bundleList = Arrays.asList(bundle, bundle2);
//    }
//
//    @Test
//    @DisplayName("Given a bundle ID, when getBundleById is called, then bundle should be
// returned")
//    void givenBundleId_whenGetBundleByIdCalled_thenBundleReturned() {
//        // Given
//        when(bundleDomainService.findBundleByIdAndStatusNot(
//                        any(BundleId.class), eq(BundleStatus.PASSIVE)))
//                .thenReturn(bundle);
//
//        // When
//        Bundle result = bundleApplicationService.getBundleById(bundleId);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).isEqualTo(bundle);
//
//        // Verify interactions
//        verify(bundleDomainService)
//                .findBundleByIdAndStatusNot(eq(BundleId.of(bundleId)), eq(BundleStatus.PASSIVE));
//    }
//
//    @Test
//    @DisplayName(
//            "Given a status, page, and size, when getAllBundles is called, then all matching
// bundles should be returned")
//    void givenStatusPageAndSize_whenGetAllBundlesCalled_thenAllMatchingBundlesReturned() {
//        // Given
//        BundleStatus status = BundleStatus.ACTIVE;
//        Integer page = 1;
//        Integer size = 10;
//
//        when(bundleDomainService.findAllBundles(status, page, size)).thenReturn(bundleList);
//
//        // When
//        List<Bundle> result = bundleApplicationService.getAllBundles(status, page, size);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).hasSize(2);
//        assertThat(result).containsExactlyElementsOf(bundleList);
//
//        // Verify interactions
//        verify(bundleDomainService).findAllBundles(status, page, size);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID and updated bundle, when updateBundle is called, then
// BundleUpdatedEvent should be returned")
//    void givenBundleIdAndUpdatedBundle_whenUpdateBundleCalled_thenBundleUpdatedEventReturned() {
//        // Given
//        Bundle updatedBundle =
//                new Bundle.Builder()
//                        .name("Updated Bundle")
//                        .description("Updated Description")
//                        .type(BundleType.STANDALONE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        when(bundleDomainService.updateBundle(eq(BundleId.of(bundleId)), any(Bundle.class)))
//                .thenReturn(bundleUpdatedEvent);
//
//        // When
//        BundleUpdatedEvent result = bundleApplicationService.updateBundle(bundleId,
// updatedBundle);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).isEqualTo(bundleUpdatedEvent);
//        assertThat(result.getBundle()).isEqualTo(bundle);
//
//        // Verify interactions
//        verify(bundleDomainService).updateBundle(eq(BundleId.of(bundleId)), eq(updatedBundle));
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID, when deleteBundle is called, then BundleDeletedEvent should be
// returned")
//    void givenBundleId_whenDeleteBundleCalled_thenBundleDeletedEventReturned() {
//        // Given
//
// when(bundleDomainService.deleteBundle(any(BundleId.class))).thenReturn(bundleDeletedEvent);
//
//        // When
//        BundleDeletedEvent result = bundleApplicationService.deleteBundle(bundleId);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).isEqualTo(bundleDeletedEvent);
//        assertThat(result.getBundle()).isEqualTo(bundle);
//
//        // Verify interactions
//        verify(bundleDomainService).deleteBundle(eq(BundleId.of(bundleId)));
//    }
//
//    @Test
//    @DisplayName(
//            "Given null status, when getAllBundles is called, then findAllByStatusNot should be
// called with PASSIVE status")
//    void givenNullStatus_whenGetAllBundlesCalled_thenFindAllByStatusNotCalledWithPassiveStatus() {
//        // Given
//        Integer page = 1;
//        Integer size = 10;
//
//        when(bundleDomainService.findAllBundles(null, page, size)).thenReturn(bundleList);
//
//        // When
//        List<Bundle> result = bundleApplicationService.getAllBundles(null, page, size);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).hasSize(2);
//        assertThat(result).containsExactlyElementsOf(bundleList);
//
//        // Verify interactions
//        verify(bundleDomainService).findAllBundles(null, page, size);
//    }
// }
