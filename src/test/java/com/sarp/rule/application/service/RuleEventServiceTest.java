// package com.sarp.rule.application.service;
//
// import static org.assertj.core.api.Assertions.assertThat;
// import static org.assertj.core.api.Assertions.assertThatThrownBy;
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.ArgumentMatchers.eq;
// import static org.mockito.Mockito.*;
//
// import com.sarp.commons.kafka.adapter.producer.CloudEventKafkaProducer;
// import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
// import com.sarp.rule.application.event.messaging.RuleCreatePayload;
// import com.sarp.rule.application.event.messaging.RuleEvent;
// import com.sarp.rule.config.RuleEventConfig;
// import com.sarp.rule.domain.entity.*;
// import com.sarp.rule.domain.event.rule.RuleCreatedEvent;
// import com.sarp.rule.domain.repository.CriteriaRepository;
// import com.sarp.rule.domain.valueobject.common.EffectiveDates;
// import com.sarp.rule.domain.valueobject.criteria.FieldType;
// import com.sarp.rule.domain.valueobject.rule.RuleStatus;
// import com.sarp.rule.domain.valueobject.rule.RuleType;
// import java.time.Instant;
// import java.util.*;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.ArgumentCaptor;
// import org.mockito.Captor;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// class RuleEventServiceTest {
//
//    @Mock private CloudEventKafkaProducer kafkaProducer;
//
//    @Mock private CriteriaRepository criteriaRepository;
//
//    @Mock(lenient = true)
//    private RuleEventConfig config;
//
//    @Captor private ArgumentCaptor<RuleEvent<RuleCreatePayload>> ruleEventCaptor;
//
//    private RuleEventService ruleEventService;
//
//    @BeforeEach
//    void setUp() {
//        ruleEventService = new RuleEventService(kafkaProducer, criteriaRepository, config);
//    }
//
//    @Test
//    void sendRuleCreatedEvent_WithNullRule_ShouldNotSendEvent() {
//        // Given
//        RuleCreatedEvent event =
//                new RuleCreatedEvent(
//                        RuleType.BUNDLE,
//                        Collections.emptyList(),
//                        Collections.emptyList(),
//                        Collections.emptyList(),
//                        null,
//                        null);
//
//        // When
//        ruleEventService.sendRuleCreatedEvent(event);
//
//        // Then
//        verify(kafkaProducer, never()).sendAsync(any(), any(), any(), any());
//    }
//
//    @Test
//    void sendRuleCreatedEvent_WithValidRule_ShouldSendEvent() {
//        // Given
//        when(config.getProducerName()).thenReturn("rule-producer");
//        when(config.getTopic()).thenReturn("sarp.offer.rule.0");
//
//        UUID conditionSetGroupTemplateId = UUID.randomUUID();
//        ConditionSetGroupTemplate conditionSetGroupTemplate =
//                ConditionSetGroupTemplate.builder()
//                        .id(conditionSetGroupTemplateId)
//                        .name("Test Rule")
//                        .author("Berke")
//                        .build();
//
//        Set<ConditionSet> conditionSets =
//                new HashSet<>(
//                        Collections.singleton(
//                                ConditionSet.builder()
//                                        .displayOrder("1")
//                                        .conditionNodes(new HashSet<>())
//                                        .actions(new HashSet<>())
//                                        .build()));
//        ConditionSetGroup group =
//                ConditionSetGroup.builder()
//                        .name("Test Group")
//                        .description("Test Description")
//                        .conditionSets(conditionSets)
//                        .build();
//
//        RuleDefinition config =
//                RuleDefinition.builder()
//                        .name("Test Config")
//                        .type(RuleType.BUNDLE)
//                        .status(RuleStatus.ACTIVE)
//                        .priority((short) 1)
//                        .effectiveDates(
//                                EffectiveDates.of(Instant.now(), Instant.now().plusSeconds(3600)))
//                        .build();
//
//        RuleCreatedEvent event =
//                new RuleCreatedEvent(
//                        RuleType.BUNDLE,
//                        List.of(group),
//                        Collections.emptyList(),
//                        Collections.emptyList(),
//                        config,
//                        conditionSetGroupTemplate);
//
//        // When
//        ruleEventService.sendRuleCreatedEvent(event);
//
//        // Then
//        verify(kafkaProducer)
//                .sendAsync(
//                        eq("rule-producer"),
//                        eq("sarp.offer.rule.0"),
//                        eq(conditionSetGroupTemplateId.toString()),
//                        ruleEventCaptor.capture());
//
//        RuleEvent<RuleCreatePayload> capturedEvent = ruleEventCaptor.getValue();
//        assertThat(capturedEvent.getData().getId()).isEqualTo(conditionSetGroupTemplateId);
//        assertThat(capturedEvent.getData().getRuleType()).isEqualTo(RuleType.BUNDLE);
//    }
//
//    @Test
//    void convertConditionSetGroups_ShouldConvertCorrectly() {
//        // Given
//        UUID conditionSetId = UUID.randomUUID();
//        UUID criteriaId = UUID.randomUUID();
//
//        Criteria criteria =
//                Criteria.builder()
//                        .id(criteriaId)
//                        .name("Test Criteria")
//                        .description("Test Description")
//                        .mappingField("testField")
//                        .fieldType(FieldType.TEXT)
//                        .createDate(Instant.now())
//                        .build();
//
//        when(criteriaRepository.findAllById(any())).thenReturn(List.of(criteria));
//
//        Condition condition =
//                Condition.builder()
//                        .criteriaId(criteriaId)
//                        .value(List.of("testValue"))
//                        .operator("EQUALS")
//                        .build();
//
//        ConditionNode conditionNode = ConditionNode.builder().condition(condition).build();
//
//        ConditionSet conditionSet =
//                ConditionSet.builder()
//                        .id(conditionSetId)
//                        .displayOrder("1")
//                        .conditionNodes(Set.of(conditionNode))
//                        .actions(Set.of())
//                        .build();
//
//        ConditionSetGroup conditionSetGroup =
//                ConditionSetGroup.builder()
//                        .name("Test Group")
//                        .description("Test Description")
//                        .conditionSets(Set.of(conditionSet))
//                        .build();
//
//        RuleDefinition config =
//                RuleDefinition.builder()
//                        .name("Test Config")
//                        .type(RuleType.BUNDLE)
//                        .status(RuleStatus.ACTIVE)
//                        .priority((short) 1)
//                        .effectiveDates(
//                                EffectiveDates.of(Instant.now(), Instant.now().plusSeconds(3600)))
//                        .build();
//
//        // When
//        List<RuleCreatePayload.ConditionSet> result =
//                ruleEventService.convertConditionSetGroups(List.of(conditionSetGroup), config);
//
//        // Then
//        assertThat(result).hasSize(1);
//        RuleCreatePayload.ConditionSet convertedSet = result.get(0);
//        assertThat(convertedSet.getPriority()).isEqualTo(1);
//        assertThat(convertedSet.getWhen().getConditions()).hasSize(1);
//        RuleCreatePayload.Condition convertedCondition =
//                convertedSet.getWhen().getConditions().get(0);
//        assertThat(convertedCondition.getType().name()).isEqualTo(FieldType.TEXT.name());
//        assertThat(convertedCondition.getValue()).isEqualTo(List.of("testValue"));
//        assertThat(convertedCondition.getOperator()).isEqualTo(ComparisonOperator.EQUALS);
//        assertThat(convertedCondition.getCriteria()).isEqualTo("testField");
//    }
//
//    @Test
//    void convertConditionNodes_ShouldConvertCorrectly() {
//        // Given
//        UUID criteriaId = UUID.randomUUID();
//        UUID conditionNodeId = UUID.randomUUID();
//
//        Criteria criteria =
//                Criteria.builder()
//                        .id(criteriaId)
//                        .name("Test Criteria")
//                        .description("Test Description")
//                        .mappingField("testField")
//                        .fieldType(FieldType.TEXT)
//                        .createDate(Instant.now())
//                        .build();
//
//        when(criteriaRepository.findAllById(any())).thenReturn(List.of(criteria));
//
//        Condition condition =
//                Condition.builder()
//                        .criteriaId(criteriaId)
//                        .value(List.of("testValue"))
//                        .operator("EQUALS")
//                        .build();
//
//        ConditionNode conditionNode =
//                ConditionNode.builder().id(conditionNodeId).condition(condition).build();
//
//        // When
//        List<RuleCreatePayload.Condition> result =
//                ruleEventService.convertConditionNodes(Set.of(conditionNode));
//
//        // Then
//        assertThat(result).hasSize(1);
//        RuleCreatePayload.Condition convertedCondition = result.get(0);
//        assertThat(convertedCondition.getType().name()).isEqualTo(FieldType.TEXT.name());
//        assertThat(convertedCondition.getValue()).isEqualTo(List.of("testValue"));
//        assertThat(convertedCondition.getOperator()).isEqualTo(ComparisonOperator.EQUALS);
//        assertThat(convertedCondition.getCriteria()).isEqualTo("testField");
//    }
//
//    @Test
//    void convertConditionNodes_WithMissingCriteria_ShouldThrowException() {
//        // Given
//        UUID criteriaId = UUID.randomUUID();
//        UUID conditionNodeId = UUID.randomUUID();
//
//        Condition condition =
//                Condition.builder()
//                        .criteriaId(criteriaId)
//                        .value(List.of("testValue"))
//                        .operator("EQUALS")
//                        .build();
//
//        ConditionNode conditionNode =
//                ConditionNode.builder().id(conditionNodeId).condition(condition).build();
//
//        when(criteriaRepository.findAllById(any())).thenReturn(Collections.emptyList());
//
//        // When/Then
//        assertThatThrownBy(() -> ruleEventService.convertConditionNodes(Set.of(conditionNode)))
//                .isInstanceOf(IllegalStateException.class)
//                .hasMessageContaining("Criteria not found");
//    }
// }
