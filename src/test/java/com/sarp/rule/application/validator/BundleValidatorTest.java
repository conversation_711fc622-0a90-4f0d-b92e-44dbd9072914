package com.sarp.rule.application.validator;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.exception.BundleNotFoundException;
import com.sarp.rule.domain.repository.BundleRepository;
import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
import com.sarp.rule.domain.valueobject.bundle.BundleType;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BundleValidatorTest {

    @Mock
    private BundleRepository bundleRepository;

    @InjectMocks
    private BundleValidator bundleValidator;

    private UUID bundleId1;
    private UUID bundleId2;
    private Bundle bundle1;
    private Bundle bundle2;

    @BeforeEach
    void setUp() {
        bundleId1 = UUID.randomUUID();
        bundleId2 = UUID.randomUUID();

        bundle1 = createBundle(bundleId1, "Bundle 1");
        bundle2 = createBundle(bundleId2, "Bundle 2");
    }

    @Test
    void shouldNotThrowException_WhenAllBundlesExist() {
        // Given
        List<UUID> bundleIds = Arrays.asList(bundleId1, bundleId2);
        List<Bundle> foundBundles = Arrays.asList(bundle1, bundle2);

        when(bundleRepository.findAllById(bundleIds)).thenReturn(foundBundles);

        // When & Then
        assertDoesNotThrow(() -> bundleValidator.validateAllBundlesExist(bundleIds));
        verify(bundleRepository).findAllById(bundleIds);
    }

    @Test
    void shouldThrowBundleNotFoundException_WhenNoBundlesFound() {
        // Given
        List<UUID> bundleIds = Arrays.asList(bundleId1, bundleId2);

        when(bundleRepository.findAllById(bundleIds)).thenReturn(Collections.emptyList());

        // When & Then
        assertThatThrownBy(() -> bundleValidator.validateAllBundlesExist(bundleIds))
                .isInstanceOf(BundleNotFoundException.class)
                .hasMessageContaining("bundle_not_found")
                .satisfies(exception -> {
                    Object[] args = ((BundleNotFoundException) exception).getArgs();
                    assertThat(args).isNotNull();
                    assertThat(args[0].toString().contains(bundleId1.toString()))
                            .isTrue();
                });

        verify(bundleRepository).findAllById(bundleIds);
    }

    private Bundle createBundle(UUID id, String name) {
        return Bundle.builder()
                .id(id)
                .name(name)
                .description("Test description for " + name)
                .type(BundleType.FLIGHT_INCLUSIVE)
                .status(BundleStatus.ACTIVE)
                .build();
    }
}
