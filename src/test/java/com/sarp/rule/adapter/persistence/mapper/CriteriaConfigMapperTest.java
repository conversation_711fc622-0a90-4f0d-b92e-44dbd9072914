package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.domain.entity.CriteriaConfig;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CriteriaConfigMapperTest {

    private CriteriaConfigMapper criteriaConfigMapper;

    private CriteriaConfigEntity criteriaConfigEntity;
    private CriteriaEntity criteriaEntity;
    private CriteriaConfig criteriaConfigDomain;

    @BeforeEach
    void setUp() {
        criteriaConfigMapper = Mappers.getMapper(CriteriaConfigMapper.class);

        UUID configUuid = UUID.randomUUID();
        UUID criteriaUuid = UUID.randomUUID();

        // Create domain object
        criteriaConfigDomain = CriteriaConfig.builder()
                .id(configUuid)
                .allowedRules(null)
                .criteriaId(criteriaUuid)
                .displayOrder(1)
                .build();

        // Create entity object
        criteriaEntity = new CriteriaEntity();
        criteriaEntity.setId(criteriaUuid);

        criteriaConfigEntity = new CriteriaConfigEntity();
        criteriaConfigEntity.setId(configUuid);
        criteriaConfigEntity.setAllowedRules(null);
        criteriaConfigEntity.setCriteria(criteriaEntity);
        criteriaConfigEntity.setDisplayOrder(1);
    }

    @Test
    @DisplayName("Should map domain CriteriaConfig to entity")
    void shouldMapDomainCriteriaConfigToEntity() {
        // When
        CriteriaConfigEntity result = criteriaConfigMapper.toCriteriaConfigEntity(criteriaConfigDomain);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaConfigEntity.getId());
        assertThat(result.getCriteria().getId())
                .isEqualTo(criteriaConfigEntity.getCriteria().getId());
    }

    @Test
    @DisplayName("Should map entity to domain CriteriaConfig")
    void shouldMapEntityToDomainCriteriaConfig() {
        // When
        CriteriaConfig result = criteriaConfigMapper.toCriteriaConfigDomain(criteriaConfigEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaConfigDomain.getId());
        assertThat(result.getCriteriaId()).isEqualTo(criteriaConfigDomain.getCriteriaId());
    }
}
