package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CreateCriteriaRequestDTO;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CriteriaMapperTest {

    private final CriteriaMapper criteriaMapper = Mappers.getMapper(CriteriaMapper.class);

    private UUID criteriaUuid;
    private CriteriaEntity criteriaEntity;
    private Criteria criteriaDomain;
    private CreateCriteriaRequestDTO requestDTO;

    @BeforeEach
    void setUp() {
        criteriaUuid = UUID.randomUUID();

        criteriaEntity = CriteriaEntity.builder()
                .id(criteriaUuid)
                .name("Test Criteria")
                .description("A test criteria")
                .type(com.sarp.rule.adapter.persistence.model.enums.CriteriaType.USER_DEFINED)
                .requestType(com.sarp.rule.adapter.persistence.model.enums.RequestType.OFFER_REQUEST)
                .mappingField("testField")
                .fieldType(com.sarp.rule.adapter.persistence.model.enums.FieldType.INTEGER)
                .selectionType(null)
                .allowedValues(null)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .criteriaGroup(
                        CriteriaGroupEntity.builder().id(UUID.randomUUID()).build())
                .build();

        criteriaDomain = new Criteria.Builder()
                .id(criteriaUuid)
                .name("Test Criteria")
                .description("A test criteria")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("testField")
                .fieldType(FieldType.INTEGER)
                .selectionType(null)
                .allowedValues(null)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .criteriaGroupId(UUID.randomUUID())
                .build();

        requestDTO = new CreateCriteriaRequestDTO()
                .name("Test Criteria")
                .description("A test criteria")
                .mappingField("testField")
                .fieldType(CreateCriteriaRequestDTO.FieldTypeEnum.INTEGER)
                .minValue(0.0)
                .maxValue(10.0);
    }

    @Test
    @DisplayName("Given a domain criteria, when toCriteriaEntity is called, then entity should be returned")
    void givenDomainCriteria_whenToCriteriaEntityCalled_thenEntityReturned() {
        CriteriaEntity result = criteriaMapper.toCriteriaEntity(criteriaDomain);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaUuid);
        assertThat(result.getName()).isEqualTo("Test Criteria");
        assertThat(result.getFieldType().name()).isEqualTo("INTEGER");
    }

    @Test
    @DisplayName("Given a criteria entity, when toCriteriaDomain is called, then domain should be returned")
    void givenCriteriaEntity_whenToDomainCalled_thenDomainReturned() {
        Criteria result = criteriaMapper.toCriteriaDomain(criteriaEntity);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaUuid);
        assertThat(result.getName()).isEqualTo("Test Criteria");
        assertThat(result.getFieldType()).isEqualTo(FieldType.INTEGER);
    }

    @Test
    @DisplayName("Given a DTO, when toCriteriaDomain is called, then domain should be returned with id ignored")
    void givenRequestDTO_whenToDomainCalled_thenDomainReturnedWithIdIgnored() {
        Criteria result = criteriaMapper.toCriteriaDomain(requestDTO);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isInstanceOf(UUID.class);
        assertThat(result.getName()).isEqualTo("Test Criteria");
        assertThat(result.getFieldType()).isEqualTo(FieldType.INTEGER);
    }
}
