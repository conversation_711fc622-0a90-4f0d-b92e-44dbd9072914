package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.sarp.generated.openapi.api.dto.*;
import com.sarp.rule.adapter.persistence.model.entity.BundleEntity;
import com.sarp.rule.adapter.persistence.model.entity.BundleProductEntity;
import com.sarp.rule.adapter.persistence.model.entity.ProductEntity;
import com.sarp.rule.adapter.persistence.model.enums.BundleStatus;
import com.sarp.rule.adapter.persistence.model.enums.BundleType;
import com.sarp.rule.domain.entity.Bundle;
import com.sarp.rule.domain.entity.Product;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BundleMapperTest {

    @InjectMocks
    private BundleMapperImpl bundleMapper;

    @Mock
    private ProductMapper productMapper;

    private UUID bundleUuid;
    private BundleEntity bundleEntity;
    private Bundle bundleDomain;
    private BundleDTO bundleDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        bundleUuid = UUID.randomUUID();

        // Setup ProductEntity and Product mock
        UUID productId = UUID.randomUUID();
        ProductEntity productEntity = ProductEntity.builder()
                .id(productId)
                .variantId(productId)
                .name("Sample Product")
                .build();

        Product productDomain = new Product.Builder()
                .id(productId)
                .variantId(productId)
                .name("Sample Product")
                .build();

        when(productMapper.toDomain(productEntity)).thenReturn(productDomain);

        // Setup BundleProductEntity
        BundleProductEntity bundleProductEntity = BundleProductEntity.builder()
                .id(UUID.randomUUID())
                .bundle(null)
                .product(productEntity)
                .quantity(1)
                .build();

        // Setup BundleEntity
        bundleEntity = BundleEntity.builder()
                .id(bundleUuid)
                .name("Test Bundle")
                .description("Test Description")
                .type(BundleType.FLIGHT_INCLUSIVE)
                .status(BundleStatus.ACTIVE)
                .bundleProducts(Set.of(bundleProductEntity))
                .build();

        // Setup Bundle Domain
        bundleDomain = new Bundle.Builder()
                .id(bundleUuid)
                .name("Test Bundle")
                .description("Test Description")
                .type(com.sarp.rule.domain.valueobject.bundle.BundleType.FLIGHT_INCLUSIVE)
                .status(com.sarp.rule.domain.valueobject.bundle.BundleStatus.ACTIVE)
                .build();

        // Setup VariantDTO
        VariantDTO variant = new VariantDTO().variantId(UUID.randomUUID()).quantity(1);

        // Setup BundleDTO
        bundleDTO = new BundleDTO();
        bundleDTO.setName("Test Bundle");
        bundleDTO.setDescription("Test Description");
        bundleDTO.setType(BundleTypeDTO.FLIGHT_INCLUSIVE);
        bundleDTO.setStatus(BundleStatusDTO.ACTIVE);
        bundleDTO.variants(List.of(variant)); // Add at least one product variant ID with quantity
    }

    @Test
    @DisplayName("Given a domain bundle, when toEntity is called, then correct entity should be returned")
    void givenDomainBundle_whenToEntityCalled_thenCorrectEntityReturned() {
        // When
        BundleEntity result = bundleMapper.toEntity(bundleDomain);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(bundleUuid);
        assertThat(result.getName()).isEqualTo("Test Bundle");
        assertThat(result.getDescription()).isEqualTo("Test Description");
        assertThat(result.getType()).isEqualTo(BundleType.FLIGHT_INCLUSIVE);
        assertThat(result.getStatus()).isEqualTo(BundleStatus.ACTIVE);
    }

    @Test
    @DisplayName("Given a bundle entity, when toDomain is called, then correct domain bundle should be returned")
    void givenBundleEntity_whenToDomainCalled_thenCorrectDomainReturned() {
        // When
        Bundle result = bundleMapper.toDomain(bundleEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(bundleUuid);
        assertThat(result.getName()).isEqualTo("Test Bundle");
        assertThat(result.getDescription()).isEqualTo("Test Description");
        assertThat(result.getType()).isEqualTo(com.sarp.rule.domain.valueobject.bundle.BundleType.FLIGHT_INCLUSIVE);
        assertThat(result.getStatus()).isEqualTo(com.sarp.rule.domain.valueobject.bundle.BundleStatus.ACTIVE);
    }

    @Test
    @DisplayName("Given a BundleDTO, when bundleDTOToBundle is called, then correct domain bundle should be returned")
    void givenBundleDTO_whenBundleDTOToBundleCalled_thenCorrectDomainReturned() {
        // When
        Bundle result = bundleMapper.bundleDTOToBundle(bundleDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Bundle");
        assertThat(result.getDescription()).isEqualTo("Test Description");
        assertThat(result.getType()).isEqualTo(com.sarp.rule.domain.valueobject.bundle.BundleType.FLIGHT_INCLUSIVE);
        assertThat(result.getStatus()).isEqualTo(com.sarp.rule.domain.valueobject.bundle.BundleStatus.ACTIVE);
        assertThat(result.getProductWithQuantities()).isNotEmpty();
    }

    @Test
    @DisplayName(
            "Given a domain bundle, when bundleToBundleResponseDTO is called, then correct response DTO should be returned")
    void givenDomainBundle_whenBundleToBundleResponseDTOCalled_thenCorrectResponseDTOReturned() {
        // When
        BundleResponseDTO result = bundleMapper.bundleToBundleResponseDTO(bundleDomain);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(bundleUuid);
        assertThat(result.getName()).isEqualTo("Test Bundle");
        assertThat(result.getDescription()).isEqualTo("Test Description");
        assertThat(result.getType()).isEqualTo(BundleTypeDTO.FLIGHT_INCLUSIVE);
        assertThat(result.getStatus()).isEqualTo(BundleStatusDTO.ACTIVE);
    }

    @Test
    @DisplayName(
            "Given a domain BundleStatus, when toBundleStatus is called, then correct persistence BundleStatus should be returned")
    void givenDomainBundleStatus_whenToBundleStatusCalled_thenCorrectPersistenceBundleStatusReturned() {
        // Given
        com.sarp.rule.domain.valueobject.bundle.BundleStatus domainStatus =
                com.sarp.rule.domain.valueobject.bundle.BundleStatus.ACTIVE;

        // When
        BundleStatus result = bundleMapper.toBundleStatus(domainStatus);

        // Then
        assertThat(result).isEqualTo(BundleStatus.ACTIVE);
    }

    @Test
    @DisplayName(
            "Given a domain BundleStatus PASSIVE, when toBundleStatus is called, then correct persistence BundleStatus should be returned")
    void givenDomainBundleStatusPassive_whenToBundleStatusCalled_thenCorrectPersistenceBundleStatusReturned() {
        // Given
        com.sarp.rule.domain.valueobject.bundle.BundleStatus domainStatus =
                com.sarp.rule.domain.valueobject.bundle.BundleStatus.PASSIVE;

        // When
        BundleStatus result = bundleMapper.toBundleStatus(domainStatus);

        // Then
        assertThat(result).isEqualTo(BundleStatus.PASSIVE);
    }

    @Test
    @DisplayName("Given null BundleDTO, when bundleDTOToBundle is called, then null should be returned")
    void givenNullBundleDTO_whenBundleDTOToBundleCalled_thenNullReturned() {
        // When
        Bundle result = bundleMapper.bundleDTOToBundle(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("Given null domain bundle, when toEntity is called, then null should be returned")
    void givenNullDomainBundle_whenToEntityCalled_thenNullReturned() {
        // When
        BundleEntity result = bundleMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("Given null bundle entity, when toDomain is called, then null should be returned")
    void givenNullBundleEntity_whenToDomainCalled_thenNullReturned() {
        // When
        Bundle result = bundleMapper.toDomain(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("Given null domain bundle, when bundleToBundleResponseDTO is called, then null should be returned")
    void givenNullDomainBundle_whenBundleToBundleResponseDTOCalled_thenNullReturned() {
        // When
        BundleResponseDTO result = bundleMapper.bundleToBundleResponseDTO(null);

        // Then
        assertThat(result).isNull();
    }
}
