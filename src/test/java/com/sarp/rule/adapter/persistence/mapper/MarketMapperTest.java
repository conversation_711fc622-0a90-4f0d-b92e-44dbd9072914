package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.sarp.generated.openapi.api.dto.MarketResponseDTO;
import com.sarp.rule.adapter.persistence.model.entity.MarketEntity;
import com.sarp.rule.adapter.persistence.model.entity.PortEntity;
import com.sarp.rule.domain.entity.Market;
import com.sarp.rule.domain.entity.Port;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class MarketMapperTest {

    @Mock
    private PortMapper portMapper;

    @Spy
    @InjectMocks
    private MarketMapper marketMapper = Mappers.getMapper(MarketMapper.class);

    private UUID marketId;
    private String marketName;
    private Instant now;
    private String createdBy;
    private String updatedBy;

    @BeforeEach
    void setUp() {
        marketId = UUID.randomUUID();
        marketName = "Test Market";
        now = Instant.now();
        createdBy = "creator";
        updatedBy = "updater";

        // Inject portMapper into marketMapper
        ReflectionTestUtils.setField(marketMapper, "portMapper", portMapper);
    }

    @Test
    @DisplayName("Should map MarketEntity to Market domain object")
    void toDomain_ShouldMapEntityToDomain() {
        // Given
        PortEntity portEntity = PortEntity.builder()
                .portCode("IST")
                .portName("Istanbul")
                .active(true)
                .build();

        Port expectedPort =
                Port.builder().portCode("IST").portName("Istanbul").active(true).build();

        MarketEntity marketEntity = MarketEntity.builder()
                .id(marketId)
                .name(marketName)
                .ports(Set.of(portEntity))
                .build();

        marketEntity.setCreatedAt(now);
        marketEntity.setUpdatedAt(now);
        marketEntity.setCreatedBy(createdBy);
        marketEntity.setUpdatedBy(updatedBy);

        when(portMapper.toDomain(any(PortEntity.class))).thenReturn(expectedPort);

        // When
        Market result = marketMapper.toDomain(marketEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getName()).isEqualTo(marketName);
        assertThat(result.getCreatedAt()).isEqualTo(now);
        assertThat(result.getUpdatedAt()).isEqualTo(now);
        assertThat(result.getCreatedBy()).isEqualTo(createdBy);
        assertThat(result.getUpdatedBy()).isEqualTo(updatedBy);

        assertThat(result.getPorts()).hasSize(1).element(0).satisfies(port -> {
            assertThat(port.getPortCode()).isEqualTo("IST");
            assertThat(port.getPortName()).isEqualTo("Istanbul");
            assertThat(port.isActive()).isTrue();
        });
    }

    @Test
    @DisplayName("Should map Market domain object to MarketEntity")
    void toEntity_ShouldMapDomainToEntity() {
        // Given
        Port port =
                Port.builder().portCode("IST").portName("Istanbul").active(true).build();

        PortEntity expectedPortEntity = PortEntity.builder()
                .portCode("IST")
                .portName("Istanbul")
                .active(true)
                .build();

        Market market = Market.builder()
                .id(marketId)
                .name(marketName)
                .ports(new HashSet<>(Set.of(port)))
                .createdAt(now)
                .updatedAt(now)
                .createdBy(createdBy)
                .updatedBy(updatedBy)
                .build();

        when(portMapper.toEntity(any(Port.class))).thenReturn(expectedPortEntity);

        // When
        MarketEntity result = marketMapper.toEntity(market);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(marketId);
        assertThat(result.getName()).isEqualTo(marketName);

        assertThat(result.getPorts()).hasSize(1).element(0).satisfies(portEntity -> {
            assertThat(portEntity.getPortCode()).isEqualTo("IST");
            assertThat(portEntity.getPortName()).isEqualTo("Istanbul");
            assertThat(portEntity.isActive()).isTrue();
        });
    }

    @Test
    @DisplayName("Should map Market domain object to MarketResponseDTO")
    void toMarketResponseDTO_ShouldMapDomainToDTO() {
        // Given
        Port port =
                Port.builder().portCode("IST").portName("Istanbul").active(true).build();

        Market market = Market.builder()
                .id(marketId)
                .name(marketName)
                .ports(new HashSet<>(Set.of(port)))
                .build();

        // When
        MarketResponseDTO result = marketMapper.toMarketResponseDTO(market);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo(marketName);
        // Add more assertions based on your MarketResponseDTO structure
    }

    @Test
    @DisplayName("Should handle null inputs gracefully")
    void shouldHandleNullInputs() {
        // When/Then
        assertThat(marketMapper.toDomain(null)).isNull();
        assertThat(marketMapper.toEntity(null)).isNull();
        assertThat(marketMapper.toMarketResponseDTO(null)).isNull();
    }

    @Test
    @DisplayName("Should handle empty collections in DTO mapping")
    void shouldHandleEmptyCollections() {
        // Given
        MarketEntity marketEntity = MarketEntity.builder()
                .id(marketId)
                .name(marketName)
                .ports(new HashSet<>())
                .build();

        Market market = Market.builder()
                .id(marketId)
                .name(marketName)
                .ports(new HashSet<>())
                .build();

        // When
        Market domainResult = marketMapper.toDomain(marketEntity);
        MarketEntity entityResult = marketMapper.toEntity(market);
        MarketResponseDTO dtoResult = marketMapper.toMarketResponseDTO(market);

        // Then
        assertThat(domainResult.getPorts()).isEmpty();
        assertThat(entityResult.getPorts()).isEmpty();
        assertThat(dtoResult.getPorts()).isEmpty();
        assertThat(dtoResult.getId()).isEqualTo(marketId);
        assertThat(dtoResult.getName()).isEqualTo(marketName);
    }
}
