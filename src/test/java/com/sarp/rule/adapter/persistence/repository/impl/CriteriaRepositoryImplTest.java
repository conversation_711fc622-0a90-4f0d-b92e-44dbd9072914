package com.sarp.rule.adapter.persistence.repository.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sarp.rule.adapter.persistence.mapper.CriteriaMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaJpaRepository;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaRepositoryImplTest {
    @Mock
    private CriteriaJpaRepository criteriaJpaRepository;

    @Mock
    private CriteriaMapper criteriaMapper;

    @InjectMocks
    private CriteriaRepositoryImpl criteriaRepository;

    private UUID criteriaId;
    private CriteriaEntity criteriaEntity;
    private Criteria criteriaDomain;

    @BeforeEach
    void setUp() {
        criteriaId = UUID.randomUUID();

        criteriaEntity = CriteriaEntity.builder()
                .id(criteriaId)
                .name("Test Criteria")
                .description("Description")
                .type(com.sarp.rule.adapter.persistence.model.enums.CriteriaType.USER_DEFINED)
                .requestType(com.sarp.rule.adapter.persistence.model.enums.RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(com.sarp.rule.adapter.persistence.model.enums.FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .criteriaGroup(
                        CriteriaGroupEntity.builder().id(UUID.randomUUID()).build())
                .build();

        criteriaDomain = new Criteria.Builder()
                .id(criteriaId)
                .name("Test Criteria")
                .description("Description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .criteriaGroupId(UUID.randomUUID())
                .build();
    }

    @Test
    @DisplayName("Given a domain criteria, when save is called, then saved criteria should be returned")
    void givenDomainCriteria_whenSaveCalled_thenSavedCriteriaReturned() {
        when(criteriaMapper.toCriteriaEntity(criteriaDomain)).thenReturn(criteriaEntity);
        when(criteriaJpaRepository.save(criteriaEntity)).thenReturn(criteriaEntity);
        when(criteriaMapper.toCriteriaDomain(criteriaEntity)).thenReturn(criteriaDomain);

        Criteria result = criteriaRepository.save(criteriaDomain);

        assertThat(result).isNotNull().isEqualTo(criteriaDomain);

        verify(criteriaMapper).toCriteriaEntity(criteriaDomain);
        verify(criteriaJpaRepository).save(criteriaEntity);
        verify(criteriaMapper).toCriteriaDomain(criteriaEntity);
    }

    @Test
    @DisplayName(
            "Given a criteria ID that exists, when findById is called, then corresponding criteria should be returned")
    void givenExistingCriteriaId_whenFindByIdCalled_thenCriteriaReturned() {
        when(criteriaJpaRepository.findById(criteriaId)).thenReturn(Optional.of(criteriaEntity));
        when(criteriaMapper.toCriteriaDomain(criteriaEntity)).thenReturn(criteriaDomain);

        Optional<Criteria> found = criteriaRepository.findById(criteriaId);

        assertThat(found).isPresent().contains(criteriaDomain);

        verify(criteriaJpaRepository).findById(criteriaId);
        verify(criteriaMapper).toCriteriaDomain(criteriaEntity);
    }

    @Test
    @DisplayName(
            "Given a criteria ID that does not exist, when findById is called, then empty optional should be returned")
    void givenNonExistingCriteriaId_whenFindByIdCalled_thenEmptyOptionalReturned() {
        when(criteriaJpaRepository.findById(criteriaId)).thenReturn(Optional.empty());

        Optional<Criteria> found = criteriaRepository.findById(criteriaId);

        assertThat(found).isEmpty();

        verify(criteriaJpaRepository).findById(criteriaId);
        verify(criteriaMapper, never()).toCriteriaDomain((CriteriaEntity) any());
    }

    @Test
    @DisplayName("Given a domain criteria, when save is called, then proper sequence of operations should be executed")
    void givenDomainCriteria_whenSaveCalled_thenProperSequenceExecuted() {
        when(criteriaMapper.toCriteriaEntity(criteriaDomain)).thenReturn(criteriaEntity);
        when(criteriaJpaRepository.save(criteriaEntity)).thenReturn(criteriaEntity);
        when(criteriaMapper.toCriteriaDomain(criteriaEntity)).thenReturn(criteriaDomain);

        criteriaRepository.save(criteriaDomain);

        verify(criteriaMapper, times(1)).toCriteriaEntity(criteriaDomain);
        verify(criteriaJpaRepository, times(1)).save(criteriaEntity);
        verify(criteriaMapper, times(1)).toCriteriaDomain(criteriaEntity);
    }

    @Test
    @DisplayName(
            "Given a criteria ID, when deleteCriteriaById is called, then the correct repository method should be invoked")
    void givenCriteriaId_whenDeleteCriteriaByIdCalled_thenRepositoryDeleteInvoked() {
        criteriaRepository.deleteById(criteriaId);

        verify(criteriaJpaRepository).deleteById(criteriaId);
    }
}
