package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.generated.openapi.api.dto.CreateCriteriaGroupRequestDTO;
import com.sarp.generated.openapi.api.dto.CriteriaDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupResponseDTO;
import com.sarp.generated.openapi.api.dto.CriteriaGroupWithCriteriaResponseDTO;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.adapter.persistence.model.enums.RuleType;
import com.sarp.rule.domain.entity.Criteria;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.valueobject.criteria.CriteriaType;
import com.sarp.rule.domain.valueobject.criteria.FieldType;
import com.sarp.rule.domain.valueobject.criteria.RequestType;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class CriteriaGroupMapperTest {

    CriteriaMapper criteriaMapper = Mappers.getMapper(CriteriaMapper.class);
    private CriteriaGroupMapper criteriaGroupMapper;

    private UUID criteriaGroupUuid;
    private CriteriaGroupEntity criteriaGroupEntity;
    private CriteriaGroup criteriaGroupDomain;
    private CreateCriteriaGroupRequestDTO createCriteriaGroupRequestDTO;
    private List<Criteria> criteriaList;

    @SneakyThrows
    @BeforeEach
    void setUp() {

        criteriaGroupMapper = Mappers.getMapper(CriteriaGroupMapper.class);
        Field field = CriteriaGroupMapperImpl.class.getDeclaredField("criteriaMapper");
        field.setAccessible(true);
        field.set(criteriaGroupMapper, criteriaMapper);

        criteriaGroupUuid = UUID.randomUUID();
        UUID criteriaGroupId = criteriaGroupUuid;

        // Create criteria list
        UUID criteriaUuid1 = UUID.randomUUID();
        UUID criteriaUuid2 = UUID.randomUUID();

        Criteria criteria1 = Criteria.builder()
                .id(criteriaUuid1)
                .name("Criteria 1")
                .description("Test Description 1")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field1")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .criteriaGroupId(criteriaGroupUuid)
                .build();

        Criteria criteria2 = Criteria.builder()
                .id(criteriaUuid2)
                .name("Criteria 2")
                .description("Test Description 2")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field2")
                .fieldType(FieldType.TEXT)
                .criteriaGroupId(criteriaGroupUuid)
                .build();

        criteriaList = new ArrayList<>();
        criteriaList.add(criteria1);
        criteriaList.add(criteria2);

        // Create criteria entity list
        CriteriaEntity criteriaEntity1 = new CriteriaEntity();
        criteriaEntity1.setId(criteriaUuid1);
        criteriaEntity1.setName("Criteria 1");
        criteriaEntity1.setDescription("Test Description 1");
        criteriaEntity1.setType(com.sarp.rule.adapter.persistence.model.enums.CriteriaType.USER_DEFINED);
        criteriaEntity1.setRequestType(com.sarp.rule.adapter.persistence.model.enums.RequestType.OFFER_REQUEST);
        criteriaEntity1.setMappingField("field1");
        criteriaEntity1.setFieldType(com.sarp.rule.adapter.persistence.model.enums.FieldType.INTEGER);
        criteriaEntity1.setMinValue(BigDecimal.ZERO);
        criteriaEntity1.setMaxValue(BigDecimal.TEN);

        CriteriaEntity criteriaEntity2 = new CriteriaEntity();
        criteriaEntity2.setId(criteriaUuid2);
        criteriaEntity2.setName("Criteria 2");
        criteriaEntity2.setDescription("Test Description 2");
        criteriaEntity2.setType(com.sarp.rule.adapter.persistence.model.enums.CriteriaType.USER_DEFINED);
        criteriaEntity2.setRequestType(com.sarp.rule.adapter.persistence.model.enums.RequestType.OFFER_REQUEST);
        criteriaEntity2.setMappingField("field2");
        criteriaEntity2.setFieldType(com.sarp.rule.adapter.persistence.model.enums.FieldType.TEXT);

        List<CriteriaEntity> criteriaEntityList = new ArrayList<>();
        criteriaEntityList.add(criteriaEntity1);
        criteriaEntityList.add(criteriaEntity2);

        // Create criteria group
        criteriaGroupDomain = new CriteriaGroup.Builder()
                .id(criteriaGroupId)
                .name("Test Criteria Group")
                .displayOrder(1)
                .criteriaList(criteriaList)
                .build();

        // Create criteria group entity
        criteriaGroupEntity = new CriteriaGroupEntity();
        criteriaGroupEntity.setId(criteriaGroupUuid);
        criteriaGroupEntity.setName("Test Criteria Group");
        criteriaGroupEntity.setDisplayOrder(1);
        criteriaGroupEntity.setCriteriaList(criteriaEntityList);

        // Create criteria group request DTO
        createCriteriaGroupRequestDTO = new CreateCriteriaGroupRequestDTO();
        createCriteriaGroupRequestDTO.setName("Test Criteria Group");
    }

    @Test
    @DisplayName("Should map domain criteria group to entity")
    void shouldMapDomainCriteriaGroupToEntity() {
        // When
        CriteriaGroupEntity result = criteriaGroupMapper.toEntity(criteriaGroupDomain);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaGroupEntity.getId());
        assertThat(result.getName()).isEqualTo(criteriaGroupEntity.getName());
        assertThat(result.getDisplayOrder()).isEqualTo(criteriaGroupEntity.getDisplayOrder());
    }

    @Test
    @DisplayName("Should map entity to domain criteria group")
    void shouldMapEntityToDomainCriteriaGroup() {
        // When
        CriteriaGroup result = criteriaGroupMapper.toDomain(criteriaGroupEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaGroupDomain.getId());
        assertThat(result.getName()).isEqualTo(criteriaGroupDomain.getName());
        assertThat(result.getDisplayOrder()).isEqualTo(criteriaGroupDomain.getDisplayOrder());
    }

    @Test
    @DisplayName("Should map domain criteria group to response DTO")
    void shouldMapDomainCriteriaGroupToResponseDTO() {
        // When
        CriteriaGroupResponseDTO result = criteriaGroupMapper.toCriteriaGroupResponseDTO(criteriaGroupDomain);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteriaGroupUuid);
        assertThat(result.getName()).isEqualTo("Test Criteria Group");
        assertThat(result.getDisplayOrder()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should map request DTO to domain criteria group with display number")
    void shouldMapRequestDTOToDomainCriteriaGroup() {
        // Given
        Integer displayOrder = 5;

        // When
        CriteriaGroup result = criteriaGroupMapper.toDomain(displayOrder, createCriteriaGroupRequestDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Criteria Group");
        assertThat(result.getDisplayOrder()).isEqualTo(displayOrder);
        assertThat(result.getCriteriaList()).isEmpty();
    }

    @Test
    @DisplayName("Should map domain criteria group to detail DTO")
    void shouldMapDomainCriteriaGroupToDetailDTO() {
        // When
        CriteriaGroupWithCriteriaResponseDTO result =
                criteriaGroupMapper.toCriteriaGroupWithCriteriaResponseDTO(criteriaGroupDomain);

        // Then
        assertThat(result).isNotNull();

        assertThat(result.getId()).isEqualTo(criteriaGroupDomain.getId());
        assertThat(result.getName()).isEqualTo(criteriaGroupDomain.getName());
        assertThat(result.getDisplayOrder()).isEqualTo(criteriaGroupDomain.getDisplayOrder());

        assertThat(result.getCriteriaList()).hasSize(criteriaList.size());
        assertThat(result.getCriteriaList().get(0).getName())
                .isEqualTo(criteriaList.get(0).getName());
        assertThat(result.getCriteriaList().get(1).getName())
                .isEqualTo(criteriaList.get(1).getName());
    }

    @Test
    @DisplayName("Should map criteria to criteria DTO")
    void shouldMapCriteriaToCriteriaDTO() {
        // Given
        Criteria criteria = criteriaList.getFirst();

        // When
        CriteriaDTO result = criteriaGroupMapper.toCriteriaDTO(criteria);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(criteria.getId());
        assertThat(result.getName()).isEqualTo(criteria.getName());
        assertThat(result.getDescription()).isEqualTo(criteria.getDescription());
        assertThat(result.getType())
                .isEqualTo(CriteriaDTO.TypeEnum.valueOf(criteria.getType().name()));
        assertThat(result.getRequestType())
                .isEqualTo(CriteriaDTO.RequestTypeEnum.valueOf(
                        criteria.getRequestType().name()));
        assertThat(result.getMappingField()).isEqualTo(criteria.getMappingField());
        assertThat(result.getFieldType())
                .isEqualTo(CriteriaDTO.FieldTypeEnum.valueOf(
                        criteria.getFieldType().name()));
        assertThat(result.getMinValue()).isEqualTo(criteria.getMinValue().toString());
        assertThat(result.getMaxValue()).isEqualTo(criteria.getMaxValue().toString());
    }

    @Test
    @DisplayName("Should map criteria list to criteria DTO list")
    void shouldMapCriteriaListToCriteriaDTOList() {
        // When
        List<CriteriaDTO> result = criteriaGroupMapper.toCriteriaDTOList(criteriaList);

        // Then
        assertThat(result).isNotNull().hasSize(2);
        assertThat(result.get(0).getName()).isEqualTo(criteriaList.get(0).getName());
        assertThat(result.get(1).getName()).isEqualTo(criteriaList.get(1).getName());
    }

    @Test
    @DisplayName("Should map criteria group list to criteria group detail DTO list")
    void shouldMapCriteriaGroupListToCriteriaGroupDetailDTOList() {
        // Given
        List<CriteriaGroup> criteriaGroups = new ArrayList<>();
        criteriaGroups.add(criteriaGroupDomain);

        // When
        List<CriteriaGroupWithCriteriaResponseDTO> result =
                criteriaGroupMapper.toCriteriaGroupWithCriteriaResponseDTOList(criteriaGroups);

        // Then
        assertThat(result).isNotNull().hasSize(criteriaGroups.size());

        CriteriaGroupWithCriteriaResponseDTO firstResult = result.getFirst();
        assertThat(firstResult).isNotNull();
        assertThat(firstResult.getName()).isEqualTo(criteriaGroupDomain.getName());
        assertThat(firstResult.getCriteriaList()).isNotNull();
        assertThat(firstResult.getCriteriaList())
                .hasSize(criteriaGroupDomain.getCriteriaList().size());
    }

    @Test
    @DisplayName("Should handle null criteria list when mapping to DTO list")
    void shouldHandleNullCriteriaListWhenMappingToDTOList() {
        // When
        List<CriteriaDTO> result = criteriaGroupMapper.toCriteriaDTOList(null);

        // Then
        assertThat(result).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle null criteria group list when mapping to detail DTO list")
    void shouldHandleNullCriteriaGroupListWhenMappingToDetailDTOList() {
        // When
        List<CriteriaGroupWithCriteriaResponseDTO> result =
                criteriaGroupMapper.toCriteriaGroupWithCriteriaResponseDTOList(null);

        // Then
        assertThat(result).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should map CriteriaGroupEntity to CriteriaGroupWithDetails")
    void shouldMapEntityToCriteriaGroupWithDetails() {
        // When
        var result = criteriaGroupMapper.entityToDetails(criteriaGroupEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.id()).isEqualTo(criteriaGroupEntity.getId());
        assertThat(result.name()).isEqualTo(criteriaGroupEntity.getName());
        assertThat(result.displayOrder()).isEqualTo(criteriaGroupEntity.getDisplayOrder());

        assertThat(result.criteriaDetails()).isNotNull();
        assertThat(result.criteriaDetails())
                .hasSize(criteriaGroupEntity.getCriteriaList().size());
    }

    @Test
    @DisplayName("Should map CriteriaEntity to CriteriaWithConfigDetails")
    void shouldMapEntityToCriteriaWithConfigDetails() {
        // Given
        var criteriaEntity = criteriaGroupEntity.getCriteriaList().getFirst();

        var criteriaConfig = new CriteriaConfigEntity();
        criteriaConfig.setDisplayOrder(5);

        List<RuleType> allowedRules = new ArrayList<>();
        allowedRules.add(RuleType.ALA_CARTE);
        criteriaConfig.setAllowedRules(allowedRules);

        List<RuleType> mandatoryRules = new ArrayList<>();
        mandatoryRules.add(RuleType.BUNDLE);
        criteriaConfig.setMandatoryRules(mandatoryRules);

        criteriaEntity.setCriteriaConfig(criteriaConfig);

        // When
        var result = criteriaGroupMapper.entityToDetails(criteriaEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.criteriaId()).isEqualTo(criteriaEntity.getId());
        assertThat(result.criteriaName()).isEqualTo(criteriaEntity.getName());
        assertThat(result.displayOrder()).isEqualTo(criteriaConfig.getDisplayOrder());

        assertThat(result.allowedRules()).isNotNull();
        assertThat(result.allowedRules()).hasSize(1);
        assertThat(result.allowedRules().getFirst().name()).isEqualTo("ALA_CARTE");

        assertThat(result.mandatoryRules()).isNotNull();
        assertThat(result.mandatoryRules()).hasSize(1);
        assertThat(result.mandatoryRules().getFirst().name()).isEqualTo("BUNDLE");
    }

    @Test
    @DisplayName("Should handle empty lists when mapping CriteriaGroupEntity to CriteriaGroupWithDetails")
    void shouldHandleEmptyCriteriaListWhenMappingToGroupWithDetails() {
        // Given
        var entityWithEmptyList = new CriteriaGroupEntity();
        entityWithEmptyList.setId(UUID.randomUUID());
        entityWithEmptyList.setName("Empty Group");
        entityWithEmptyList.setDisplayOrder(3);
        entityWithEmptyList.setCriteriaList(new ArrayList<>());

        // When
        var result = criteriaGroupMapper.entityToDetails(entityWithEmptyList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.id()).isEqualTo(entityWithEmptyList.getId());
        assertThat(result.name()).isEqualTo(entityWithEmptyList.getName());
        assertThat(result.displayOrder()).isEqualTo(entityWithEmptyList.getDisplayOrder());
        assertThat(result.criteriaDetails()).isEmpty();
    }
}
