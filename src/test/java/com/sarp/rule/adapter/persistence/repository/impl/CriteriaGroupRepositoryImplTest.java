package com.sarp.rule.adapter.persistence.repository.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sarp.rule.adapter.persistence.mapper.CriteriaGroupMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaGroupEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaGroupJpaRepository;
import com.sarp.rule.domain.entity.CriteriaGroup;
import com.sarp.rule.domain.valueobject.criteriagroup.CriteriaGroupWithDetails;
import com.sarp.rule.domain.valueobject.pagination.PaginatedResult;
import com.sarp.rule.domain.valueobject.pagination.PagingParams;
import com.sarp.rule.domain.valueobject.rule.RuleType;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

@ExtendWith(MockitoExtension.class)
class CriteriaGroupRepositoryImplTest {

    @Mock
    private CriteriaGroupJpaRepository criteriaGroupJpaRepository;

    @Mock
    private CriteriaGroupMapper criteriaGroupMapper;

    @InjectMocks
    private CriteriaGroupRepositoryImpl criteriaGroupRepository;

    private UUID criteriaGroupId;
    private CriteriaGroup criteriaGroup;
    private CriteriaGroupEntity criteriaGroupEntity;

    @BeforeEach
    void setUp() {
        criteriaGroupId = UUID.randomUUID();

        criteriaGroup = new CriteriaGroup.Builder()
                .id(criteriaGroupId)
                .name("Test Group")
                .displayOrder(1)
                .build();

        criteriaGroupEntity = new CriteriaGroupEntity();
        criteriaGroupEntity.setId(criteriaGroupId);
        criteriaGroupEntity.setName("Test Group");
        criteriaGroupEntity.setDisplayOrder(1);

        // reset mocks
        Mockito.reset(criteriaGroupJpaRepository, criteriaGroupMapper);
    }

    @Test
    @DisplayName("Should save criteria group without criteria list")
    void shouldSaveCriteriaGroup() {
        // Given
        when(criteriaGroupMapper.toEntityWithoutCriteriaList(criteriaGroup)).thenReturn(criteriaGroupEntity);

        when(criteriaGroupJpaRepository.save(criteriaGroupEntity)).thenReturn(criteriaGroupEntity);
        when(criteriaGroupMapper.toDomain(criteriaGroupEntity)).thenReturn(criteriaGroup);

        // When
        CriteriaGroup result = criteriaGroupRepository.save(criteriaGroup);

        // Then
        assertThat(result).isEqualTo(criteriaGroup);

        verify(criteriaGroupMapper).toEntityWithoutCriteriaList(criteriaGroup);
        verify(criteriaGroupJpaRepository).save(criteriaGroupEntity);
        verify(criteriaGroupMapper).toDomain(criteriaGroupEntity);
    }

    @Test
    @DisplayName("Should find criteria group by ID without criteria list")
    void shouldFindCriteriaGroupById() {
        // Given
        when(criteriaGroupJpaRepository.findById(criteriaGroupId)).thenReturn(Optional.of(criteriaGroupEntity));
        when(criteriaGroupMapper.toDomainWithoutCriteriaList(criteriaGroupEntity))
                .thenReturn(criteriaGroup);

        // When
        Optional<CriteriaGroup> result = criteriaGroupRepository.findByIdWithoutCriteriaList(criteriaGroupId);

        // Then
        assertThat(result).isPresent().contains(criteriaGroup);

        verify(criteriaGroupJpaRepository).findById(criteriaGroupId);
        verify(criteriaGroupMapper).toDomainWithoutCriteriaList(criteriaGroupEntity);
    }

    @Test
    @DisplayName("Should find all criteria groups with criteria details using pagination")
    void shouldFindAllCriteriaGroupsWithCriteriaDetails() {
        // Given
        CriteriaGroupEntity criteriaGroupEntity2 = new CriteriaGroupEntity();
        criteriaGroupEntity2.setId(UUID.randomUUID());
        criteriaGroupEntity2.setName("Test Group 2");
        criteriaGroupEntity2.setDisplayOrder(2);

        CriteriaGroup criteriaGroup2 = new CriteriaGroup.Builder()
                .id(criteriaGroupEntity2.getId())
                .name("Test Group 2")
                .displayOrder(2)
                .build();

        List<CriteriaGroupEntity> criteriaGroupEntities = Arrays.asList(criteriaGroupEntity, criteriaGroupEntity2);
        List<CriteriaGroup> criteriaGroups = Arrays.asList(criteriaGroup, criteriaGroup2);

        // Configure paging parameters
        int page = 0;
        int size = 10;
        PagingParams pagingParams = PagingParams.of(page, size);

        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "displayOrder"));
        Page<CriteriaGroupEntity> pagedResult =
                new PageImpl<>(criteriaGroupEntities, pageRequest, criteriaGroupEntities.size());

        when(criteriaGroupJpaRepository.findAll(pageRequest)).thenReturn(pagedResult);
        when(criteriaGroupMapper.toDomain(criteriaGroupEntity)).thenReturn(criteriaGroup);
        when(criteriaGroupMapper.toDomain(criteriaGroupEntity2)).thenReturn(criteriaGroup2);

        // When
        PaginatedResult<CriteriaGroup> result = criteriaGroupRepository.findAllWithCriteriaDetails(pagingParams);

        // Then
        assertThat(result.getItems()).hasSize(2);
        assertThat(result.getItems()).isEqualTo(criteriaGroups);

        verify(criteriaGroupJpaRepository).findAll(pageRequest);
        verify(criteriaGroupMapper).toDomain(criteriaGroupEntity);
        verify(criteriaGroupMapper).toDomain(criteriaGroupEntity2);
    }

    @Test
    @DisplayName("Should find all criteria groups with details by rule type")
    void shouldFindAllWithDetailsByRuleType() {
        // Given
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        CriteriaGroupEntity group1 = createCriteriaGroupEntity(id1, "Group 1", 1);
        CriteriaGroupEntity group2 = createCriteriaGroupEntity(id2, "Group 2", 2);
        List<CriteriaGroupEntity> entities = List.of(group1, group2);

        CriteriaGroupWithDetails details1 = Mockito.mock(CriteriaGroupWithDetails.class);
        CriteriaGroupWithDetails details2 = Mockito.mock(CriteriaGroupWithDetails.class);

        int page = 0;
        int size = 10;
        PagingParams pagingParams = PagingParams.of(page, size);
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "display_order"));
        Page<CriteriaGroupEntity> pageResult = new PageImpl<>(entities, pageRequest, entities.size());

        RuleType ruleType = RuleType.BUNDLE;
        com.sarp.rule.adapter.persistence.model.enums.RuleType entityRuleType =
                com.sarp.rule.adapter.persistence.model.enums.RuleType.BUNDLE;

        // Use Mockito.any() for the second argument to handle the JSON string that's generated
        when(criteriaGroupMapper.toRuleType(ruleType)).thenReturn(entityRuleType);
        when(criteriaGroupJpaRepository.findAllWithDetailsByRuleType(
                        Mockito.eq(pageRequest), Mockito.any(String.class)))
                .thenReturn(pageResult);
        when(criteriaGroupMapper.entityToDetails(group1)).thenReturn(details1);
        when(criteriaGroupMapper.entityToDetails(group2)).thenReturn(details2);

        // When
        PaginatedResult<CriteriaGroupWithDetails> result =
                criteriaGroupRepository.findAllWithDetailsByRuleType(ruleType, pagingParams);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getItems()).containsExactly(details1, details2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getTotalPages()).isEqualTo(1);

        verify(criteriaGroupMapper).toRuleType(ruleType);
        // Use Mockito.any() here too for consistency
        verify(criteriaGroupJpaRepository)
                .findAllWithDetailsByRuleType(Mockito.eq(pageRequest), Mockito.any(String.class));
        verify(criteriaGroupMapper).entityToDetails(group1);
        verify(criteriaGroupMapper).entityToDetails(group2);
    }

    @Test
    @DisplayName("Should find all criteria groups with details when rule type is null")
    void shouldFindAllWithDetailsByRuleTypeWhenRuleTypeIsNull() {
        // Given
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        CriteriaGroupEntity group1 = createCriteriaGroupEntity(id1, "Group 1", 1);
        CriteriaGroupEntity group2 = createCriteriaGroupEntity(id2, "Group 2", 2);
        List<CriteriaGroupEntity> entities = List.of(group1, group2);

        CriteriaGroupWithDetails details1 = Mockito.mock(CriteriaGroupWithDetails.class);
        CriteriaGroupWithDetails details2 = Mockito.mock(CriteriaGroupWithDetails.class);

        int page = 0;
        int size = 10;
        PagingParams pagingParams = PagingParams.of(page, size);
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "display_order"));
        Page<CriteriaGroupEntity> pageResult = new PageImpl<>(entities, pageRequest, entities.size());

        when(criteriaGroupJpaRepository.findAllWithDetailsByRuleType(Mockito.eq(pageRequest), Mockito.isNull()))
                .thenReturn(pageResult);
        when(criteriaGroupMapper.entityToDetails(group1)).thenReturn(details1);
        when(criteriaGroupMapper.entityToDetails(group2)).thenReturn(details2);

        // When
        PaginatedResult<CriteriaGroupWithDetails> result =
                criteriaGroupRepository.findAllWithDetailsByRuleType(null, pagingParams);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getItems()).containsExactly(details1, details2);
        assertThat(result.getTotalElements()).isEqualTo(2);

        verify(criteriaGroupJpaRepository).findAllWithDetailsByRuleType(Mockito.eq(pageRequest), Mockito.isNull());
        verify(criteriaGroupMapper).entityToDetails(group1);
        verify(criteriaGroupMapper).entityToDetails(group2);
    }

    private CriteriaGroupEntity createCriteriaGroupEntity(UUID id, String name, int displayOrder) {
        CriteriaGroupEntity entity = new CriteriaGroupEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setDisplayOrder(displayOrder);
        return entity;
    }
}
