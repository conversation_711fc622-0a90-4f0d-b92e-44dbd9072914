package com.sarp.rule.adapter.persistence.repository.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sarp.rule.adapter.persistence.mapper.CriteriaConfigMapper;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaConfigEntity;
import com.sarp.rule.adapter.persistence.model.entity.CriteriaEntity;
import com.sarp.rule.adapter.persistence.repository.CriteriaConfigJpaRepository;
import com.sarp.rule.domain.entity.CriteriaConfig;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CriteriaConfigRepositoryImplTest {

    @Mock
    private CriteriaConfigJpaRepository criteriaConfigJpaRepository;

    @Mock
    private CriteriaConfigMapper criteriaConfigMapper;

    @InjectMocks
    private CriteriaConfigRepositoryImpl criteriaConfigRepository;

    private UUID criteriaConfigUuid;
    private CriteriaConfig criteriaConfig;
    private CriteriaConfigEntity criteriaConfigEntity;

    @BeforeEach
    void setUp() {
        criteriaConfigUuid = UUID.randomUUID();

        criteriaConfig = CriteriaConfig.builder()
                .id(criteriaConfigUuid)
                .criteriaId(criteriaConfigUuid)
                .displayOrder(1)
                .build();

        criteriaConfigEntity = new CriteriaConfigEntity();
        criteriaConfigEntity.setId(criteriaConfigUuid);
        criteriaConfigEntity.setCriteria(
                CriteriaEntity.builder().id(criteriaConfigUuid).build());
        criteriaConfigEntity.setDisplayOrder(1);
    }

    @Test
    @DisplayName("Should save a criteria config")
    void shouldSaveCriteriaConfig() {
        // Given
        when(criteriaConfigMapper.toCriteriaConfigEntity(criteriaConfig)).thenReturn(criteriaConfigEntity);
        when(criteriaConfigJpaRepository.save(criteriaConfigEntity)).thenReturn(criteriaConfigEntity);
        when(criteriaConfigMapper.toCriteriaConfigDomain(criteriaConfigEntity)).thenReturn(criteriaConfig);

        // When
        CriteriaConfig result = criteriaConfigRepository.save(criteriaConfig);

        // Then
        assertThat(result).isEqualTo(criteriaConfig);

        verify(criteriaConfigMapper).toCriteriaConfigEntity(criteriaConfig);
        verify(criteriaConfigJpaRepository).save(criteriaConfigEntity);
        verify(criteriaConfigMapper).toCriteriaConfigDomain(criteriaConfigEntity);
    }

    @Test
    @DisplayName("Should find all criteria configs by criteria IDs")
    void shouldFindCriteriaConfigsByCriteriaIds() {
        // Given
        UUID anotherCriteriaUuid = UUID.randomUUID();

        CriteriaConfigEntity anotherEntity = new CriteriaConfigEntity();
        anotherEntity.setId(anotherCriteriaUuid);
        anotherEntity.setCriteria(
                CriteriaEntity.builder().id(anotherCriteriaUuid).build());

        CriteriaConfig anotherDomain = CriteriaConfig.builder()
                .id(anotherCriteriaUuid)
                .criteriaId(anotherCriteriaUuid)
                .displayOrder(1)
                .build();

        List<CriteriaConfigEntity> entityList = Arrays.asList(criteriaConfigEntity, anotherEntity);
        List<UUID> criteriaIds = Arrays.asList(criteriaConfigUuid, anotherCriteriaUuid);

        when(criteriaConfigJpaRepository.findByCriteriaIdIn(criteriaIds)).thenReturn(entityList);
        when(criteriaConfigMapper.toCriteriaConfigDomain(criteriaConfigEntity)).thenReturn(criteriaConfig);
        when(criteriaConfigMapper.toCriteriaConfigDomain(anotherEntity)).thenReturn(anotherDomain);

        // When
        List<CriteriaConfig> result = criteriaConfigRepository.findByCriteriaIdIn(criteriaIds);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.stream().map(CriteriaConfig::getCriteriaId).collect(Collectors.toList()))
                .containsExactlyInAnyOrder(criteriaConfigUuid, anotherCriteriaUuid);

        verify(criteriaConfigJpaRepository).findByCriteriaIdIn(criteriaIds);
        verify(criteriaConfigMapper).toCriteriaConfigDomain(criteriaConfigEntity);
        verify(criteriaConfigMapper).toCriteriaConfigDomain(anotherEntity);
    }

    @Test
    @DisplayName("Should save all criteria configs")
    void shouldSaveAllCriteriaConfigs() {
        // Given
        CriteriaConfig anotherCriteriaConfig = CriteriaConfig.builder()
                .id(UUID.randomUUID())
                .criteriaId(UUID.randomUUID())
                .displayOrder(1)
                .build();

        CriteriaConfigEntity anotherCriteriaConfigEntity = new CriteriaConfigEntity();
        anotherCriteriaConfigEntity.setId(anotherCriteriaConfig.getId());
        anotherCriteriaConfigEntity.setCriteria(CriteriaEntity.builder()
                .id(anotherCriteriaConfig.getCriteriaId())
                .build());

        List<CriteriaConfig> configsToSave = Arrays.asList(criteriaConfig, anotherCriteriaConfig);
        List<CriteriaConfigEntity> entitiesToSave = Arrays.asList(criteriaConfigEntity, anotherCriteriaConfigEntity);

        when(criteriaConfigMapper.toCriteriaConfigEntity(criteriaConfig)).thenReturn(criteriaConfigEntity);
        when(criteriaConfigMapper.toCriteriaConfigEntity(anotherCriteriaConfig))
                .thenReturn(anotherCriteriaConfigEntity);

        // When
        criteriaConfigRepository.saveAll(configsToSave);

        // Then
        verify(criteriaConfigMapper).toCriteriaConfigEntity(criteriaConfig);
        verify(criteriaConfigMapper).toCriteriaConfigEntity(anotherCriteriaConfig);
        verify(criteriaConfigJpaRepository).saveAll(entitiesToSave);
    }
}
