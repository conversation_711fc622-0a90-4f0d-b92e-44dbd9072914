package com.sarp.rule.adapter.persistence.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.sarp.rule.adapter.persistence.model.entity.PortEntity;
import com.sarp.rule.domain.entity.Port;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class PortMapperTest {

    private PortMapper portMapper;
    private String portCode;
    private String portName;
    private String cityCode;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String portType;

    @BeforeEach
    void setUp() {
        portMapper = Mappers.getMapper(PortMapper.class);
        portCode = "IST";
        portName = "Istanbul Airport";
        cityCode = "IST";
        longitude = new BigDecimal("28.964472");
        latitude = new BigDecimal("41.009633");
        portType = "AIRPORT";
    }

    @Test
    @DisplayName("Should map PortEntity to Port domain object")
    void toDomain_ShouldMapEntityToDomain() {
        // Given
        PortEntity portEntity = PortEntity.builder()
                .portCode(portCode)
                .portName(portName)
                .cityCode(cityCode)
                .longitude(longitude)
                .latitude(latitude)
                .portType(portType)
                .hasComfort(true)
                .hasConvertibleCurrency(true)
                .roundTripMandatory(false)
                .domestic(true)
                .active(true)
                .refundable(true)
                .award(true)
                .starAward(true)
                .ajetActive(true)
                .spa(true)
                .spaArrival(true)
                .activeForWebAgent(true)
                .deleted(false)
                .build();

        // When
        Port result = portMapper.toDomain(portEntity);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPortCode()).isEqualTo(portCode);
        assertThat(result.getPortName()).isEqualTo(portName);
        assertThat(result.getCityCode()).isEqualTo(cityCode);
        assertThat(result.getLongitude()).isEqualTo(longitude);
        assertThat(result.getLatitude()).isEqualTo(latitude);
        assertThat(result.getPortType()).isEqualTo(portType);
        assertThat(result.isHasComfort()).isTrue();
        assertThat(result.isHasConvertibleCurrency()).isTrue();
        assertThat(result.isRoundTripMandatory()).isFalse();
        assertThat(result.isDomestic()).isTrue();
        assertThat(result.isActive()).isTrue();
        assertThat(result.isRefundable()).isTrue();
        assertThat(result.isAward()).isTrue();
        assertThat(result.isStarAward()).isTrue();
        assertThat(result.isAjetActive()).isTrue();
        assertThat(result.isSpa()).isTrue();
        assertThat(result.isSpaArrival()).isTrue();
        assertThat(result.isActiveForWebAgent()).isTrue();
        assertThat(result.isDeleted()).isFalse();
    }

    @Test
    @DisplayName("Should map Port domain object to PortEntity")
    void toEntity_ShouldMapDomainToEntity() {
        // Given
        Port port = Port.builder()
                .portCode(portCode)
                .portName(portName)
                .cityCode(cityCode)
                .longitude(longitude)
                .latitude(latitude)
                .portType(portType)
                .hasComfort(true)
                .hasConvertibleCurrency(true)
                .roundTripMandatory(false)
                .domestic(true)
                .active(true)
                .refundable(true)
                .award(true)
                .starAward(true)
                .ajetActive(true)
                .spa(true)
                .spaArrival(true)
                .activeForWebAgent(true)
                .deleted(false)
                .build();

        // When
        PortEntity result = portMapper.toEntity(port);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPortCode()).isEqualTo(portCode);
        assertThat(result.getPortName()).isEqualTo(portName);
        assertThat(result.getCityCode()).isEqualTo(cityCode);
        assertThat(result.getLongitude()).isEqualTo(longitude);
        assertThat(result.getLatitude()).isEqualTo(latitude);
        assertThat(result.getPortType()).isEqualTo(portType);
        assertThat(result.isHasComfort()).isTrue();
        assertThat(result.isHasConvertibleCurrency()).isTrue();
        assertThat(result.isRoundTripMandatory()).isFalse();
        assertThat(result.isDomestic()).isTrue();
        assertThat(result.isActive()).isTrue();
        assertThat(result.isRefundable()).isTrue();
        assertThat(result.isAward()).isTrue();
        assertThat(result.isStarAward()).isTrue();
        assertThat(result.isAjetActive()).isTrue();
        assertThat(result.isSpa()).isTrue();
        assertThat(result.isSpaArrival()).isTrue();
        assertThat(result.isActiveForWebAgent()).isTrue();
        assertThat(result.isDeleted()).isFalse();
    }

    @Test
    @DisplayName("Should map list of PortEntity to list of Port domain objects")
    void toDomain_ShouldMapEntityListToDomainList() {
        // Given
        PortEntity portEntity1 = PortEntity.builder()
                .portCode("IST")
                .portName("Istanbul Airport")
                .build();

        PortEntity portEntity2 = PortEntity.builder()
                .portCode("SAW")
                .portName("Sabiha Gokcen Airport")
                .build();

        List<PortEntity> portEntities = List.of(portEntity1, portEntity2);

        // When
        List<Port> results = portMapper.toDomain(portEntities);

        // Then
        assertThat(results).hasSize(2).extracting(Port::getPortCode).containsExactly("IST", "SAW");

        assertThat(results).extracting(Port::getPortName).containsExactly("Istanbul Airport", "Sabiha Gokcen Airport");
    }

    @Test
    @DisplayName("Should handle null inputs gracefully")
    void shouldHandleNullInputs() {
        // When/Then
        assertThat(portMapper.toDomain((PortEntity) null)).isNull();
        assertThat(portMapper.toDomain((List<PortEntity>) null)).isNull();
        assertThat(portMapper.toEntity(null)).isNull();
    }

    @Test
    @DisplayName("Should handle empty list")
    void shouldHandleEmptyList() {
        // When
        List<Port> results = portMapper.toDomain(List.of());

        // Then
        assertThat(results).isEmpty();
    }
}
