package com.sarp.rule.adapter.persistence.repository.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sarp.rule.adapter.persistence.mapper.ActionMapper;
import com.sarp.rule.adapter.persistence.model.entity.ActionEntity;
import com.sarp.rule.adapter.persistence.model.enums.ActionType;
import com.sarp.rule.adapter.persistence.repository.ActionJpaRepository;
import com.sarp.rule.domain.entity.Action;
import com.sarp.rule.domain.valueobject.action.Parameters;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ActionRepositoryImplTest {

    @Mock
    private ActionJpaRepository actionJpaRepository;

    @Mock
    private ActionMapper actionMapper;

    @InjectMocks
    private ActionRepositoryImpl actionRepository;

    private UUID actionId1;
    private UUID actionId2;
    private Action action1;
    private Action action2;
    private ActionEntity actionEntity1;
    private ActionEntity actionEntity2;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        actionId1 = UUID.randomUUID();
        actionId2 = UUID.randomUUID();
        objectMapper = new ObjectMapper();

        // Create test actions
        action1 = Action.builder()
                .id(actionId1)
                .actionType(com.sarp.rule.domain.valueobject.action.ActionType.PERCENTAGE_DISCOUNT)
                .parameters(Parameters.of(Map.of("discount", 10)))
                .ruleId(UUID.randomUUID())
                .build();

        action2 = Action.builder()
                .id(actionId2)
                .actionType(com.sarp.rule.domain.valueobject.action.ActionType.FIXED_DISCOUNT)
                .parameters(Parameters.of(Map.of("amount", 100)))
                .ruleId(UUID.randomUUID())
                .build();

        // Create test entities
        actionEntity1 = ActionEntity.builder()
                .id(actionId1)
                .actionType(ActionType.PERCENTAGE_DISCOUNT)
                .parameters(objectMapper.valueToTree(Map.of("discount", 10)))
                .ruleId(action1.getRuleId())
                .build();

        actionEntity2 = ActionEntity.builder()
                .id(actionId2)
                .actionType(ActionType.FIXED_DISCOUNT)
                .parameters(objectMapper.valueToTree(Map.of("amount", 100)))
                .ruleId(action2.getRuleId())
                .build();
    }

    @Test
    @DisplayName("Should find actions by IDs")
    void shouldFindActionsByIds() {
        // Given
        List<UUID> actionIds = Arrays.asList(actionId1, actionId2);
        List<ActionEntity> actionEntities = Arrays.asList(actionEntity1, actionEntity2);
        List<Action> expectedActions = Arrays.asList(action1, action2);

        when(actionJpaRepository.findAllByIdIn(actionIds)).thenReturn(actionEntities);
        when(actionMapper.toDomain(actionEntity1)).thenReturn(action1);
        when(actionMapper.toDomain(actionEntity2)).thenReturn(action2);

        // When
        List<Action> result = actionRepository.findByIds(actionIds);

        // Then
        assertThat(result).isNotNull().hasSize(2).containsExactlyElementsOf(expectedActions);

        verify(actionJpaRepository).findAllByIdIn(actionIds);
        verify(actionMapper).toDomain(actionEntity1);
        verify(actionMapper).toDomain(actionEntity2);
    }

    @Test
    @DisplayName("Should return empty list when no actions found")
    void shouldReturnEmptyListWhenNoActionsFound() {
        // Given
        List<UUID> actionIds = Arrays.asList(actionId1, actionId2);
        when(actionJpaRepository.findAllByIdIn(actionIds)).thenReturn(List.of());

        // When
        List<Action> result = actionRepository.findByIds(actionIds);

        // Then
        assertThat(result).isNotNull().isEmpty();

        verify(actionJpaRepository).findAllByIdIn(actionIds);
    }
}
