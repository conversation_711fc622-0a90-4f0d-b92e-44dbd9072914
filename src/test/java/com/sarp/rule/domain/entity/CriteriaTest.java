package com.sarp.rule.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.sarp.rule.adapter.persistence.model.enums.ComparisonOperator;
import com.sarp.rule.domain.exception.CriteriaDomainException;
import com.sarp.rule.domain.valueobject.criteria.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaTest {

    @Mock
    private UUID mockCriteriaId;

    @Test
    @DisplayName("Should build a valid Criteria with all required fields")
    void shouldBuildValidCriteria() {
        Criteria criteria = new Criteria.Builder()
                .id(mockCriteriaId)
                .name("Test Criteria")
                .description("Some description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("someField")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .criteriaGroupId(UUID.randomUUID())
                .build();

        assertThat(criteria).isNotNull();
        assertThat(criteria.getName()).isEqualTo("Test Criteria");
    }

    @Test
    @DisplayName("Should throw exception when name is null")
    void shouldThrowWhenNameIsNull() {
        try {
            new Criteria.Builder()
                    .description("desc")
                    .type(CriteriaType.USER_DEFINED)
                    .requestType(RequestType.OFFER_REQUEST)
                    .mappingField("field")
                    .fieldType(FieldType.INTEGER)
                    .minValue(BigDecimal.ONE)
                    .maxValue(BigDecimal.TEN)
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_name_cannot_be_empty");
        }
    }

    @Test
    @DisplayName("Should throw exception when fieldType is LIST and allowedValues is missing")
    void shouldThrowWhenListWithoutAllowedValues() {
        try {
            new Criteria.Builder()
                    .name("List Field")
                    .description("desc")
                    .type(CriteriaType.SYSTEM_DEFINED)
                    .systemDefinedCriteriaType("PASSENGER_AGE")
                    .requestType(RequestType.OFFER_REQUEST)
                    .fieldType(FieldType.LIST)
                    .selectionType(SelectionType.MULTI_SELECT)
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_values_cannot_be_empty");
        }
    }

    @Test
    @DisplayName("Should throw exception when fieldType is LIST and selectionType is missing")
    void shouldThrowWhenListWithoutSelectionType() {
        try {
            new Criteria.Builder()
                    .name("List Field")
                    .description("desc")
                    .type(CriteriaType.SYSTEM_DEFINED)
                    .systemDefinedCriteriaType("PASSENGER_AGE")
                    .requestType(RequestType.OFFER_REQUEST)
                    .fieldType(FieldType.LIST)
                    .allowedValues(List.of("A", "B", "C"))
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_selection_type_cannot_be_null");
        }
    }

    @Test
    @DisplayName("Should throw exception when fieldType is INTEGER and maxValue is null")
    void shouldThrowWhenIntegerFieldWithoutMaxValue() {
        try {
            new Criteria.Builder()
                    .name("Integer Field")
                    .description("desc")
                    .type(CriteriaType.USER_DEFINED)
                    .requestType(RequestType.OFFER_REQUEST)
                    .mappingField("intField")
                    .fieldType(FieldType.INTEGER)
                    .minValue(BigDecimal.ZERO)
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_max_value_cannot_be_null");
        }
    }

    @Test
    @DisplayName("Should throw exception when allowedOperators is not valid for the field type")
    void shouldThrowWhenAllowedOperatorsIsNotValid() {
        try {
            new Criteria.Builder()
                    .name("List Field")
                    .description("desc")
                    .type(CriteriaType.SYSTEM_DEFINED)
                    .systemDefinedCriteriaType("PASSENGER_AGE")
                    .requestType(RequestType.OFFER_REQUEST)
                    .mappingField("listField")
                    .fieldType(FieldType.TEXT)
                    .allowedValues(List.of("A", "B", "C"))
                    .allowedOperators(List.of(ComparisonOperator.GREATER_THAN))
                    .selectionType(SelectionType.MULTI_SELECT)
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_allowed_operators_is_not_valid");
        }
    }

    @Test
    @DisplayName("Should build a valid Criteria with all required fields")
    void shouldBuildAValidCriteriaWithDefaultOperators() {
        Criteria criteria = new Criteria.Builder()
                .id(mockCriteriaId)
                .name("Test Criteria")
                .description("Some description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("someField")
                .fieldType(FieldType.INTEGER)
                .allowedOperators(null)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .criteriaGroupId(UUID.randomUUID())
                .build();

        assertThat(criteria).isNotNull();
        assertThat(criteria.getAllowedOperators())
                .isEqualTo(List.of(
                        ComparisonOperator.EQUALS,
                        ComparisonOperator.NOT_EQUALS,
                        ComparisonOperator.GREATER_THAN,
                        ComparisonOperator.GREATER_OR_EQUAL,
                        ComparisonOperator.LESS_THAN,
                        ComparisonOperator.LESS_OR_EQUAL,
                        ComparisonOperator.BETWEEN,
                        ComparisonOperator.NOT_BETWEEN,
                        ComparisonOperator.CONTAINS_ANY,
                        ComparisonOperator.CONTAINS_NONE));
    }

    @Test
    @DisplayName("Should initialize missing fields with default values")
    void shouldInitializeWithDefaults() {
        Criteria criteria = new Criteria.Builder()
                .name("Default Test")
                .description("desc")
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .build();

        assertThat(criteria.getId()).isNotNull();
        assertThat(criteria.getType()).isEqualTo(CriteriaType.USER_DEFINED);
        assertThat(criteria.getRequestType()).isEqualTo(RequestType.OFFER_REQUEST);
    }

    @Test
    @DisplayName("Should not override manually set values during initialize")
    void shouldPreserveManuallySetValuesOnInitialize() {
        Criteria criteria = new Criteria.Builder()
                .name("Preserve Values")
                .description("desc")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .type(CriteriaType.SYSTEM_DEFINED)
                .systemDefinedCriteriaType("PASSENGER_AGE")
                .requestType(RequestType.OFFER_REQUEST)
                .build();

        assertThat(criteria.getType()).isEqualTo(CriteriaType.SYSTEM_DEFINED);
        assertThat(criteria.getRequestType()).isEqualTo(RequestType.OFFER_REQUEST);
    }

    @Test
    @DisplayName("Should return true when IDs are the same")
    void shouldReturnTrueForEqualIds() {
        UUID sameId = UUID.randomUUID();

        Criteria a = new Criteria.Builder()
                .id(sameId)
                .name("b")
                .description("c")
                .mappingField("x")
                .fieldType(FieldType.TEXT)
                .build();
        Criteria b = new Criteria.Builder()
                .id(sameId)
                .name("b")
                .description("c")
                .mappingField("x")
                .fieldType(FieldType.TEXT)
                .build();

        assertThat(a).isEqualTo(b).hasSameHashCodeAs(b);
    }

    @Test
    @DisplayName("Should return false when IDs are different")
    void shouldReturnFalseForDifferentIds() {
        Criteria a = new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("b")
                .description("b")
                .mappingField("x")
                .fieldType(FieldType.TEXT)
                .build();
        Criteria b = new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("c")
                .description("c")
                .mappingField("x")
                .fieldType(FieldType.TEXT)
                .build();

        assertThat(a).isNotEqualTo(b);
    }

    @Test
    @DisplayName("Should throw when integer fieldType but min/max are missing")
    void shouldThrowWhenIntegerFieldWithoutMinMax() {
        try {
            new Criteria.Builder()
                    .name("Integer Test")
                    .description("desc")
                    .mappingField("intField")
                    .type(CriteriaType.USER_DEFINED)
                    .requestType(RequestType.OFFER_REQUEST)
                    .fieldType(FieldType.INTEGER)
                    .build();
        } catch (CriteriaDomainException e) {
            assertThat(e.getMessage()).isEqualTo("criteria_min_value_cannot_be_null");
        }
    }

    @Test
    @DisplayName("Should include all fields in toString")
    void shouldGenerateToString() {
        Criteria criteria = new Criteria.Builder()
                .id(mockCriteriaId)
                .name("Test")
                .description("Test description")
                .type(CriteriaType.USER_DEFINED)
                .requestType(RequestType.OFFER_REQUEST)
                .mappingField("field")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ONE)
                .maxValue(BigDecimal.TEN)
                .build();

        String str = criteria.toString();
        assertThat(str).contains("Test description").contains("field").contains("USER_DEFINED");
    }

    @Test
    @DisplayName("Should update criteria fields correctly")
    void shouldUpdateCriteriaFieldsCorrectly() {
        // Given
        Criteria criteria = new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("Original Name")
                .description("Original Description")
                .mappingField("originalField")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .build();

        String newName = "Updated Name";
        String newDescription = "Updated Description";

        // When
        criteria.update(newName, newDescription);

        // Then
        assertThat(criteria.getName()).isEqualTo(newName);
        assertThat(criteria.getDescription()).isEqualTo(newDescription);
    }

    @Test
    @DisplayName("Should support partial updates of name or description")
    void shouldSupportPartialUpdates() {
        // Given
        Criteria criteria = new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("Original Name")
                .description("Original Description")
                .mappingField("someField")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .build();

        // When - Update only name
        criteria.update("New Name", null);

        // Then
        assertThat(criteria.getName()).isEqualTo("New Name");
        assertThat(criteria.getDescription()).isEqualTo("Original Description");

        // When - Update only description
        criteria.update(null, "New Description");

        // Then
        assertThat(criteria.getName()).isEqualTo("New Name");
        assertThat(criteria.getDescription()).isEqualTo("New Description");

        // When - Update both
        criteria.update("Final Name", "Final Description");

        // Then
        assertThat(criteria.getName()).isEqualTo("Final Name");
        assertThat(criteria.getDescription()).isEqualTo("Final Description");

        // Still validates that values can't be empty
        assertThatThrownBy(() -> criteria.update("", "Valid"))
                .isInstanceOf(CriteriaDomainException.class)
                .hasMessage("criteria_name_cannot_be_empty");

        assertThatThrownBy(() -> criteria.update("Valid", ""))
                .isInstanceOf(CriteriaDomainException.class)
                .hasMessage("criteria_description_cannot_be_empty");
    }

    @Test
    @DisplayName("Should update criteria fields correctly")
    void shouldUpdateCriteriaFieldsCorrectlyWithoutSelectionType() {
        // Given
        Criteria criteria = new Criteria.Builder()
                .id(UUID.randomUUID())
                .name("Original Name")
                .description("Original Description")
                .mappingField("originalField")
                .fieldType(FieldType.INTEGER)
                .minValue(BigDecimal.ZERO)
                .maxValue(BigDecimal.TEN)
                .build();

        String newName = "Updated Name";
        String newDescription = "Updated Description";

        // When
        criteria.update(newName, newDescription);

        // Then
        assertThat(criteria.getName()).isEqualTo(newName);
        assertThat(criteria.getDescription()).isEqualTo(newDescription);

        assertThat(criteria.getMappingField()).isEqualTo("originalField");
        assertThat(criteria.getSelectionType()).isNull();
    }
}
