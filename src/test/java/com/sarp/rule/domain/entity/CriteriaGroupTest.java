package com.sarp.rule.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.sarp.rule.domain.exception.CriteriaGroupDomainException;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CriteriaGroupTest {

    @Test
    @DisplayName("Should build a valid CriteriaGroup with all required fields")
    void shouldBuildValidCriteriaGroup() {
        // Given
        String name = "Test Criteria Group";
        Integer displayOrder = 1;

        // When
        CriteriaGroup criteriaGroup = new CriteriaGroup.Builder()
                .name(name)
                .displayOrder(displayOrder)
                .build();

        // Then
        assertThat(criteriaGroup).isNotNull();
        assertThat(criteriaGroup.getName()).isEqualTo(name);
        assertThat(criteriaGroup.getDisplayOrder()).isEqualTo(displayOrder);
        assertThat(criteriaGroup.getId()).isNotNull();
    }

    @Test
    @DisplayName("Should throw exception when name is null")
    void shouldThrowExceptionWhenNameIsNull() {
        // When & Then
        assertThatThrownBy(() -> buildCriteriaGroupWithName(null))
                .isInstanceOf(CriteriaGroupDomainException.class)
                .hasMessage("criteria_group_name_cannot_be_null_or_empty");
    }

    @Test
    @DisplayName("Should throw exception when name is empty")
    void shouldThrowWhenNameIsEmpty() {
        // When & Then
        assertThatThrownBy(() -> buildCriteriaGroupWithName(""))
                .isInstanceOf(CriteriaGroupDomainException.class)
                .hasMessage("criteria_group_name_cannot_be_null_or_empty");
    }

    @Test
    @DisplayName("Should throw exception when name is blank")
    void shouldThrowWhenNameIsBlank() {
        // When & Then
        assertThatThrownBy(() -> buildCriteriaGroupWithName("   "))
                .isInstanceOf(CriteriaGroupDomainException.class)
                .hasMessage("criteria_group_name_cannot_be_null_or_empty");
    }

    @Test
    @DisplayName("Should trim name when set")
    void shouldTrimNameWhenSet() {
        // Given
        String nameWithSpaces = "  Test Group  ";

        // When
        CriteriaGroup criteriaGroup =
                new CriteriaGroup.Builder().name(nameWithSpaces).displayOrder(1).build();

        // Then
        assertThat(criteriaGroup.getName()).isEqualTo("Test Group");
    }

    @Test
    @DisplayName("Should have equals and hashCode implemented based on ID")
    void shouldImplementEqualsAndHashCodeBasedOnId() {
        // Given
        UUID sharedUuid = UUID.randomUUID();

        // When
        CriteriaGroup groupA = new CriteriaGroup.Builder()
                .id(sharedUuid)
                .name("Group A")
                .displayOrder(1)
                .build();

        CriteriaGroup groupB = new CriteriaGroup.Builder()
                .id(sharedUuid)
                .name("Group B")
                .displayOrder(2)
                .build();

        CriteriaGroup groupC =
                new CriteriaGroup.Builder().name("Group C").displayOrder(3).build();

        // Then
        assertThat(groupA).isEqualTo(groupB).hasSameHashCodeAs(groupB).isNotEqualTo(groupC);
        assertThat(groupB).isNotEqualTo(groupC);
    }

    @Test
    @DisplayName("Should handle null criteria list by creating empty list")
    void shouldHandleNullCriteriaList() {
        // Given
        CriteriaGroup criteriaGroup = new CriteriaGroup.Builder()
                .name("Test Group")
                .criteriaList(null)
                .displayOrder(1)
                .build();

        // When & Then
        assertThat(criteriaGroup.getCriteriaList()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should set display number correctly")
    void shouldSetDisplayNumberCorrectly() {
        // Given
        Integer displayOrder = 5;

        // When
        CriteriaGroup criteriaGroup = new CriteriaGroup.Builder()
                .name("Test Group")
                .displayOrder(displayOrder)
                .build();

        // Then
        assertThat(criteriaGroup.getDisplayOrder()).isEqualTo(displayOrder);
    }

    private static CriteriaGroup buildCriteriaGroupWithName(String name) {
        return new CriteriaGroup.Builder().name(name).build();
    }
}
