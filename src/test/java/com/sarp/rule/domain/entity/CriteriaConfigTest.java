// package com.sarp.rule.domain.entity;
//
// import static org.assertj.core.api.Assertions.assertThat;
// import static org.assertj.core.api.Assertions.assertThatThrownBy;
//
// import com.sarp.rule.domain.exception.CriteriaConfigDomainException;
// import com.sarp.rule.domain.valueobject.rule.RuleType;
// import java.util.List;
// import java.util.UUID;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
//
// class CriteriaConfigTest {
//
//    @Test
//    @DisplayName("Should build a valid CriteriaConfig with all required fields")
//    void shouldBuildValidCriteriaConfig() {
//        // Given
//        CriteriaConfig criteriaConfig =
//                CriteriaConfig.builder()
//                        .id(UUID.randomUUID())
//                        .displayOrder(1)
//                        .allowedRules(List.of(RuleType.BUNDLE))
//                        .mandatoryRules(List.of(RuleType.ALA_CARTE))
//                        .criteria(UUID.randomUUID())
//                        .build();
//
//        // When/Then
//        assertThat(criteriaConfig).isNotNull();
//        assertThat(criteriaConfig.getDisplayOrder()).isEqualTo(1);
//        assertThat(criteriaConfig.getAllowedRules()).containsExactly(RuleType.BUNDLE);
//        assertThat(criteriaConfig.getMandatoryRules()).containsExactly(RuleType.ALA_CARTE);
//        assertThat(criteriaConfig.getCriteria()).isNotNull();
//    }
//
//    @Test
//    @DisplayName("Should throw exception when id is null during validation")
//    void shouldThrowWhenIdIsNull() {
//        CriteriaConfig config =
//                new CriteriaConfig(null, 1, List.of(), List.of(), UUID.randomUUID());
//
//        assertThatThrownBy(config::validate)
//                .isInstanceOf(CriteriaConfigDomainException.class)
//                .hasMessage("CriteriaConfigId cannot be null");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when displayOrder is null during validation")
//    void shouldThrowWhenDisplayOrderIsNull() {
//        CriteriaConfig config =
//                new CriteriaConfig(
//                        UUID.randomUUID(), null, List.of(), List.of(), UUID.randomUUID());
//
//        assertThatThrownBy(config::validate)
//                .isInstanceOf(CriteriaConfigDomainException.class)
//                .hasMessage("DisplayOrder cannot be null");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when criteria is null during validation")
//    void shouldThrowWhenCriteriaIsNull() {
//        CriteriaConfig config =
//                new CriteriaConfig(UUID.randomUUID(), 1, List.of(), List.of(), null);
//
//        assertThatThrownBy(config::validate)
//                .isInstanceOf(CriteriaConfigDomainException.class)
//                .hasMessage("Criteria cannot be null");
//    }
//
//    @Test
//    @DisplayName("Should initialize missing fields with defaults")
//    void shouldInitializeWithDefaults() {
//        // Given
//        CriteriaConfig config = new CriteriaConfig();
//
//        // When
//        config.initialize();
//
//        // Then
//        assertThat(config.getId()).isNotNull();
//        assertThat(config.getAllowedRules()).isEmpty();
//        assertThat(config.getMandatoryRules()).isEmpty();
//    }
//
//    @Test
//    @DisplayName("Should preserve manually set fields on initialize")
//    void shouldPreserveManuallySetFieldsOnInitialize() {
//        // Given
//        UUID id = UUID.randomUUID();
//        List<RuleType> allowed = List.of(RuleType.BUNDLE);
//        List<RuleType> mandatory = List.of(RuleType.ALA_CARTE);
//        UUID criteria = UUID.randomUUID();
//
//        CriteriaConfig config = new CriteriaConfig(id, 5, allowed, mandatory, criteria);
//
//        // When
//        config.initialize();
//
//        // Then
//        assertThat(config.getId()).isEqualTo(id);
//        assertThat(config.getAllowedRules()).isEqualTo(allowed);
//        assertThat(config.getMandatoryRules()).isEqualTo(mandatory);
//        assertThat(config.getCriteria()).isEqualTo(criteria);
//    }
//
//    @Test
//    @DisplayName("Should return true when IDs are equal")
//    void shouldReturnTrueWhenIdsAreEqual() {
//        UUID id = UUID.randomUUID();
//
//        CriteriaConfig config1 =
//
// CriteriaConfig.builder().id(id).displayOrder(1).criteria(UUID.randomUUID()).build();
//
//        CriteriaConfig config2 =
//
// CriteriaConfig.builder().id(id).displayOrder(2).criteria(UUID.randomUUID()).build();
//
//        assertThat(config1).isEqualTo(config2);
//        assertThat(config1.hashCode()).isEqualTo(config2.hashCode());
//    }
//
//    @Test
//    @DisplayName("Should return false when IDs are different")
//    void shouldReturnFalseWhenIdsAreDifferent() {
//        CriteriaConfig config1 =
//                CriteriaConfig.builder()
//                        .id(UUID.randomUUID())
//                        .displayOrder(1)
//                        .criteria(UUID.randomUUID())
//                        .build();
//
//        CriteriaConfig config2 =
//                CriteriaConfig.builder()
//                        .id(UUID.randomUUID())
//                        .displayOrder(1)
//                        .criteria(UUID.randomUUID())
//                        .build();
//
//        assertThat(config1).isNotEqualTo(config2);
//    }
//
//    @Test
//    @DisplayName("Should generate a non-empty toString")
//    void shouldGenerateToString() {
//        CriteriaConfig config =
//                CriteriaConfig.builder()
//                        .id(UUID.randomUUID())
//                        .displayOrder(1)
//                        .criteria(UUID.randomUUID())
//                        .build();
//
//        String str = config.toString();
//
//        assertThat(str).contains("displayOrder=1").contains("criteria=");
//    }
//
//    @Test
//    @DisplayName("Should correctly update CriteriaConfig fields")
//    void shouldCorrectlyUpdateCriteriaConfigFields() {
//        // Given
//        CriteriaConfig original =
//                CriteriaConfig.builder()
//                        .id(UUID.randomUUID())
//                        .criteria(UUID.randomUUID())
//                        .displayOrder(1)
//                        .allowedRules(List.of(RuleType.BUNDLE))
//                        .mandatoryRules(List.of(RuleType.ALA_CARTE))
//                        .build();
//
//        List<RuleType> newAllowed = List.of(RuleType.ALA_CARTE);
//        List<RuleType> newMandatory = List.of(RuleType.BUNDLE);
//
//        // When
//        CriteriaConfig updated = original.update(10, newAllowed, newMandatory);
//
//        // Then
//        assertThat(updated.getId()).isEqualTo(original.getId()); // ID stays same
//        assertThat(updated.getCriteria()).isEqualTo(original.getCriteria()); // criteria stays
// same
//        assertThat(updated.getDisplayOrder()).isEqualTo(10);
//        assertThat(updated.getAllowedRules()).containsExactly(RuleType.ALA_CARTE);
//        assertThat(updated.getMandatoryRules()).containsExactly(RuleType.BUNDLE);
//    }
// }
