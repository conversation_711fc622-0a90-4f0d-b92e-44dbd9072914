// package com.sarp.rule.domain.entity;
//
// import static org.assertj.core.api.Assertions.assertThat;
// import static org.assertj.core.api.Assertions.assertThatThrownBy;
// import static org.mockito.Mockito.when;
//
// import com.sarp.rule.domain.exception.BundleDomainException;
// import com.sarp.rule.domain.valueobject.bundle.BundleId;
// import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
// import com.sarp.rule.domain.valueobject.bundle.BundleType;
// import java.util.UUID;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// public class BundleTest {
//
//    @Mock private BundleId mockBundleId;
//
//    @Test
//    @DisplayName("Should build a valid bundle with all required fields")
//    void shouldBuildValidBundle() {
//        // When
//        Bundle bundle =
//                new Bundle.Builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE)
//                        .version(1)
//                        .build();
//
//        // Then
//        assertThat(bundle).isNotNull();
//        assertThat(bundle.getId()).isEqualTo(mockBundleId);
//        assertThat(bundle.getName()).isEqualTo("Test Bundle");
//        assertThat(bundle.getDescription()).isEqualTo("This is a test bundle");
//        assertThat(bundle.getType()).isEqualTo(BundleType.FLIGHT_INCLUSIVE);
//        assertThat(bundle.getStatus()).isEqualTo(BundleStatus.ACTIVE);
//        assertThat(bundle.getVersion()).isEqualTo(1);
//    }
//
//    @Test
//    @DisplayName("Should throw exception when name is null")
//    void shouldThrowExceptionWhenNameIsNull() {
//        // Given
//        Bundle.Builder builder =
//                new Bundle.Builder()
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE);
//
//        // When/Then
//        assertThatThrownBy(builder::build)
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessage("Bundle name cannot be null or empty");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when name is empty")
//    void shouldThrowExceptionWhenNameIsEmpty() {
//        // Given
//        Bundle.Builder builder =
//                new Bundle.Builder()
//                        .name("")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE);
//
//        // When/Then
//        assertThatThrownBy(builder::build)
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessage("Bundle name cannot be null or empty");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when description is null")
//    void shouldThrowExceptionWhenDescriptionIsNull() {
//        // Given
//        Bundle.Builder builder =
//                new Bundle.Builder().name("Test Bundle").type(BundleType.FLIGHT_INCLUSIVE);
//
//        // When/Then
//        assertThatThrownBy(builder::build)
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessage("Bundle description cannot be null or empty");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when description is empty")
//    void shouldThrowExceptionWhenDescriptionIsEmpty() {
//        // Given
//        Bundle.Builder builder =
//                new Bundle.Builder()
//                        .name("Test Bundle")
//                        .description("")
//                        .type(BundleType.FLIGHT_INCLUSIVE);
//
//        // When/Then
//        assertThatThrownBy(builder::build)
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessage("Bundle description cannot be null or empty");
//    }
//
//    @Test
//    @DisplayName("Should throw exception when type is null")
//    void shouldThrowExceptionWhenTypeIsNull() {
//        // Given
//        Bundle.Builder builder =
//                new Bundle.Builder().name("Test Bundle").description("This is a test bundle");
//
//        // When/Then
//        assertThatThrownBy(builder::build)
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessage("Bundle type cannot be null");
//    }
//
//    @Test
//    @DisplayName("Should return true for equals when IDs match")
//    void shouldReturnTrueForEqualsWhenIdsMatch() {
//        // Given
//        Bundle bundle1 =
//                new Bundle.Builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle 1")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .build();
//
//        Bundle bundle2 =
//                new Bundle.Builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle 2") // Different name
//                        .description("Different description") // Different description
//                        .type(BundleType.STANDALONE) // Different type
//                        .build();
//
//        // When/Then
//        assertThat(bundle1).isEqualTo(bundle2);
//        assertThat(bundle1.hashCode()).isEqualTo(bundle2.hashCode());
//    }
//
//    @Test
//    @DisplayName("Should return false for equals when IDs don't match")
//    void shouldReturnFalseForEqualsWhenIdsDontMatch() {
//        // Given
//        // First bundle with auto-generated ID
//        Bundle bundle1 =
//                new Bundle.Builder()
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .build();
//
//        // Second bundle with auto-generated ID
//        Bundle bundle2 =
//                new Bundle.Builder()
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .build();
//
//        // When/Then
//        assertThat(bundle1).isEqualTo(bundle2);
//    }
//
//    @Test
//    @DisplayName("Should generate valid toString representation")
//    void shouldGenerateValidToStringRepresentation() {
//        // Given
//        when(mockBundleId.toString()).thenReturn("mocked-bundle-id");
//
//        Bundle bundle =
//                new Bundle.Builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE) // Add status explicitly
//                        .version(5)
//                        .build();
//
//        // When
//        String toString = bundle.toString();
//
//        // Then
//        assertThat(toString)
//                .contains("Bundle{")
//                .contains("id=" + mockBundleId)
//                .contains("name='Test Bundle'")
//                .contains("description='This is a test bundle'")
//                .contains("type=" + BundleType.FLIGHT_INCLUSIVE)
//                .contains("status=" + BundleStatus.ACTIVE)
//                .contains("version=5");
//    }
//
//    @Test
//    @DisplayName("Should validate successfully when all required fields are present")
//    void shouldValidateSuccessfullyWhenAllRequiredFieldsArePresent() {
//        // Given
//        Bundle bundle =
//                new Bundle.Builder()
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .build();
//
//        // When/Then - No exception should be thrown
//        bundle.validate();
//    }
//
//    @Test
//    @DisplayName("Should initialize ID when it's null")
//    void shouldInitializeIdWhenItsNull() {
//        // Given
//        Bundle bundle =
//                Bundle.builder()
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .id(null)
//                        .build();
//
//        // When
//        bundle.initialize();
//
//        // Then
//        assertThat(bundle.getId()).isNotNull();
//    }
//
//    @Test
//    @DisplayName("Should not initialize ID when it's already set")
//    void shouldNotInitializeIdWhenItsAlreadySet() {
//        // Given
//        BundleId bundleId = BundleId.of(UUID.randomUUID());
//        Bundle bundle =
//                Bundle.builder()
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .id(bundleId)
//                        .build();
//
//        // When
//        bundle.initialize();
//
//        // Then
//        assertThat(bundle.getId()).isEqualTo(bundleId);
//    }
//
//    @Test
//    @DisplayName("Should initialize status when it's null")
//    void shouldInitializeStatusWhenItsNull() {
//        // Given
//        Bundle bundle =
//                Bundle.builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(null)
//                        .build();
//
//        // When
//        bundle.initialize();
//
//        // Then
//        assertThat(bundle.getStatus()).isEqualTo(BundleStatus.ACTIVE);
//    }
//
//    @Test
//    @DisplayName("Should not initialize status when it's already set")
//    void shouldNotInitializeStatusWhenItsAlreadySet() {
//        // Given
//        Bundle bundle =
//                Bundle.builder()
//                        .id(mockBundleId)
//                        .name("Test Bundle")
//                        .description("This is a test bundle")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.PASSIVE)
//                        .build();
//
//        // When
//        bundle.initialize();
//
//        // Then
//        assertThat(bundle.getStatus()).isEqualTo(BundleStatus.PASSIVE);
//    }
//
//    @Test
//    @DisplayName("Should update bundle properties correctly")
//    void shouldUpdateBundlePropertiesCorrectly() {
//        // Given
//        Bundle originalBundle =
//                new Bundle.Builder()
//                        .id(mockBundleId)
//                        .name("Original Name")
//                        .description("Original Description")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        Bundle updatedBundle =
//                new Bundle.Builder()
//                        .name("Updated Name")
//                        .description("Updated Description")
//                        .type(BundleType.STANDALONE)
//                        .status(BundleStatus.PASSIVE)
//                        .build();
//
//        // When
//        originalBundle.update(updatedBundle);
//
//        // Then
//        assertThat(originalBundle.getName()).isEqualTo("Updated Name");
//        assertThat(originalBundle.getDescription()).isEqualTo("Updated Description");
//        assertThat(originalBundle.getType()).isEqualTo(BundleType.STANDALONE);
//        assertThat(originalBundle.getStatus()).isEqualTo(BundleStatus.PASSIVE);
//        // ID should remain unchanged
//        assertThat(originalBundle.getId()).isEqualTo(mockBundleId);
//    }
//
//    @Test
//    @DisplayName("Should set and get properties correctly")
//    void shouldSetAndGetPropertiesCorrectly() {
//        // Given
//        // When
//        BundleId bundleId = BundleId.of(UUID.randomUUID());
//        Bundle bundle =
//                Bundle.builder()
//                        .id(bundleId)
//                        .name("Test Name")
//                        .description("Test Description")
//                        .type(BundleType.STANDALONE)
//                        .status(BundleStatus.PASSIVE)
//                        .version(10)
//                        .build();
//
//        // Then
//        assertThat(bundle.getId()).isEqualTo(bundleId);
//        assertThat(bundle.getName()).isEqualTo("Test Name");
//        assertThat(bundle.getDescription()).isEqualTo("Test Description");
//        assertThat(bundle.getType()).isEqualTo(BundleType.STANDALONE);
//        assertThat(bundle.getStatus()).isEqualTo(BundleStatus.PASSIVE);
//        assertThat(bundle.getVersion()).isEqualTo(10);
//    }
// }
