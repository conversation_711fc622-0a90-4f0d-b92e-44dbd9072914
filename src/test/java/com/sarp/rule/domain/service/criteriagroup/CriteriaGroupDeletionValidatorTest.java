package com.sarp.rule.domain.service.criteriagroup;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.sarp.rule.domain.exception.CriteriaGroupDomainException;
import com.sarp.rule.domain.validator.CriteriaGroupDeletionValidator;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class CriteriaGroupDeletionValidatorTest {

    private final CriteriaGroupDeletionValidator validator = new CriteriaGroupDeletionValidator();

    @Test
    @DisplayName("Should allow deletion when associatedCriteriaCount is 0")
    void shouldAllowDeletionWhenAssociatedCriteriaCountIsZero() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        long associatedCriteriaCount = 0;

        // When & Then
        assertDoesNotThrow(() -> validator.ensureDeletable(criteriaGroupId, associatedCriteriaCount));
    }

    @Test
    @DisplayName("Should throw exception when associatedCriteriaCount is greater than 0")
    void shouldThrowExceptionWhenAssociatedCriteriaCountIsGreaterThanZero() {
        // Given
        UUID criteriaGroupId = UUID.randomUUID();
        long associatedCriteriaCount = 5;

        // When & Then
        assertThatThrownBy(() -> validator.ensureDeletable(criteriaGroupId, associatedCriteriaCount))
                .isInstanceOf(CriteriaGroupDomainException.class)
                .hasMessageContaining("criteria_group_has_associated_criteria");
    }
}
