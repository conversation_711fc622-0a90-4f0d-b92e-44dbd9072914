package com.sarp.rule.domain.service.criteriagroup;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class CriteriaGroupDisplayOrderCalculatorTest {

    private final CriteriaGroupDisplayOrderCalculator calculator = new CriteriaGroupDisplayOrderCalculator();

    @Test
    @DisplayName("Should return 1 when currentMaxDisplayOrder is null")
    void shouldReturnOneWhenCurrentMaxDisplayOrderIsNull() {
        // Given
        Integer currentMaxDisplayOrder = null;

        // When
        Integer nextDisplayOrder = calculator.calculateNextDisplayOrder(currentMaxDisplayOrder);

        // Then
        assertThat(nextDisplayOrder).isEqualTo(1);
    }

    @Test
    @DisplayName("Should increment currentMaxDisplayOrder by 1")
    void shouldIncrementCurrentMaxDisplayOrderByOne() {
        // Given
        Integer currentMaxDisplayOrder = 5;

        // When
        Integer nextDisplayOrder = calculator.calculateNextDisplayOrder(currentMaxDisplayOrder);

        // Then
        assertThat(nextDisplayOrder).isEqualTo(6);
    }
}
