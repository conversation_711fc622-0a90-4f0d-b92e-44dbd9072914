// package com.sarp.rule.domain.service.impl;
//
// import static org.assertj.core.api.Assertions.assertThat;
// import static org.assertj.core.api.Assertions.assertThatThrownBy;
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.Mockito.*;
//
// import com.sarp.rule.domain.entity.Bundle;
// import com.sarp.rule.domain.event.bundle.BundleCreatedEvent;
// import com.sarp.rule.domain.event.bundle.BundleDeletedEvent;
// import com.sarp.rule.domain.event.bundle.BundleUpdatedEvent;
// import com.sarp.rule.domain.exception.BundleDomainException;
// import com.sarp.rule.domain.repository.BundleRepository;
// import com.sarp.rule.domain.valueobject.bundle.BundleId;
// import com.sarp.rule.domain.valueobject.bundle.BundleStatus;
// import com.sarp.rule.domain.valueobject.bundle.BundleType;
// import java.util.*;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// class BundleDomainServiceImplTest {
//
//    @Mock private BundleRepository bundleRepository;
//
//    @InjectMocks private BundleDomainServiceImpl bundleDomainService;
//
//    private Bundle validBundle;
//    private UUID bundleId;
//    private BundleId bundleIdObj;
//
//    @BeforeEach
//    void setUp() {
//        bundleId = UUID.randomUUID();
//        bundleIdObj = new BundleId(bundleId);
//
//        // Create a valid bundle for testing
//        validBundle =
//                new Bundle.Builder()
//                        .id(bundleIdObj)
//                        .name("Test Bundle")
//                        .description("Test Bundle Description")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//    }
//
//    @Test
//    @DisplayName(
//            "Given a valid bundle, when saveBundle is called, then BundleCreatedEvent should be
// returned")
//    void givenValidBundle_whenSaveBundleCalled_thenBundleCreatedEventReturned() {
//        // Given
//        when(bundleRepository.save(validBundle)).thenReturn(validBundle);
//
//        // When
//        BundleCreatedEvent event = bundleDomainService.saveBundle(validBundle);
//
//        // Then
//        assertThat(event).isNotNull();
//        assertThat(event.getBundle()).isEqualTo(validBundle);
//        assertThat(event.getEventDateTime()).isNotNull();
//        verify(bundleRepository).save(validBundle);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle that fails to save, when saveBundle is called, then exception should
// be thrown")
//    void givenBundleThatFailsToSave_whenSaveBundleCalled_thenExceptionThrown() {
//        // Given
//        when(bundleRepository.save(validBundle)).thenReturn(null);
//
//        // When & Then
//        assertThatThrownBy(() -> bundleDomainService.saveBundle(validBundle))
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessageContaining("Bundle creation failed");
//        verify(bundleRepository).save(validBundle);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID that exists, when findBundleByIdAndStatusNot is called, then bundle
// should be returned")
//    void givenBundleIdThatExists_whenFindBundleByIdAndStatusNotCalled_thenBundleReturned() {
//        // Given
//        when(bundleRepository.findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE))
//                .thenReturn(Optional.of(validBundle));
//
//        // When
//        Bundle result =
//                bundleDomainService.findBundleByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).isEqualTo(validBundle);
//        verify(bundleRepository).findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID that doesn't exist, when findBundleByIdAndStatusNot is called, then
// exception should be thrown")
//    void givenBundleIdThatDoesntExist_whenFindBundleByIdAndStatusNotCalled_thenExceptionThrown() {
//        // Given
//        when(bundleRepository.findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE))
//                .thenReturn(Optional.empty());
//
//        // When & Then
//        assertThatThrownBy(
//                        () ->
//                                bundleDomainService.findBundleByIdAndStatusNot(
//                                        bundleIdObj, BundleStatus.PASSIVE))
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessageContaining("Bundle not found with ID: " + bundleId);
//        verify(bundleRepository).findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a specific status, when findAllBundles is called with status, then filtered
// bundles should be returned")
//    void givenSpecificStatus_whenFindAllBundlesCalledWithStatus_thenFilteredBundlesReturned() {
//        // Given
//        List<Bundle> bundles = Collections.singletonList(validBundle);
//        when(bundleRepository.findAllByStatus(eq(BundleStatus.ACTIVE), eq(1), eq(10)))
//                .thenReturn(bundles);
//
//        // When
//        List<Bundle> result = bundleDomainService.findAllBundles(BundleStatus.ACTIVE, 1, 10);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).hasSize(1);
//        assertThat(result).containsExactly(validBundle);
//        verify(bundleRepository).findAllByStatus(BundleStatus.ACTIVE, 1, 10);
//    }
//
//    @Test
//    @DisplayName(
//            "Given null status, when findAllBundles is called, then all non-PASSIVE bundles should
// be returned")
//    void givenNullStatus_whenFindAllBundlesCalled_thenAllNonPassiveBundlesReturned() {
//        // Given
//        List<Bundle> bundles = Collections.singletonList(validBundle);
//        when(bundleRepository.findAllByStatusNot(eq(BundleStatus.PASSIVE), eq(1), eq(10)))
//                .thenReturn(bundles);
//
//        // When
//        List<Bundle> result = bundleDomainService.findAllBundles(null, 1, 10);
//
//        // Then
//        assertThat(result).isNotNull();
//        assertThat(result).hasSize(1);
//        assertThat(result).containsExactly(validBundle);
//        verify(bundleRepository).findAllByStatusNot(BundleStatus.PASSIVE, 1, 10);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID and updated bundle, when updateBundle is called, then
// BundleUpdatedEvent should be returned")
//    void givenBundleIdAndUpdatedBundle_whenUpdateBundleCalled_thenBundleUpdatedEventReturned() {
//        // Given
//        Bundle updatedBundle =
//                new Bundle.Builder()
//                        .name("Updated Bundle")
//                        .description("Updated Description")
//                        .type(BundleType.STANDALONE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        Bundle existingBundle =
//                new Bundle.Builder()
//                        .id(bundleIdObj)
//                        .name("Original Bundle")
//                        .description("Original Description")
//                        .type(BundleType.FLIGHT_INCLUSIVE)
//                        .status(BundleStatus.ACTIVE)
//                        .build();
//
//        when(bundleRepository.findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE))
//                .thenReturn(Optional.of(existingBundle));
//        when(bundleRepository.save(any(Bundle.class))).thenReturn(existingBundle);
//
//        // When
//        BundleUpdatedEvent event = bundleDomainService.updateBundle(bundleIdObj, updatedBundle);
//
//        // Then
//        assertThat(event).isNotNull();
//        assertThat(event.getBundle()).isEqualTo(existingBundle);
//        assertThat(event.getEventDateTime()).isNotNull();
//
//        // Verify that the existing bundle was updated with new values
//        verify(bundleRepository).findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//        verify(bundleRepository).save(existingBundle);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID, when deleteBundle is called, then BundleDeletedEvent should be
// returned")
//    void givenBundleId_whenDeleteBundleCalled_thenBundleDeletedEventReturned() {
//        // Given
//        when(bundleRepository.findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE))
//                .thenReturn(Optional.of(validBundle));
//        when(bundleRepository.save(any(Bundle.class))).thenReturn(validBundle);
//
//        // When
//        BundleDeletedEvent event = bundleDomainService.deleteBundle(bundleIdObj);
//
//        // Then
//        assertThat(event).isNotNull();
//        assertThat(event.getBundle()).isEqualTo(validBundle);
//        assertThat(event.getEventDateTime()).isNotNull();
//
//        // Verify that the bundle status was set to PASSIVE
//        verify(bundleRepository).findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//        verify(bundleRepository).save(validBundle);
//    }
//
//    @Test
//    @DisplayName(
//            "Given a bundle ID that doesn't exist, when deleteBundle is called, then exception
// should be thrown")
//    void givenBundleIdThatDoesntExist_whenDeleteBundleCalled_thenExceptionThrown() {
//        // Given
//        when(bundleRepository.findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE))
//                .thenReturn(Optional.empty());
//
//        // When & Then
//        assertThatThrownBy(() -> bundleDomainService.deleteBundle(bundleIdObj))
//                .isInstanceOf(BundleDomainException.class)
//                .hasMessageContaining("Bundle not found with ID: " + bundleId);
//
//        verify(bundleRepository).findByIdAndStatusNot(bundleIdObj, BundleStatus.PASSIVE);
//        verify(bundleRepository, never()).save(any());
//    }
// }
