package com.sarp.rule.domain.service;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

import com.sarp.rule.domain.exception.CriteriaDomainException;
import com.sarp.rule.domain.validator.CriteriaDeletionValidator;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class CriteriaDeletionValidatorTest {

    private final CriteriaDeletionValidator criteriaDeletionValidator = new CriteriaDeletionValidator();

    @Test
    @DisplayName("Should throw exception if criteria deletion is not valid")
    void shouldThrowCriteriaDomainException() {
        // Given
        UUID criteriaId = UUID.randomUUID();
        boolean isAssociatedConditionExist = true;

        // When / Then
        assertThatThrownBy(() -> criteriaDeletionValidator.ensureDeletable(criteriaId, isAssociatedConditionExist))
                .isInstanceOf(CriteriaDomainException.class)
                .hasMessageContaining("criteria_has_condition_set");
    }
}
