```json
{
  "ruleType": "BUNDLE",
  "conditionSetGroups": [
    {
      "name": "Rule Engine Testing Rules for BUNDLE",
      "description": "Rules applied for premium developers",
      "conditionSets": [
        {
          "displayOrder": "1",
          "conditionNodes": [
            {
              "condition": {
                "criteriaId": "f4b6d508-f5c9-4a83-b20e-4f208be5f550",
                "operator": "EQUALS",
                "value": "false"
              },
              "nextNodeId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307"
            },
            {
              "condition": {
                "criteriaId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307",
                "operator": "CONTAINS_ANY",
                "value": "[\"TR\", \"US\", \"IST\", \"ESB\"]"
              },
              "nextNodeId": "cb007c3e-82a1-4393-bfbb-8d93d547516b",
              "previousNodeId": "f4b6d508-f5c9-4a83-b20e-4f208be5f550"
            },
            {
              "condition": {
                "criteriaId": "cb007c3e-82a1-4393-bfbb-8d93d547516b",
                "operator": "LESS_OR_EQUAL",
                "value": "2002-10-10"
              },
              "nextNodeId": "aed2f103-4951-417b-8b85-68f55f55b3cc",
              "previousNodeId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307"
            },
            {
              "condition": {
                "criteriaId": "aed2f103-4951-417b-8b85-68f55f55b3cc",
                "operator": "NOT_EQUALS",
                "value": "10"
              },
              "previousNodeId": "cb007c3e-82a1-4393-bfbb-8d93d547516b"
            }
          ],
          "actions": [
            {
              "actionType": "PERCENTAGE_DISCOUNT",
              "parameters": {
                "discountInfo": {
                  "1": {
                    "value": 12.25,
                    "productDiscountInfo": {
                      "1": {
                        "value": 5
                      },
                      "2": {
                        "value": 10
                      }
                    }
                  },
                  "2": {}
                }
              }
            }
          ]
        },
        {
          "displayOrder": "2",
          "conditionNodes": [
            {
              "condition": {
                "criteriaId": "f4b6d508-f5c9-4a83-b20e-4f208be5f550",
                "operator": "NOT_EQUALS",
                "value": "false"
              },
              "nextNodeId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307"
            },
            {
              "condition": {
                "criteriaId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307",
                "operator": "EQUALS",
                "value": "IST"
              },
              "nextNodeId": "cb007c3e-82a1-4393-bfbb-8d93d547516b",
              "previousNodeId": "f4b6d508-f5c9-4a83-b20e-4f208be5f550"
            },
            {
              "condition": {
                "criteriaId": "cb007c3e-82a1-4393-bfbb-8d93d547516b",
                "operator": "GREATER_OR_EQUAL",
                "value": "2002-10-10"
              },
              "nextNodeId": "aed2f103-4951-417b-8b85-68f55f55b3cc",
              "previousNodeId": "38cffbe1-f4cc-4d9b-84c3-b3b85838d307"
            },
            {
              "condition": {
                "criteriaId": "aed2f103-4951-417b-8b85-68f55f55b3cc",
                "operator": "EQUALS",
                "value": "10"
              },
              "previousNodeId": "cb007c3e-82a1-4393-bfbb-8d93d547516b"
            }
          ],
          "actions": [
            {
              "actionType": "PERCENTAGE_DISCOUNT",
              "parameters": {
                "discountInfo": {
                  "1": {
                    "value": 42.25,
                    "productDiscountInfo": {
                      "1": {
                        "value": 5
                      },
                      "2": {
                        "value": 10
                      }
                    }
                  },
                  "2": {}
                }
              }
            }
          ]
        }
      ]
    }
  ],
  "bundles": [
    {
      "name": "Premium Package",
      "description": "Premium bundle with all services included",
      "type": "FLIGHT_INCLUSIVE",
      "status": "ACTIVE",
      "productIds": [
        "38cffbe1-f4cc-4d9b-84c3-b3b85838d307"
      ]
    }
  ],
  "conditionSetGroupConfig": {
    "name": "Sample Rule Template",
    "effectiveDateFrom": "2025-06-03T10:40:37.542Z",
    "effectiveDateTo": "2026-06-03T10:40:37.542Z",
    "priority": 1,
    "type": "BUNDLE",
    "saveAsDraft": false
  }
}
```

criteria grup
INSERT INTO public.criteria_group (id, name, display_no) VALUES ('d20fa18a-4d66-4a9b-a2e3-0be60774eac1', 'Criteria Group Name', 1);


criteria
INSERT INTO public.criteria (id, name, description, type, request_type, mapping_field, field_type, selection_type, allowed_values, min_value, max_value, create_date, criteria_group_id) VALUES ('aed2f103-4951-417b-8b85-68f55f55b3cc', 'Dates Before', 'cool', 'USER_DEFINED', 'OFFER_REQUEST', 'originDestinationsCriteria.calendarDateCriteria.datesBefore', 'INTEGER', null, null, 1, 10000, '2025-06-16 06:26:56.412820', 'd20fa18a-4d66-4a9b-a2e3-0be60774eac1');
INSERT INTO public.criteria (id, name, description, type, request_type, mapping_field, field_type, selection_type, allowed_values, min_value, max_value, create_date, criteria_group_id) VALUES ('38cffbe1-f4cc-4d9b-84c3-b3b85838d307', 'Location', 'cool', 'USER_DEFINED', 'OFFER_REQUEST', 'originDestinationsCriteria.destArrivalCriteria.locationCode', 'TEXT', null, null, null, null, '2025-06-16 06:28:07.819078', 'd20fa18a-4d66-4a9b-a2e3-0be60774eac1');
INSERT INTO public.criteria (id, name, description, type, request_type, mapping_field, field_type, selection_type, allowed_values, min_value, max_value, create_date, criteria_group_id) VALUES ('f4b6d508-f5c9-4a83-b20e-4f208be5f550', 'Premium Customer', 'cool', 'USER_DEFINED', 'OFFER_REQUEST', 'paxList[].isPremium', 'BOOLEAN', null, null, null, null, '2025-06-16 06:31:46.570548', 'd20fa18a-4d66-4a9b-a2e3-0be60774eac1');
INSERT INTO public.criteria (id, name, description, type, request_type, mapping_field, field_type, selection_type, allowed_values, min_value, max_value, create_date, criteria_group_id) VALUES ('cb007c3e-82a1-4393-bfbb-8d93d547516b', 'Birth Date', 'cool', 'USER_DEFINED', 'OFFER_REQUEST', 'paxList[].birthDate', 'DATETIME', null, null, null, null, '2025-06-16 06:32:03.676149', 'd20fa18a-4d66-4a9b-a2e3-0be60774eac1');

