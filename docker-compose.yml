services:
  postgres:
    image: postgres:17
    container_name: postgres
    environment:
      POSTGRES_DB: sarpdb
      POSTGRES_USER: sarpdb
      POSTGRES_PASSWORD: sarpdb123
    ports:
      - "5432:5432"
    volumes:
      - ./docker-config/postgres:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sarpdb -d sarpdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  couchbase:
    build:
      context: ./docker-config/couchbase
      dockerfile: Dockerfile
    container_name: couchbase
    ports:
      - "8091-8096:8091-8096"
      - "11210-11211:11210-11211"
    volumes:
      - ./docker-config/couchbase/couchbase-data:/opt/couchbase/var
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-s", "-u", "admin:sarp123", "http://localhost:8091/pools/default"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  rule-admin-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rule-admin-service
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: local
      SPRING_DATASOURCE_URL: **************************************
      SPRING_DATASOURCE_USERNAME: sarpdb
      SPRING_DATASOURCE_PASSWORD: sarpdb123
      SERVER_PORT: 8080
    volumes:
      - ./docker-config/logs/rule-admin-service:/app/logs

  mockoon:
    image: mockoon/cli:latest
    container_name: mockoon
    ports:
      - "3000:3000"
    volumes:
      - ./docker-config/mockoon/environment.json:/data/environment.json
    command: >
      -d /data/environment.json
      -p 3000

  liquibase:
    container_name: liquibase
    image: liquibase/liquibase:latest
    command: update --changelog-file=db.changelog-master.yml --username=sarpdb --password=sarpdb123 --url=**************************************
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./src/main/resources/db/changelog/db.changelog-master.yml:/liquibase/db.changelog-master.yml
      - ./src/main/resources/db:/liquibase/db
      - ./src/main/resources/db/scripts:/liquibase/db/scripts

  kafka:
    image: bitnami/kafka:4.0.0
    container_name: kafka
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      - KAFKA_KRAFT_CLUSTER_ID=abcdefghijklmnopqrstuv
      - KAFKA_CFG_NODE_ID=1
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092,INTERNAL://kafka:29092
      - KAFKA_CFG_LISTENERS=PLAINTEXT://0.0.0.0:9092,INTERNAL://0.0.0.0:29092,CONTROLLER://0.0.0.0:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,INTERNAL:PLAINTEXT,CONTROLLER:PLAINTEXT
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=INTERNAL
    volumes:
      - ./docker-config/kafka:/bitnami/kafka
    restart: unless-stopped

  redpanda-console:
    image: docker.redpanda.com/redpandadata/console:v2.8.6
    container_name: redpanda-console
    ports:
      - "8082:8080"
    environment:
      - KAFKA_BROKERS=kafka:29092
    depends_on:
      - kafka