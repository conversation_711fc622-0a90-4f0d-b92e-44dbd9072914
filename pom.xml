<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sarp</groupId>
        <artifactId>sarp-parent</artifactId>
        <version>1.0.0-R8</version>
    </parent>

    <artifactId>rule-admin-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>rule-admin-service</name>
    <description>Sarp Rule Admin Service</description>

    <properties>
        <openapi-generator.version>7.12.0</openapi-generator.version>
        <querydsl.version>5.1.0</querydsl.version>
        <java.version>21</java.version>
        <querydsl.plugin.version>1.1.3</querydsl.plugin.version>
        <jib.liquibase.ofms.image>${acr.registry}/ofms/liquibase</jib.liquibase.ofms.image>
        <jib.liquibase.image.tag>latest</jib.liquibase.image.tag>
        <openapi-useBuilder>true</openapi-useBuilder>
        <openapi-delegatePattern>false</openapi-delegatePattern>
        <openapi-interfaceOnly>true</openapi-interfaceOnly>
        <openapi-useSpringBoot3>true</openapi-useSpringBoot3>
        <openapi-documentationProvider>springdoc</openapi-documentationProvider>
        <openapi-openApiNullable>false</openapi-openApiNullable>
        <openapi-skipDefaultInterface>true</openapi-skipDefaultInterface>
        <openapi-useTags>true</openapi-useTags>
        <openapi-useJakartaEe>true</openapi-useJakartaEe>
        <openapi-dateLibrary>java8</openapi-dateLibrary>
        <openapi-useOneOfDiscriminatorLookup>true</openapi-useOneOfDiscriminatorLookup>
        <openapi-legacyDiscriminatorBehavior>false</openapi-legacyDiscriminatorBehavior>
        <openapi-dateTimeType>java.time.LocalDateTime</openapi-dateTimeType>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sarp</groupId>
            <artifactId>sarp-commons</artifactId>
            <version>1.0.0-R3</version>
        </dependency>

        <dependency>
            <groupId>io.github.perplexhub</groupId>
            <artifactId>rsql-querydsl-spring-boot-starter</artifactId>
            <version>6.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <version>${querydsl.version}</version>
            <classifier>jakarta</classifier>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <version>${querydsl.version}</version>
            <classifier>jakarta</classifier>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>4.2.1</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Override the dependency from parent -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-docker-compose</artifactId>
                <version>3.4.4</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <typeMappings>
                                <typeMapping>OffsetDateTime=Instant</typeMapping>
                                <typeMapping>Pageable=org.springframework.data.domain.Pageable</typeMapping>
                            </typeMappings>
                            <schemaMappings>
                                <typeMapping>BaseResponseDTO=com.sarp.core.controller.dto.BaseResponseDTO</typeMapping>
                                <typeMapping>Pageable=org.springframework.data.domain.Pageable</typeMapping>
                            </schemaMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.Instant</importMapping>
                                <importMapping>BaseResponseDTO=com.sarp.core.controller.dto.BaseResponseDTO
                                </importMapping>
                                <importMapping>Pageable=org.springframework.data.domain.Pageable</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-openapi-product</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/openapi/product/specs.yml</inputSpec>
                            <generatorName>spring</generatorName>
                            <library>spring-boot</library>
                            <output>${project.build.directory}/generated-sources/openapi</output>
                            <apiPackage>com.sarp.generated.openapi.product.paths</apiPackage>
                            <modelPackage>com.sarp.generated.openapi.product.dto</modelPackage>
                            <configOptions>
                                <useBuilder>${openapi-useBuilder}</useBuilder>
                                <delegatePattern>${openapi-delegatePattern}</delegatePattern>
                                <interfaceOnly>${openapi-interfaceOnly}</interfaceOnly>
                                <useSpringBoot3>${openapi-useSpringBoot3}</useSpringBoot3>
                                <documentationProvider>${openapi-documentationProvider}</documentationProvider>
                                <openApiNullable>${openapi-openApiNullable}</openApiNullable>
                                <skipDefaultInterface>${openapi-skipDefaultInterface}</skipDefaultInterface>
                                <useTags>${openapi-useTags}</useTags>
                                <useJakartaEe>${openapi-useJakartaEe}</useJakartaEe>
                                <dateLibrary>${openapi-dateLibrary}</dateLibrary>
                                <useOneOfDiscriminatorLookup>${openapi-useOneOfDiscriminatorLookup}
                                </useOneOfDiscriminatorLookup>
                                <legacyDiscriminatorBehavior>${openapi-legacyDiscriminatorBehavior}
                                </legacyDiscriminatorBehavior>
                                <dateTimeType>${openapi-dateTimeType}</dateTimeType>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/openapi</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>${querydsl.plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jib-build-liquibase</id>
                        <phase>none</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <!-- Base image -->
                            <from>
                                <image>liquibase/liquibase:latest</image>
                            </from>
                            <!-- Target image coordinates -->
                            <to>
                                <image>${jib.liquibase.ofms.image}</image>
                                <tags>
                                    <tag>${jib.liquibase.image.tag}</tag>
                                </tags>
                            </to>
                            <!-- Include liquibase changelogs -->
                            <extraDirectories>
                                <paths>
                                    <path>
                                        <from>${project.basedir}/src/main/resources/db</from>
                                        <into>/liquibase/db</into>
                                    </path>
                                </paths>
                            </extraDirectories>
                            <container>
                                <entrypoint>
                                    <entrypointArg>liquibase</entrypointArg>
                                    <entrypointArg>--defaultsFile=db/changelog/db.changelog-master.yml</entrypointArg>
                                    <entrypointArg>update</entrypointArg>
                                </entrypoint>
                            </container>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
